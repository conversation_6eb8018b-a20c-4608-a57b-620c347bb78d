name: Bug Report
description: Create a bug issue for RAGFlow
title: "[Bug]: "
labels: [bug]
body:
- type: checkboxes
  attributes:
    label: Is there an existing issue for the same bug?
    description: Please check if an issue already exists for the bug you encountered.
    options:
    - label: I have checked the existing issues.
      required: true
- type: markdown
  attributes:
    value: "Please provide the following information to help us understand the issue."
- type: input
  attributes:
    label: RAGFlow workspace code commit ID
    description: Enter the commit ID associated with the issue.
    placeholder: e.g., 26d3480e
  validations:
    required: true
- type: input
  attributes:
    label: RAGFlow image version
    description: Enter the image version(shown in RAGFlow UI, `System` page) associated with the issue.
    placeholder: e.g., 26d3480e(v0.13.0~174)
  validations:
    required: true
- type: textarea
  attributes:
    label: Other environment information
    description: |
      Enter the environment details:
      value: |
      - Hardware parameters:
      - OS type:
      - Others:
    render: Markdown
  validations:
    required: false
- type: textarea
  attributes:
    label: Actual behavior
    description: Describe what you encountered.
  validations:
    required: true
- type: textarea
  attributes:
    label: Expected behavior
    description: Describe what you expected.
  validations:
    required: false
- type: textarea
  attributes:
    label: Steps to reproduce
    description: Steps to reproduce what you encountered.
    render: Markdown
  validations:
    required: true
- type: textarea
  attributes:
    label: Additional information
    description: |
      Log, error message, or any other information can help find the root cause.
  validations:
    required: false