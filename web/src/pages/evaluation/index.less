.evaluationPage {
  padding: 16px;
  min-height: 100vh;
  background-color: #f5f5f5;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;

  .ant-card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 100%;
  }

  .ant-tabs-content-holder {
    padding: 16px 0;
    width: 100%;
    max-width: 100%;
  }

  .ant-tabs-content {
    width: 100%;
    max-width: 100%;
  }

  .ant-tabs-tabpane {
    width: 100%;
    max-width: 100%;
  }

  .datasetCard {
    margin-bottom: 16px;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .datasetHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .datasetTitle {
        font-size: 16px;
        font-weight: 600;
        color: #1890ff;
      }

      .datasetActions {
        display: flex;
        gap: 8px;
      }
    }

    .datasetMeta {
      color: #666;
      font-size: 14px;
      margin-bottom: 12px;
    }

    .datasetStats {
      display: flex;
      gap: 16px;

      .statItem {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #999;
        font-size: 12px;
      }
    }
  }

  .taskCard {
    margin-bottom: 16px;
    border-radius: 8px;

    .taskHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .taskTitle {
        font-size: 16px;
        font-weight: 600;
      }

      .taskStatus {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }

    .taskProgress {
      margin-bottom: 12px;
    }

    .taskMeta {
      display: flex;
      justify-content: space-between;
      color: #666;
      font-size: 14px;
    }
  }

  .metricsGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    .metricCard {
      text-align: center;
      padding: 16px;
      border-radius: 8px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      .metricValue {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 4px;
      }

      .metricLabel {
        font-size: 14px;
        opacity: 0.9;
      }
    }
  }

  .chartContainer {
    background: white;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 100%;

    .chartTitle {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #333;
    }
  }

  // 确保所有Ant Design组件都使用全宽
  .ant-row {
    width: 100% !important;
    max-width: 100% !important;
  }

  .ant-col {
    max-width: 100% !important;
  }

  .ant-card-body {
    width: 100% !important;
    max-width: 100% !important;
    padding: 16px !important;
  }

  .ant-tabs {
    width: 100% !important;
    max-width: 100% !important;
  }

  .ant-tabs-content-holder {
    width: 100% !important;
    max-width: 100% !important;
  }

  .ant-tabs-content {
    width: 100% !important;
    max-width: 100% !important;
  }

  .ant-tabs-tabpane {
    width: 100% !important;
    max-width: 100% !important;
  }

  .uploadArea {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;

    &:hover {
      border-color: #1890ff;
      background: #f0f8ff;
    }

    .uploadIcon {
      font-size: 48px;
      color: #d9d9d9;
      margin-bottom: 16px;
    }

    .uploadText {
      font-size: 16px;
      color: #666;
      margin-bottom: 8px;
    }

    .uploadHint {
      font-size: 14px;
      color: #999;
    }
  }

  .filterBar {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    padding: 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .filterItem {
      flex: 1;
      max-width: 200px;
    }
  }

  .actionBar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    .actionLeft {
      display: flex;
      gap: 12px;
    }

    .actionRight {
      display: flex;
      gap: 12px;
    }
  }

  .emptyState {
    text-align: center;
    padding: 60px 20px;
    color: #999;

    .emptyIcon {
      font-size: 64px;
      margin-bottom: 16px;
      color: #d9d9d9;
    }

    .emptyText {
      font-size: 16px;
      margin-bottom: 8px;
    }

    .emptyHint {
      font-size: 14px;
      color: #ccc;
    }
  }
}
