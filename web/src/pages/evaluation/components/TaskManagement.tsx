import { useFetchKnowledgeList } from '@/hooks/knowledge-hooks';
import evaluationService, {
  EvaluationDataset,
  EvaluationTask,
  LLMConfig,
} from '@/services/evaluation-service';
import {
  DeleteOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  PlusOutlined,
  ReloadOutlined,
  StopOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Checkbox,
  Collapse,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Progress,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import React, { useEffect, useState } from 'react';

const { TextArea } = Input;
const { Option } = Select;

interface TaskFormValues {
  name: string;
  description: string;
  dataset_id: string;
  kb_id: string;
  metrics: string[];
  llm_id: string;
  llm_config: LLMConfig;
}

interface TaskManagementProps {
  onTabChange?: (key: string) => void;
}

const AVAILABLE_METRICS = [
  { value: 'faithfulness', label: '忠实度' },
  { value: 'answer_relevancy', label: '答案相关性' },
  { value: 'context_precision', label: '上下文精确度' },
  { value: 'context_recall', label: '上下文召回率' },
  { value: 'context_relevancy', label: '上下文相关性' },
  { value: 'answer_correctness', label: '答案正确性' },
  { value: 'answer_similarity', label: '答案相似性' },
];

const TaskManagement: React.FC<TaskManagementProps> = ({ onTabChange }) => {
  const [tasks, setTasks] = useState<EvaluationTask[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [datasets, setDatasets] = useState<EvaluationDataset[]>([]);
  const [availableLLMs, setAvailableLLMs] = useState<any[]>([]);
  const [selectedKbId, setSelectedKbId] = useState<string>('');

  // 使用实际的知识库列表Hook
  const { list: knowledgeList, loading: knowledgeListLoading } =
    useFetchKnowledgeList();

  // 获取知识库列表
  const knowledgeBasesMemo = React.useMemo(() => {
    if (!knowledgeList) return [];

    return knowledgeList.map((kb: any) => ({
      id: kb.id,
      name: kb.name,
      description: kb.description,
    }));
  }, [knowledgeList]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'default';
      case 'running':
        return 'processing';
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'stopped':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '等待中';
      case 'running':
        return '运行中';
      case 'completed':
        return '已完成';
      case 'failed':
        return '失败';
      case 'stopped':
        return '已停止';
      default:
        return status;
    }
  };

  const columns: ColumnsType<EvaluationTask> = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '数据集',
      dataIndex: 'dataset_id',
      key: 'dataset_id',
      render: (dataset_id: string) => {
        const dataset = datasets.find((d) => d.id === dataset_id);
        return dataset ? dataset.name : dataset_id;
      },
    },
    {
      title: '知识库',
      dataIndex: 'kb_id',
      key: 'kb_id',
      render: (kb_id: string) => {
        const kb = knowledgeList.find((k: any) => k.id === kb_id);
        return kb ? kb.name : kb_id;
      },
    },
    {
      title: '评估指标',
      dataIndex: 'metrics',
      key: 'metrics',
      render: (metrics: string[]) => (
        <div>
          {metrics?.map((metric) => {
            const metricInfo = AVAILABLE_METRICS.find(
              (m) => m.value === metric,
            );
            return (
              <Tag key={metric} color="blue" style={{ marginBottom: 4 }}>
                {metricInfo ? metricInfo.label.split(' ')[0] : metric}
              </Tag>
            );
          })}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
      ),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number, record: EvaluationTask) => (
        <Progress
          percent={progress || 0}
          size="small"
          status={record.status === 'failed' ? 'exception' : undefined}
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'create_date',
      key: 'create_date',
      render: (date: string) => (date ? new Date(date).toLocaleString() : '-'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: EvaluationTask) => (
        <Space>
          {record.status === 'pending' && (
            <Button
              type="link"
              icon={<PlayCircleOutlined />}
              onClick={() => handleStartTask(record.id!)}
            >
              开始
            </Button>
          )}
          {record.status === 'running' && (
            <Popconfirm
              title="确定停止这个任务吗？"
              onConfirm={() => handleStopTask(record.id!)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger icon={<StopOutlined />}>
                停止
              </Button>
            </Popconfirm>
          )}
          {record.status === 'completed' && (
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleViewResults(record)}
            >
              查看结果
            </Button>
          )}
          {(record.status === 'completed' ||
            record.status === 'failed' ||
            record.status === 'stopped') && (
            <Popconfirm
              title="确定重新运行这个任务吗？"
              description="将清除之前的评估结果并重新开始"
              onConfirm={() => handleRestartTask(record.id!)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" icon={<ReloadOutlined />}>
                重新运行
              </Button>
            </Popconfirm>
          )}
          <Popconfirm
            title="确定删除这个任务吗？"
            description="删除后无法恢复"
            onConfirm={() => handleDeleteTask(record.id!)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const handleCreateTask = async (values: TaskFormValues) => {
    try {
      // 过滤掉值为null、undefined或空的llm_config参数
      const llm_config = values.llm_config
        ? Object.fromEntries(
            Object.entries(values.llm_config).filter(
              ([_, value]) =>
                value !== null && value !== undefined && value !== '',
            ),
          )
        : {};

      const taskData = {
        ...values,
        llm_config: Object.keys(llm_config).length > 0 ? llm_config : undefined,
      };

      const response = await evaluationService.createTask(taskData);
      if (response.data?.code === 0) {
        message.success('任务创建成功');
        fetchTasks();
        setModalVisible(false);
        form.resetFields();
      } else {
        message.error(response.data?.message || '创建失败');
      }
    } catch (error) {
      console.error('创建任务失败:', error);
      message.error('创建失败');
    }
  };

  const handleStartTask = async (taskId: string) => {
    try {
      const response = await evaluationService.startTask(taskId);
      if (response.data?.code === 0) {
        message.success('任务已开始');
        fetchTasks();
      } else {
        message.error(response.data?.message || '启动失败');
      }
    } catch (error) {
      console.error('启动任务失败:', error);
      message.error('启动失败');
    }
  };

  const handleStopTask = async (taskId: string) => {
    try {
      const response = await evaluationService.stopTask(taskId);
      if (response.data?.code === 0) {
        message.success('任务已停止');
        fetchTasks();
      } else {
        message.error(response.data?.message || '停止失败');
      }
    } catch (error) {
      console.error('停止任务失败:', error);
      message.error('停止失败');
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    try {
      const response = await evaluationService.deleteTask(taskId);
      if (response.data?.code === 0) {
        message.success('任务删除成功');
        fetchTasks();
      } else {
        message.error(response.data?.message || '删除失败');
      }
    } catch (error) {
      console.error('删除任务失败:', error);
      message.error('删除失败');
    }
  };

  const handleRestartTask = async (taskId: string) => {
    try {
      const response = await evaluationService.restartTask(taskId);
      if (response.data?.code === 0) {
        message.success('任务已重新开始运行');
        fetchTasks();
      } else {
        message.error(response.data?.message || '重新运行失败');
      }
    } catch (error) {
      console.error('重新运行任务失败:', error);
      message.error('重新运行失败');
    }
  };

  const handleViewResults = (task: EvaluationTask) => {
    // 切换到结果分析标签页
    if (onTabChange) {
      onTabChange('results');
    } else {
      // 如果没有传递onTabChange，则显示消息
      message.success('正在跳转到结果分析页面...');
      // 可以使用路由跳转或其他方式
      window.location.hash = '#results';
    }
  };

  const fetchTasks = async () => {
    setLoading(true);
    try {
      const response = await evaluationService.getTasks();
      if (response.data?.code === 0) {
        setTasks(response.data.data || []);
      } else {
        message.error('获取任务列表失败');
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchDatasets = async () => {
    try {
      const response = await evaluationService.getDatasets();
      if (response.data?.code === 0) {
        setDatasets(response.data.data || []);
      }
    } catch (error) {
      console.error('获取数据集列表失败:', error);
    }
  };

  const fetchAvailableLLMs = async (kb_id?: string) => {
    try {
      const response = await evaluationService.getAvailableLLMs(kb_id);
      if (response.data?.code === 0) {
        setAvailableLLMs(response.data.data?.llms || []);
      }
    } catch (error) {
      console.error('获取可用LLM模型失败:', error);
    }
  };

  // 处理知识库选择变化
  const handleKbChange = (kb_id: string) => {
    setSelectedKbId(kb_id);
    // 清空之前选择的LLM模型
    form.setFieldsValue({ llm_id: undefined });
    // 获取该知识库可用的LLM模型
    if (kb_id) {
      fetchAvailableLLMs(kb_id);
    } else {
      setAvailableLLMs([]);
    }
  };

  useEffect(() => {
    fetchTasks();
    fetchDatasets();

    // 设置定时器，每5秒刷新一次任务状态
    const timer = setInterval(fetchTasks, 5000);
    return () => clearInterval(timer);
  }, []);

  return (
    <div className="task-management">
      <Card
        title="评估任务管理"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setModalVisible(true)}
          >
            创建任务
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={tasks}
          loading={loading}
          rowKey="id"
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
        />
      </Card>

      <Modal
        title="创建评估任务"
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateTask}>
          <Form.Item
            name="name"
            label="任务名称"
            rules={[{ required: true, message: '请输入任务名称' }]}
          >
            <Input placeholder="请输入任务名称" />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <TextArea rows={3} placeholder="请输入任务描述" />
          </Form.Item>

          <Form.Item
            name="dataset_id"
            label="评估数据集"
            rules={[{ required: true, message: '请选择评估数据集' }]}
          >
            <Select placeholder="请选择数据集">
              {datasets.map((ds) => (
                <Option key={ds.id} value={ds.id}>
                  {ds.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="kb_id"
            label="目标知识库"
            rules={[{ required: true, message: '请选择目标知识库' }]}
          >
            <Select placeholder="请选择知识库" onChange={handleKbChange}>
              {knowledgeBasesMemo.map((kb) => (
                <Option key={kb.id} value={kb.id}>
                  {kb.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="llm_id"
            label="评估模型"
            rules={[{ required: true, message: '请选择用于评估的LLM模型' }]}
            tooltip="用于生成答案和执行Ragas评估的语言模型。Embedding模型将自动使用知识库的配置。"
          >
            <Select
              placeholder={selectedKbId ? '请选择LLM模型' : '请先选择知识库'}
              disabled={!selectedKbId}
              loading={!selectedKbId && availableLLMs.length === 0}
            >
              {availableLLMs.map((llm) => (
                <Option key={llm.id} value={llm.id}>
                  {llm.llm_name}
                  {llm.llm_factory && (
                    <span style={{ color: '#666', fontSize: '12px' }}>
                      {' '}
                      ({llm.llm_factory})
                    </span>
                  )}
                  {llm.api_base && (
                    <span style={{ color: '#999', fontSize: '11px' }}>
                      {' '}
                      - {llm.api_base}
                    </span>
                  )}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="metrics"
            label="评估指标"
            rules={[{ required: true, message: '请选择至少一个评估指标' }]}
          >
            <Checkbox.Group>
              {AVAILABLE_METRICS.map((metric) => (
                <Checkbox key={metric.value} value={metric.value}>
                  {metric.label}
                </Checkbox>
              ))}
            </Checkbox.Group>
          </Form.Item>

          <Collapse
            size="small"
            items={[
              {
                key: 'llm_config',
                label: 'LLM生成参数配置（可选）',
                children: (
                  <div style={{ padding: '8px 0' }}>
                    <Form.Item
                      name={['llm_config', 'temperature']}
                      label={
                        <Tooltip title="控制输出的随机性，范围0.0-2.0，越低越确定">
                          温度 (Temperature)
                        </Tooltip>
                      }
                      initialValue={0.1}
                    >
                      <InputNumber
                        min={0}
                        max={2}
                        step={0.1}
                        placeholder="0.1"
                        style={{ width: '100%' }}
                      />
                    </Form.Item>

                    <Form.Item
                      name={['llm_config', 'max_tokens']}
                      label={
                        <Tooltip title="生成的最大token数量">
                          最大Tokens
                        </Tooltip>
                      }
                      initialValue={1024}
                    >
                      <InputNumber
                        min={1}
                        max={8192}
                        step={1}
                        placeholder="1024"
                        style={{ width: '100%' }}
                      />
                    </Form.Item>

                    <Form.Item
                      name={['llm_config', 'top_p']}
                      label={
                        <Tooltip title="核采样参数，控制词汇选择的多样性，范围0.0-1.0">
                          Top P
                        </Tooltip>
                      }
                      initialValue={0.9}
                    >
                      <InputNumber
                        min={0}
                        max={1}
                        step={0.1}
                        placeholder="0.9"
                        style={{ width: '100%' }}
                      />
                    </Form.Item>

                    <Form.Item
                      name={['llm_config', 'presence_penalty']}
                      label={
                        <Tooltip title="惩罚已出现的主题，范围-2.0到2.0">
                          存在惩罚 (Presence Penalty)
                        </Tooltip>
                      }
                      initialValue={0.0}
                    >
                      <InputNumber
                        min={-2}
                        max={2}
                        step={0.1}
                        placeholder="0.0"
                        style={{ width: '100%' }}
                      />
                    </Form.Item>

                    <Form.Item
                      name={['llm_config', 'frequency_penalty']}
                      label={
                        <Tooltip title="惩罚重复的词汇，范围-2.0到2.0">
                          频率惩罚 (Frequency Penalty)
                        </Tooltip>
                      }
                      initialValue={0.0}
                    >
                      <InputNumber
                        min={-2}
                        max={2}
                        step={0.1}
                        placeholder="0.0"
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </div>
                ),
              },
            ]}
          />

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                创建
              </Button>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  form.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TaskManagement;
