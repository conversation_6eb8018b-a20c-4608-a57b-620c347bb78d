import evaluationService, {
  EvaluationTask,
} from '@/services/evaluation-service';
import {
  <PERSON><PERSON>,
  Card,
  Col,
  Empty,
  Row,
  Select,
  Space,
  Spin,
  Statistic,
  Tabs,
  message,
} from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import {
  Bar,
  BarChart,
  CartesianGrid,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

const { Option } = Select;
const { TabPane } = Tabs;

interface TaskResult {
  task_id: string;
  task_name: string;
  status: string;
  metrics: Record<string, number>;
  details?: any;
  create_date: string;
  samples_count: number;
}

const ResultsView: React.FC = () => {
  const [tasks, setTasks] = useState<EvaluationTask[]>([]);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
  const [taskResult, setTaskResult] = useState<TaskResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [resultsLoading, setResultsLoading] = useState(false);

  // 指标名称映射
  const metricNames: Record<string, string> = {
    faithfulness: '忠实度',
    answer_relevancy: '答案相关性',
    context_precision: '上下文精确度',
    context_recall: '上下文召回率',
    context_relevancy: '上下文相关性',
    answer_correctness: '答案正确性',
    answer_similarity: '答案相似性',
  };

  // 获取已完成的任务列表
  const fetchCompletedTasks = async () => {
    setLoading(true);
    try {
      const response = await evaluationService.getTasks();
      if (response.data?.code === 0) {
        const completedTasks = (response.data.data || []).filter(
          (task: EvaluationTask) => task.status === 'completed',
        );
        setTasks(completedTasks);

        // 自动选择第一个任务
        if (completedTasks.length > 0 && !selectedTaskId) {
          setSelectedTaskId(completedTasks[0].id!);
        }
      } else {
        message.error('获取任务列表失败');
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取任务结果
  const fetchTaskResults = async (taskId: string) => {
    setResultsLoading(true);
    try {
      const response = await evaluationService.getTaskResults(taskId);
      if (response.data?.code === 0) {
        const data = response.data.data;
        const task = tasks.find((t) => t.id === taskId);

        setTaskResult({
          task_id: taskId,
          task_name: task?.name || 'Unknown Task',
          status: task?.status || 'unknown',
          metrics: data.metrics || {},
          details: data.details,
          create_date: task?.create_date || '',
          samples_count: data.samples_count || 0,
        });
      } else {
        message.error('获取结果失败');
        setTaskResult(null);
      }
    } catch (error) {
      console.error('获取任务结果失败:', error);
      message.error('获取任务结果失败');
      setTaskResult(null);
    } finally {
      setResultsLoading(false);
    }
  };

  // 手动刷新数据
  const handleRefresh = async () => {
    message.info('正在刷新数据...');
    await fetchCompletedTasks();
    if (selectedTaskId) {
      await fetchTaskResults(selectedTaskId);
    }
    message.success('数据刷新完成');
  };

  useEffect(() => {
    fetchCompletedTasks();

    // 设置定时器，每5秒刷新一次任务列表和结果
    const timer = setInterval(() => {
      fetchCompletedTasks();
      if (selectedTaskId) {
        fetchTaskResults(selectedTaskId);
      }
    }, 5000);

    return () => clearInterval(timer);
  }, [selectedTaskId]);

  useEffect(() => {
    if (selectedTaskId) {
      fetchTaskResults(selectedTaskId);
    }
  }, [selectedTaskId]);

  // 将指标数据转换为图表格式
  const getMetricsChartData = () => {
    if (!taskResult?.metrics) return [];

    return Object.entries(taskResult.metrics).map(([key, value]) => ({
      name: metricNames[key] || key,
      value: Number((value * 100).toFixed(2)),
      fullName: `${metricNames[key] || key} (${(value * 100).toFixed(2)}%)`,
    }));
  };

  // 雷达图数据
  const getRadarData = () => {
    if (!taskResult?.metrics) return [];

    return Object.entries(taskResult.metrics).map(([key, value]) => ({
      metric: metricNames[key] || key,
      value: Number((value * 100).toFixed(1)),
      fullValue: value,
    }));
  };

  const renderMetricsOverview = () => {
    if (!taskResult?.metrics) return null;

    return (
      <Row gutter={[16, 16]}>
        {Object.entries(taskResult.metrics).map(([key, value]) => (
          <Col span={6} key={key}>
            <Card size="small">
              <Statistic
                title={metricNames[key] || key}
                value={value}
                precision={3}
                suffix={
                  <span style={{ fontSize: '12px', color: '#666' }}>
                    ({(value * 100).toFixed(1)}%)
                  </span>
                }
                valueStyle={{
                  color:
                    value > 0.7
                      ? '#3f8600'
                      : value > 0.5
                        ? '#cf1322'
                        : '#722ed1',
                }}
              />
            </Card>
          </Col>
        ))}
      </Row>
    );
  };

  const renderCharts = () => {
    const chartData = getMetricsChartData();
    const radarData = getRadarData();

    if (chartData.length === 0) return null;

    return (
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title="指标对比" size="small">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="name"
                  angle={-45}
                  textAnchor="end"
                  height={100}
                  fontSize={12}
                />
                <YAxis domain={[0, 100]} />
                <Tooltip formatter={(value) => [`${value}%`, '得分']} />
                <Bar dataKey="value" fill="#1890ff" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="指标雷达图" size="small">
            <ResponsiveContainer width="100%" height={300}>
              <RadarChart data={radarData}>
                <PolarGrid />
                <PolarAngleAxis dataKey="metric" fontSize={12} />
                <PolarRadiusAxis angle={0} domain={[0, 100]} tickCount={6} />
                <Radar
                  name="评估指标"
                  dataKey="value"
                  stroke="#1890ff"
                  fill="#1890ff"
                  fillOpacity={0.6}
                />
                <Tooltip formatter={(value) => [`${value}%`, '得分']} />
              </RadarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>
    );
  };

  const renderTaskInfo = () => {
    if (!taskResult) return null;

    return (
      <Card title="任务信息" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Statistic title="任务名称" value={taskResult.task_name} />
          </Col>
          <Col span={6}>
            <Statistic
              title="状态"
              value="已完成"
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col span={6}>
            <Statistic title="样本数量" value={taskResult.samples_count} />
          </Col>
          <Col span={6}>
            <Statistic
              title="创建时间"
              value={
                taskResult.create_date
                  ? new Date(taskResult.create_date).toLocaleDateString()
                  : '-'
              }
            />
          </Col>
        </Row>
      </Card>
    );
  };

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Select
              placeholder="选择要查看的评估任务"
              style={{ width: 300 }}
              value={selectedTaskId}
              onChange={setSelectedTaskId}
              loading={loading}
            >
              {tasks.map((task) => (
                <Option key={task.id} value={task.id!}>
                  {task.name} -{' '}
                  {task.create_date
                    ? new Date(task.create_date).toLocaleDateString()
                    : ''}
                </Option>
              ))}
            </Select>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading || resultsLoading}
              title="刷新数据"
            >
              刷新
            </Button>
          </Space>
        </div>

        {resultsLoading ? (
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>加载结果中...</div>
          </div>
        ) : taskResult ? (
          <div>
            {renderTaskInfo()}

            <Tabs defaultActiveKey="overview">
              <TabPane tab="指标概览" key="overview">
                {renderMetricsOverview()}
              </TabPane>

              <TabPane tab="可视化分析" key="charts">
                {renderCharts()}
              </TabPane>

              <TabPane tab="详细报告" key="details">
                <Card>
                  <div style={{ textAlign: 'center', padding: '50px 0' }}>
                    <Empty
                      description="详细报告功能正在开发中"
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                    />
                  </div>
                </Card>
              </TabPane>
            </Tabs>
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            {tasks.length === 0 ? (
              <Empty
                description="暂无已完成的评估任务"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ) : (
              <Empty
                description="请选择一个任务查看结果"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )}
          </div>
        )}
      </Card>
    </div>
  );
};

export default ResultsView;
