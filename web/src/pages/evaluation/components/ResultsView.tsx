import evaluationService, {
  EvaluationTask,
} from '@/services/evaluation-service';
import {
  <PERSON><PERSON>,
  Card,
  Col,
  Empty,
  Row,
  Select,
  Space,
  Spin,
  Statistic,
  Tabs,
  message,
} from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import {
  Bar,
  BarChart,
  CartesianGrid,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

const { Option } = Select;
const { TabPane } = Tabs;

interface TaskResult {
  task_id: string;
  task_name: string;
  status: string;
  metrics: Record<string, number>;
  details?: any;
  create_date: string;
  samples_count: number;
}

const ResultsView: React.FC = () => {
  const [tasks, setTasks] = useState<EvaluationTask[]>([]);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
  const [taskResult, setTaskResult] = useState<TaskResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [resultsLoading, setResultsLoading] = useState(false);

  // 指标名称映射
  const metricNames: Record<string, string> = {
    faithfulness: '忠实度',
    answer_relevancy: '答案相关性',
    context_precision: '上下文精确度',
    context_recall: '上下文召回率',
    context_relevancy: '上下文相关性',
    answer_correctness: '答案正确性',
    answer_similarity: '答案相似性',
  };

  // 获取已完成的任务列表
  const fetchCompletedTasks = async () => {
    setLoading(true);
    try {
      const response = await evaluationService.getTasks();
      if (response.data?.code === 0) {
        const completedTasks = (response.data.data || []).filter(
          (task: EvaluationTask) => task.status === 'completed',
        );
        setTasks(completedTasks);

        // 自动选择第一个任务
        if (completedTasks.length > 0 && !selectedTaskId) {
          setSelectedTaskId(completedTasks[0].id!);
        }
      } else {
        message.error('获取任务列表失败');
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取任务结果
  const fetchTaskResults = async (taskId: string) => {
    setResultsLoading(true);
    try {
      const response = await evaluationService.getTaskResults(taskId);
      if (response.data?.code === 0) {
        const data = response.data.data;
        const task = tasks.find((t) => t.id === taskId);

        setTaskResult({
          task_id: taskId,
          task_name: task?.name || 'Unknown Task',
          status: task?.status || 'unknown',
          metrics: data.metrics || {},
          details: data.details,
          create_date: task?.create_date || '',
          samples_count: data.samples_count || 0,
        });
      } else {
        message.error('获取结果失败');
        setTaskResult(null);
      }
    } catch (error) {
      console.error('获取任务结果失败:', error);
      message.error('获取任务结果失败');
      setTaskResult(null);
    } finally {
      setResultsLoading(false);
    }
  };

  // 手动刷新数据
  const handleRefresh = async () => {
    message.info('正在刷新数据...');
    await fetchCompletedTasks();
    if (selectedTaskId) {
      await fetchTaskResults(selectedTaskId);
    }
    message.success('数据刷新完成');
  };

  useEffect(() => {
    fetchCompletedTasks();
  }, []);

  useEffect(() => {
    if (selectedTaskId) {
      fetchTaskResults(selectedTaskId);
    }
  }, [selectedTaskId]);

  // 将指标数据转换为图表格式
  const getMetricsChartData = () => {
    if (!taskResult?.metrics) return [];

    return Object.entries(taskResult.metrics)
      .filter(([key]) => !key.includes('_time') && !key.includes('_samples')) // 过滤掉时间和样本数指标
      .map(([key, value]) => ({
        name: metricNames[key] || key,
        value: Number((value * 100).toFixed(1)),
        originalValue: value,
        fullName: `${metricNames[key] || key} (${(value * 100).toFixed(1)}%)`,
      }));
  };

  // 雷达图数据
  const getRadarData = () => {
    if (!taskResult?.metrics) return [];

    return Object.entries(taskResult.metrics)
      .filter(([key]) => !key.includes('_time') && !key.includes('_samples')) // 过滤掉时间和样本数指标
      .map(([key, value]) => ({
        metric: metricNames[key] || key,
        value: Number((value * 100).toFixed(1)),
        fullValue: value,
      }));
  };

  const renderMetricsOverview = () => {
    if (!taskResult?.metrics) return null;

    // 分离不同类型的指标
    const qualityMetrics = Object.entries(taskResult.metrics).filter(
      ([key]) => !key.includes('_time') && !key.includes('_samples') && !key.includes('failed_') && !key.includes('successful_') && !key.includes('total_')
    );

    const performanceMetrics = Object.entries(taskResult.metrics).filter(
      ([key]) => key.includes('_time')
    );

    const countMetrics = Object.entries(taskResult.metrics).filter(
      ([key]) => key.includes('_samples')
    );

    return (
      <div>
        {/* 质量指标 */}
        <h4 style={{ marginBottom: 16 }}>质量指标</h4>
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          {qualityMetrics.map(([key, value]) => (
            <Col span={6} key={key}>
              <Card size="small">
                <Statistic
                  title={metricNames[key] || key}
                  value={value}
                  precision={3}
                  suffix={
                    <span style={{ fontSize: '12px', color: '#666' }}>
                      ({(value * 100).toFixed(1)}%)
                    </span>
                  }
                  valueStyle={{
                    color:
                      value > 0.7
                        ? '#3f8600'
                        : value > 0.5
                          ? '#cf1322'
                          : '#722ed1',
                  }}
                />
              </Card>
            </Col>
          ))}
        </Row>

        {/* 性能指标 */}
        {performanceMetrics.length > 0 && (
          <>
            <h4 style={{ marginBottom: 16 }}>性能指标</h4>
            <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
              {performanceMetrics.map(([key, value]) => (
                <Col span={6} key={key}>
                  <Card size="small">
                    <Statistic
                      title={metricNames[key] || key.replace('avg_', '').replace('_', ' ')}
                      value={value}
                      precision={2}
                      suffix="秒"
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          </>
        )}

        {/* 统计指标 */}
        {countMetrics.length > 0 && (
          <>
            <h4 style={{ marginBottom: 16 }}>统计信息</h4>
            <Row gutter={[16, 16]}>
              {countMetrics.map(([key, value]) => (
                <Col span={6} key={key}>
                  <Card size="small">
                    <Statistic
                      title={metricNames[key] || key.replace('_', ' ')}
                      value={value}
                      precision={0}
                      suffix="个"
                      valueStyle={{ color: '#722ed1' }}
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          </>
        )}
      </div>
    );
  };

  const renderCharts = () => {
    const chartData = getMetricsChartData();
    const radarData = getRadarData();

    if (chartData.length === 0) return null;

    return (
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title="指标对比" size="small">
            <ResponsiveContainer width="100%" height={350}>
              <BarChart data={chartData} margin={{ top: 20, right: 30, bottom: 80, left: 20 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="name"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  fontSize={10}
                  tick={{ fontSize: 10, fill: '#666' }}
                />
                <YAxis
                  domain={[0, 100]}
                  tick={{ fontSize: 10, fill: '#666' }}
                  label={{ value: '得分 (%)', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip
                  formatter={(value) => [`${value}%`, '得分']}
                  labelFormatter={(label) => `指标: ${label}`}
                />
                <Bar
                  dataKey="value"
                  fill="#1890ff"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="指标雷达图" size="small">
            <ResponsiveContainer width="100%" height={350}>
              <RadarChart data={radarData} margin={{ top: 20, right: 30, bottom: 20, left: 30 }}>
                <PolarGrid />
                <PolarAngleAxis
                  dataKey="metric"
                  fontSize={10}
                  tick={{ fontSize: 10, fill: '#666' }}
                />
                <PolarRadiusAxis
                  angle={0}
                  domain={[0, 100]}
                  tickCount={5}
                  tick={{ fontSize: 8, fill: '#999' }}
                />
                <Radar
                  name="评估指标"
                  dataKey="value"
                  stroke="#1890ff"
                  fill="#1890ff"
                  fillOpacity={0.3}
                  strokeWidth={2}
                />
                <Tooltip
                  formatter={(value) => [`${value}%`, '得分']}
                  labelFormatter={(label) => `指标: ${label}`}
                />
              </RadarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>
    );
  };

  const renderDetailedReport = () => {
    if (!taskResult?.metrics) return null;

    // 生成评估报告
    const generateReport = () => {
      const qualityMetrics = Object.entries(taskResult.metrics).filter(
        ([key]) => !key.includes('_time') && !key.includes('_samples') && !key.includes('failed_') && !key.includes('successful_') && !key.includes('total_')
      );

      const avgScore = qualityMetrics.reduce((sum, [, value]) => sum + value, 0) / qualityMetrics.length;

      const getScoreLevel = (score: number) => {
        if (score >= 0.8) return { level: '优秀', color: '#52c41a' };
        if (score >= 0.6) return { level: '良好', color: '#1890ff' };
        if (score >= 0.4) return { level: '中等', color: '#faad14' };
        return { level: '需要改进', color: '#f5222d' };
      };

      const overallLevel = getScoreLevel(avgScore);

      return {
        avgScore,
        overallLevel,
        qualityMetrics,
        getScoreLevel
      };
    };

    const report = generateReport();

    return (
      <div>
        {/* 总体评估 */}
        <Card title="总体评估" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Statistic
                title="平均得分"
                value={report.avgScore}
                precision={3}
                suffix={
                  <span style={{ fontSize: '12px', color: '#666' }}>
                    ({(report.avgScore * 100).toFixed(1)}%)
                  </span>
                }
                valueStyle={{ color: report.overallLevel.color }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="评估等级"
                value={report.overallLevel.level}
                valueStyle={{ color: report.overallLevel.color }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="样本数量"
                value={taskResult.samples_count}
                suffix="个"
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
          </Row>
        </Card>

        {/* 详细指标分析 */}
        <Card title="详细指标分析" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]}>
            {report.qualityMetrics.map(([key, value]) => {
              const level = report.getScoreLevel(value);
              return (
                <Col span={12} key={key}>
                  <Card size="small" style={{ height: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                      <strong>{metricNames[key] || key}</strong>
                      <span style={{ color: level.color, fontWeight: 'bold' }}>{level.level}</span>
                    </div>
                    <div style={{ marginBottom: 8 }}>
                      <span style={{ fontSize: '24px', fontWeight: 'bold', color: level.color }}>
                        {(value * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      原始值: {value.toFixed(4)}
                    </div>
                  </Card>
                </Col>
              );
            })}
          </Row>
        </Card>

        {/* 改进建议 */}
        <Card title="改进建议" size="small">
          <div style={{ lineHeight: '1.6' }}>
            {report.qualityMetrics.map(([key, value]) => {
              if (value < 0.6) {
                const suggestions = {
                  faithfulness: '建议优化检索质量，确保检索到的文档与问题高度相关',
                  answer_relevancy: '建议改进答案生成逻辑，确保答案直接回答用户问题',
                  context_precision: '建议优化检索算法，提高相关文档的排序准确性',
                  context_recall: '建议扩大检索范围或优化索引，确保检索到所有相关信息',
                  context_relevancy: '建议改进文档分块和索引策略，提高检索精度',
                  answer_correctness: '建议优化LLM模型参数或提示词，提高答案准确性',
                  answer_similarity: '建议检查答案生成的一致性和稳定性'
                };

                return (
                  <div key={key} style={{ marginBottom: 12, padding: 12, backgroundColor: '#fff2e8', borderRadius: 4 }}>
                    <div style={{ fontWeight: 'bold', color: '#fa8c16', marginBottom: 4 }}>
                      {metricNames[key] || key} ({(value * 100).toFixed(1)}%)
                    </div>
                    <div style={{ color: '#666' }}>
                      {suggestions[key as keyof typeof suggestions] || '建议进一步分析该指标的具体问题'}
                    </div>
                  </div>
                );
              }
              return null;
            }).filter(Boolean).length === 0 && (
              <div style={{ textAlign: 'center', color: '#52c41a', fontSize: '16px' }}>
                🎉 所有指标表现良好，无需特别改进！
              </div>
            )}
          </div>
        </Card>
      </div>
    );
  };

  const renderTaskInfo = () => {
    if (!taskResult) return null;

    return (
      <Card title="任务信息" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Statistic title="任务名称" value={taskResult.task_name} />
          </Col>
          <Col span={6}>
            <Statistic
              title="状态"
              value="已完成"
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col span={6}>
            <Statistic title="样本数量" value={taskResult.samples_count} />
          </Col>
          <Col span={6}>
            <Statistic
              title="创建时间"
              value={
                taskResult.create_date
                  ? new Date(taskResult.create_date).toLocaleDateString()
                  : '-'
              }
            />
          </Col>
        </Row>
      </Card>
    );
  };

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Select
              placeholder="选择要查看的评估任务"
              style={{ width: 300 }}
              value={selectedTaskId}
              onChange={setSelectedTaskId}
              loading={loading}
            >
              {tasks.map((task) => (
                <Option key={task.id} value={task.id!}>
                  {task.name} -{' '}
                  {task.create_date
                    ? new Date(task.create_date).toLocaleDateString()
                    : ''}
                </Option>
              ))}
            </Select>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading || resultsLoading}
              title="刷新数据"
            >
              刷新
            </Button>
          </Space>
        </div>

        {resultsLoading ? (
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>加载结果中...</div>
          </div>
        ) : taskResult ? (
          <div>
            {renderTaskInfo()}

            <Tabs defaultActiveKey="overview">
              <TabPane tab="指标概览" key="overview">
                {renderMetricsOverview()}
              </TabPane>

              <TabPane tab="可视化分析" key="charts">
                {renderCharts()}
              </TabPane>

              <TabPane tab="详细报告" key="details">
                {renderDetailedReport()}
              </TabPane>
            </Tabs>
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            {tasks.length === 0 ? (
              <Empty
                description="暂无已完成的评估任务"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ) : (
              <Empty
                description="请选择一个任务查看结果"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )}
          </div>
        )}
      </Card>
    </div>
  );
};

export default ResultsView;
