import evaluationService, {
  EvaluationTask,
} from '@/services/evaluation-service';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  Empty,
  Row,
  Select,
  Space,
  Spin,
  Statistic,
  Table,
  Tabs,
  Tag,
  Tooltip as AntTooltip,
  Typography,
  message,
} from 'antd';
import { ReloadOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ResponsiveContainer,
  Tooltip as RechartsTooltip,
  XAxis,
  YAxis,
} from 'recharts';

const { Option } = Select;
const { TabPane } = Tabs;
const { Text } = Typography;

interface SampleDetail {
  id: string;
  sample_id: string;
  question: string;
  ground_truth?: string;
  retrieved_contexts: string[];
  generated_answer: string;
  metrics: {
    faithfulness?: number;
    answer_relevancy?: number;
    context_precision?: number;
    context_recall?: number;
    context_relevancy?: number;
    answer_correctness?: number;
    answer_similarity?: number;
  };
  execution_time: number;
  error_message?: string;
  create_time: string;
}

interface TaskResult {
  task_id: string;
  task_name: string;
  status: string;
  metrics: Record<string, number>;
  details: SampleDetail[];
  create_date: string;
  samples_count: number;
}

const ResultsView: React.FC = () => {
  const [tasks, setTasks] = useState<EvaluationTask[]>([]);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
  const [taskResult, setTaskResult] = useState<TaskResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [resultsLoading, setResultsLoading] = useState(false);

  // 指标名称映射
  const metricNames: Record<string, string> = {
    faithfulness: '忠实度',
    answer_relevancy: '答案相关性',
    context_precision: '上下文精确度',
    context_recall: '上下文召回率',
    context_relevancy: '上下文相关性',
    answer_correctness: '答案正确性',
    answer_similarity: '答案相似性',
    // 带avg_前缀的指标
    avg_faithfulness: '忠实度',
    avg_answer_relevancy: '答案相关性',
    avg_context_precision: '上下文精确度',
    avg_context_recall: '上下文召回率',
    avg_context_relevancy: '上下文相关性',
    avg_answer_correctness: '答案正确性',
    avg_answer_similarity: '答案相似性',
  };

  // 指标说明映射
  const metricDescriptions: Record<string, { description: string; calculation: string }> = {
    faithfulness: {
      description: '衡量生成答案与检索到的上下文信息的一致性程度。高忠实度意味着答案基于检索到的事实，没有产生幻觉。',
      calculation: '通过分析答案中的每个陈述是否能在检索到的上下文中找到支撑证据来计算。'
    },
    answer_relevancy: {
      description: '评估生成答案与用户问题的相关性。高相关性意味着答案直接回答了用户的问题，没有偏离主题。',
      calculation: '通过计算答案与问题之间的语义相似度，以及答案是否包含问题的关键信息来评估。'
    },
    context_precision: {
      description: '衡量检索到的上下文中相关信息的精确度。高精确度意味着检索到的文档大部分都与问题相关。',
      calculation: '计算检索到的文档中有多少比例是与问题真正相关的，排除无关或噪声信息。'
    },
    context_recall: {
      description: '评估检索系统是否找到了所有相关的信息。高召回率意味着没有遗漏重要的相关文档。',
      calculation: '通过检查是否检索到了回答问题所需的所有关键信息片段来计算。'
    },
    context_relevancy: {
      description: '衡量检索到的上下文与问题的整体相关性。与precision不同，这个指标关注整体相关性而非精确匹配。',
      calculation: '通过分析检索到的上下文是否有助于回答问题，即使不是直接匹配也算相关。'
    },
    answer_correctness: {
      description: '评估答案的事实准确性和完整性。这是一个综合指标，考虑答案是否正确且完整。',
      calculation: '结合语义相似度和事实准确性，通过与标准答案对比来评估答案的正确程度。'
    },
    answer_similarity: {
      description: '衡量生成答案与标准答案之间的相似程度。高相似度表示答案在语义上接近期望的回答。',
      calculation: '使用语义嵌入技术计算生成答案与参考答案之间的余弦相似度或其他相似度指标。'
    }
  };

  // 获取分数等级和颜色
  const getScoreLevel = (score: number) => {
    if (score >= 0.8) return { level: '优秀', color: '#52c41a' };
    if (score >= 0.6) return { level: '良好', color: '#1890ff' };
    if (score >= 0.4) return { level: '中等', color: '#faad14' };
    return { level: '需要改进', color: '#f5222d' };
  };

  // 获取已完成的任务列表
  const fetchCompletedTasks = async () => {
    setLoading(true);
    try {
      const response = await evaluationService.getTasks();
      if (response.data?.code === 0) {
        const completedTasks = (response.data.data || []).filter(
          (task: EvaluationTask) => task.status === 'completed',
        );
        setTasks(completedTasks);

        // 自动选择第一个任务
        if (completedTasks.length > 0 && !selectedTaskId) {
          setSelectedTaskId(completedTasks[0].id!);
        }
      } else {
        message.error('获取任务列表失败');
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取任务结果
  const fetchTaskResults = async (taskId: string) => {
    setResultsLoading(true);
    try {
      const response = await evaluationService.getTaskResults(taskId);
      if (response.data?.code === 0) {
        const data = response.data.data;
        const task = tasks.find((t) => t.id === taskId);

        setTaskResult({
          task_id: taskId,
          task_name: task?.name || 'Unknown Task',
          status: task?.status || 'unknown',
          metrics: data.metrics || {},
          details: data.details || [],
          create_date: task?.create_date || '',
          samples_count: data.samples_count || 0,
        });
      } else {
        message.error('获取结果失败');
        setTaskResult(null);
      }
    } catch (error) {
      console.error('获取任务结果失败:', error);
      message.error('获取任务结果失败');
      setTaskResult(null);
    } finally {
      setResultsLoading(false);
    }
  };

  // 手动刷新数据
  const handleRefresh = async () => {
    message.info('正在刷新数据...');
    await fetchCompletedTasks();
    if (selectedTaskId) {
      await fetchTaskResults(selectedTaskId);
    }
    message.success('数据刷新完成');
  };

  useEffect(() => {
    fetchCompletedTasks();
  }, []);

  useEffect(() => {
    if (selectedTaskId) {
      fetchTaskResults(selectedTaskId);
    }
  }, [selectedTaskId]);

  // 将指标数据转换为图表格式
  const getMetricsChartData = () => {
    if (!taskResult?.metrics) return [];

    return Object.entries(taskResult.metrics)
      .filter(([key]) => !key.includes('_time') && !key.includes('_samples')) // 过滤掉时间和样本数指标
      .map(([key, value]) => ({
        name: metricNames[key] || key,
        value: Number((value * 100).toFixed(1)),
        originalValue: value,
        fullName: `${metricNames[key] || key} (${(value * 100).toFixed(1)}%)`,
      }));
  };

  // 雷达图数据
  const getRadarData = () => {
    if (!taskResult?.metrics) return [];

    return Object.entries(taskResult.metrics)
      .filter(([key]) => !key.includes('_time') && !key.includes('_samples')) // 过滤掉时间和样本数指标
      .map(([key, value]) => ({
        metric: metricNames[key] || key,
        value: Number((value * 100).toFixed(1)),
        fullValue: value,
      }));
  };

  const renderMetricsOverview = () => {
    if (!taskResult?.metrics) return null;

    // 分离不同类型的指标
    const qualityMetrics = Object.entries(taskResult.metrics).filter(
      ([key]) => !key.includes('_time') && !key.includes('_samples') && !key.includes('failed_') && !key.includes('successful_') && !key.includes('total_')
    );

    const performanceMetrics = Object.entries(taskResult.metrics).filter(
      ([key]) => key.includes('_time')
    );

    const countMetrics = Object.entries(taskResult.metrics).filter(
      ([key]) => key.includes('_samples')
    );

    return (
      <div>
        {/* 质量指标 */}
        <h4 style={{ marginBottom: 16 }}>质量指标</h4>
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          {qualityMetrics.map(([key, value]) => {
            const scoreLevel = getScoreLevel(value);
            // 处理带avg_前缀的指标名称
            const cleanKey = key.replace('avg_', '');
            const description = metricDescriptions[cleanKey];

            return (
              <Col span={6} key={key}>
                <Card size="small">
                  <Statistic
                    title={
                      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                        <span style={{ fontSize: '14px', fontWeight: 'bold' }}>
                          {metricNames[key] || key}
                        </span>
                        {description && (
                          <AntTooltip
                            title={
                              <div style={{ maxWidth: 350, lineHeight: '1.5' }}>
                                <div style={{ fontWeight: 'bold', marginBottom: 12, fontSize: '14px', color: '#fff' }}>
                                  📊 {metricNames[key] || metricNames[cleanKey] || key}
                                </div>
                                <div style={{ marginBottom: 12, fontSize: '13px' }}>
                                  <strong style={{ color: '#91d5ff' }}>📝 指标说明：</strong>
                                  <br />
                                  {description.description}
                                </div>
                                <div style={{ fontSize: '13px' }}>
                                  <strong style={{ color: '#91d5ff' }}>🔢 计算方式：</strong>
                                  <br />
                                  {description.calculation}
                                </div>
                              </div>
                            }
                            placement="topLeft"
                          >
                            <QuestionCircleOutlined
                              style={{
                                color: '#1890ff',
                                cursor: 'help',
                                fontSize: '16px',
                                padding: '2px',
                                borderRadius: '50%',
                                backgroundColor: '#f0f8ff',
                                border: '1px solid #d6e4ff'
                              }}
                            />
                          </AntTooltip>
                        )}
                      </div>
                    }
                    value={value}
                    precision={3}
                    suffix={
                      <span style={{ fontSize: '12px', color: '#666' }}>
                        ({(value * 100).toFixed(1)}%)
                      </span>
                    }
                    valueStyle={{
                      color: scoreLevel.color,
                    }}
                  />
                  <div style={{
                    marginTop: 8,
                    fontSize: '12px',
                    color: scoreLevel.color,
                    fontWeight: 'bold',
                    textAlign: 'center'
                  }}>
                    {scoreLevel.level}
                  </div>
                </Card>
              </Col>
            );
          })}
        </Row>

        {/* 性能指标 */}
        {performanceMetrics.length > 0 && (
          <>
            <h4 style={{ marginBottom: 16 }}>性能指标</h4>
            <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
              {performanceMetrics.map(([key, value]) => (
                <Col span={6} key={key}>
                  <Card size="small">
                    <Statistic
                      title={
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                          <span style={{ fontSize: '14px', fontWeight: 'bold' }}>
                            {metricNames[key] || key.replace('avg_', '').replace('_', ' ')}
                          </span>
                          <AntTooltip
                            title={
                              <div style={{ maxWidth: 300, lineHeight: '1.5' }}>
                                <div style={{ fontWeight: 'bold', marginBottom: 8, fontSize: '14px', color: '#fff' }}>
                                  ⏱️ 执行时间
                                </div>
                                <div style={{ fontSize: '13px' }}>
                                  <strong style={{ color: '#91d5ff' }}>📝 说明：</strong>
                                  <br />
                                  评估任务的平均执行时间，反映系统的响应速度和性能表现。
                                  <br /><br />
                                  <strong style={{ color: '#91d5ff' }}>📊 评级标准：</strong>
                                  <br />
                                  • 快速: &lt; 5秒
                                  <br />
                                  • 正常: 5-10秒
                                  <br />
                                  • 较慢: &gt; 10秒
                                </div>
                              </div>
                            }
                            placement="topLeft"
                          >
                            <QuestionCircleOutlined
                              style={{
                                color: '#1890ff',
                                cursor: 'help',
                                fontSize: '16px',
                                padding: '2px',
                                borderRadius: '50%',
                                backgroundColor: '#f0f8ff',
                                border: '1px solid #d6e4ff'
                              }}
                            />
                          </AntTooltip>
                        </div>
                      }
                      value={value}
                      precision={2}
                      suffix="秒"
                      valueStyle={{
                        color: value < 5 ? '#52c41a' : value < 10 ? '#faad14' : '#f5222d'
                      }}
                    />
                    <div style={{
                      marginTop: 4,
                      fontSize: '12px',
                      color: value < 5 ? '#52c41a' : value < 10 ? '#faad14' : '#f5222d',
                      fontWeight: 'bold'
                    }}>
                      {value < 5 ? '快速' : value < 10 ? '正常' : '较慢'}
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </>
        )}

        {/* 统计指标 */}
        {countMetrics.length > 0 && (
          <>
            <h4 style={{ marginBottom: 16 }}>统计信息</h4>
            <Row gutter={[16, 16]}>
              {countMetrics.map(([key, value]) => {
                const getStatDescription = (statKey: string) => {
                  if (statKey.includes('failed')) return '评估过程中失败的样本数量';
                  if (statKey.includes('successful')) return '评估过程中成功处理的样本数量';
                  if (statKey.includes('total')) return '参与评估的样本总数';
                  return '评估相关的统计数据';
                };

                return (
                  <Col span={6} key={key}>
                    <Card size="small">
                      <Statistic
                        title={
                          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                            <span style={{ fontSize: '14px', fontWeight: 'bold' }}>
                              {metricNames[key] || key.replace('_', ' ')}
                            </span>
                            <AntTooltip
                              title={
                                <div style={{ maxWidth: 250, lineHeight: '1.5' }}>
                                  <div style={{ fontWeight: 'bold', marginBottom: 8, fontSize: '14px', color: '#fff' }}>
                                    📊 统计信息
                                  </div>
                                  <div style={{ fontSize: '13px' }}>
                                    <strong style={{ color: '#91d5ff' }}>📝 说明：</strong>
                                    <br />
                                    {getStatDescription(key)}
                                  </div>
                                </div>
                              }
                              placement="topLeft"
                            >
                              <QuestionCircleOutlined
                                style={{
                                  color: '#1890ff',
                                  cursor: 'help',
                                  fontSize: '16px',
                                  padding: '2px',
                                  borderRadius: '50%',
                                  backgroundColor: '#f0f8ff',
                                  border: '1px solid #d6e4ff'
                                }}
                              />
                            </AntTooltip>
                          </div>
                        }
                        value={value}
                        precision={0}
                        suffix="个"
                        valueStyle={{
                          color: key.includes('failed') ? '#f5222d' :
                                 key.includes('successful') ? '#52c41a' : '#722ed1'
                        }}
                      />
                    </Card>
                  </Col>
                );
              })}
            </Row>
          </>
        )}
      </div>
    );
  };

  const renderCharts = () => {
    const chartData = getMetricsChartData();
    const radarData = getRadarData();

    if (chartData.length === 0) return null;

    return (
      <div style={{ width: '100%' }}>
        <Row gutter={[24, 24]} style={{ width: '100%' }}>
          <Col span={12} style={{ width: '100%' }}>
            <Card title="指标对比" size="small" style={{ height: '100%' }}>
              <div style={{ width: '100%', height: '400px' }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={chartData} margin={{ top: 20, right: 30, bottom: 80, left: 20 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="name"
                      angle={-45}
                      textAnchor="end"
                      height={80}
                      fontSize={10}
                      tick={{ fontSize: 10, fill: '#666' }}
                    />
                    <YAxis
                      domain={[0, 100]}
                      tick={{ fontSize: 10, fill: '#666' }}
                      label={{ value: '得分 (%)', angle: -90, position: 'insideLeft' }}
                    />
                    <RechartsTooltip
                      formatter={(value) => [`${value}%`, '得分']}
                      labelFormatter={(label) => `指标: ${label}`}
                    />
                    <Bar
                      dataKey="value"
                      fill="#1890ff"
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </Card>
          </Col>
          <Col span={12} style={{ width: '100%' }}>
            <Card title="指标雷达图" size="small" style={{ height: '100%' }}>
              <div style={{ width: '100%', height: '400px' }}>
                <ResponsiveContainer width="100%" height="100%">
                  <RadarChart data={radarData} margin={{ top: 20, right: 30, bottom: 20, left: 30 }}>
                    <PolarGrid />
                    <PolarAngleAxis
                      dataKey="metric"
                      fontSize={10}
                      tick={{ fontSize: 10, fill: '#666' }}
                    />
                    <PolarRadiusAxis
                      angle={0}
                      domain={[0, 100]}
                      tickCount={5}
                      tick={{ fontSize: 8, fill: '#999' }}
                    />
                    <Radar
                      name="评估指标"
                      dataKey="value"
                      stroke="#1890ff"
                      fill="#1890ff"
                      fillOpacity={0.3}
                      strokeWidth={2}
                    />
                    <RechartsTooltip
                      formatter={(value) => [`${value}%`, '得分']}
                      labelFormatter={(label) => `指标: ${label}`}
                    />
                  </RadarChart>
                </ResponsiveContainer>
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染详细数据表格
  const renderDetailedDataTable = () => {
    if (!taskResult?.details || taskResult.details.length === 0) {
      return (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Empty description="暂无详细数据" />
        </div>
      );
    }

    const columns = [
      {
        title: '序号',
        dataIndex: 'index',
        key: 'index',
        width: 60,
        render: (_: any, __: any, index: number) => index + 1,
      },
      {
        title: '问题',
        dataIndex: 'question',
        key: 'question',
        width: 200,
        render: (text: string) => (
          <AntTooltip title={text} placement="topLeft">
            <Text ellipsis style={{ maxWidth: 180 }}>
              {text}
            </Text>
          </AntTooltip>
        ),
      },
      {
        title: '标准答案',
        dataIndex: 'ground_truth',
        key: 'ground_truth',
        width: 200,
        render: (text: string) => text ? (
          <AntTooltip title={text} placement="topLeft">
            <Text ellipsis style={{ maxWidth: 180 }}>
              {text}
            </Text>
          </AntTooltip>
        ) : (
          <Text type="secondary">-</Text>
        ),
      },
      {
        title: '生成答案',
        dataIndex: 'generated_answer',
        key: 'generated_answer',
        width: 250,
        render: (text: string) => (
          <AntTooltip title={text} placement="topLeft">
            <Text ellipsis style={{ maxWidth: 230 }}>
              {text}
            </Text>
          </AntTooltip>
        ),
      },
      {
        title: '检索上下文',
        dataIndex: 'retrieved_contexts',
        key: 'retrieved_contexts',
        width: 250,
        render: (contexts: string[]) => (
          <AntTooltip
            title={
              <div style={{ maxHeight: 300, overflow: 'auto' }}>
                {contexts.map((context, index) => (
                  <div key={index} style={{ marginBottom: 8, padding: 8, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                    <Text strong>上下文 {index + 1}:</Text>
                    <br />
                    {context}
                  </div>
                ))}
              </div>
            }
            placement="topLeft"
          >
            <Tag color="blue">
              {contexts.length} 个上下文
            </Tag>
          </AntTooltip>
        ),
      },
      {
        title: '忠实度',
        dataIndex: ['metrics', 'faithfulness'],
        key: 'faithfulness',
        width: 100,
        render: (value: number) => value ? (
          <span style={{ color: getScoreLevel(value).color }}>
            {(value * 100).toFixed(1)}%
          </span>
        ) : '-',
      },
      {
        title: '答案相关性',
        dataIndex: ['metrics', 'answer_relevancy'],
        key: 'answer_relevancy',
        width: 100,
        render: (value: number) => value ? (
          <span style={{ color: getScoreLevel(value).color }}>
            {(value * 100).toFixed(1)}%
          </span>
        ) : '-',
      },
      {
        title: '上下文精确度',
        dataIndex: ['metrics', 'context_precision'],
        key: 'context_precision',
        width: 120,
        render: (value: number) => value ? (
          <span style={{ color: getScoreLevel(value).color }}>
            {(value * 100).toFixed(1)}%
          </span>
        ) : '-',
      },
      {
        title: '上下文召回率',
        dataIndex: ['metrics', 'context_recall'],
        key: 'context_recall',
        width: 120,
        render: (value: number) => value ? (
          <span style={{ color: getScoreLevel(value).color }}>
            {(value * 100).toFixed(1)}%
          </span>
        ) : '-',
      },
      {
        title: '答案正确性',
        dataIndex: ['metrics', 'answer_correctness'],
        key: 'answer_correctness',
        width: 120,
        render: (value: number) => value ? (
          <span style={{ color: getScoreLevel(value).color }}>
            {(value * 100).toFixed(1)}%
          </span>
        ) : '-',
      },
      {
        title: '执行时间',
        dataIndex: 'execution_time',
        key: 'execution_time',
        width: 100,
        render: (time: number) => (
          <span style={{
            color: time < 5 ? '#52c41a' : time < 10 ? '#faad14' : '#f5222d'
          }}>
            {time.toFixed(2)}s
          </span>
        ),
      },
      {
        title: '状态',
        dataIndex: 'error_message',
        key: 'status',
        width: 80,
        render: (error: string) => (
          error ? (
            <AntTooltip title={error}>
              <Tag color="red">失败</Tag>
            </AntTooltip>
          ) : (
            <Tag color="green">成功</Tag>
          )
        ),
      },
    ];

    return (
      <div>
        <Card title="详细数据" size="small" style={{ marginBottom: 16 }}>
          <div style={{ marginBottom: 16 }}>
            <Text type="secondary">
              共 {taskResult.details.length} 条记录，显示每个问题的详细处理过程和评估结果
            </Text>
          </div>
          <Table
            columns={columns}
            dataSource={taskResult.details}
            rowKey="id"
            scroll={{ x: 1800, y: 600 }}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            }}
            size="small"
          />
        </Card>
      </div>
    );
  };

  const renderDetailedReport = () => {
    if (!taskResult?.metrics) return null;

    // 生成评估报告
    const generateReport = () => {
      const qualityMetrics = Object.entries(taskResult.metrics).filter(
        ([key]) => !key.includes('_time') && !key.includes('_samples') && !key.includes('failed_') && !key.includes('successful_') && !key.includes('total_')
      );

      const avgScore = qualityMetrics.reduce((sum, [, value]) => sum + value, 0) / qualityMetrics.length;



      const overallLevel = getScoreLevel(avgScore);

      return {
        avgScore,
        overallLevel,
        qualityMetrics
      };
    };

    const report = generateReport();

    return (
      <div>
        {/* 总体评估 */}
        <Card title="总体评估" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Statistic
                title="平均得分"
                value={report.avgScore}
                precision={3}
                suffix={
                  <span style={{ fontSize: '12px', color: '#666' }}>
                    ({(report.avgScore * 100).toFixed(1)}%)
                  </span>
                }
                valueStyle={{ color: report.overallLevel.color }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="评估等级"
                value={report.overallLevel.level}
                valueStyle={{ color: report.overallLevel.color }}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title="样本数量"
                value={taskResult.samples_count}
                suffix="个"
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
          </Row>
        </Card>

        {/* 详细指标分析 */}
        <Card title="详细指标分析" size="small" style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]}>
            {report.qualityMetrics.map(([key, value]) => {
              const level = getScoreLevel(value);
              return (
                <Col span={12} key={key}>
                  <Card size="small" style={{ height: '100%' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                      <strong>{metricNames[key] || key}</strong>
                      <span style={{ color: level.color, fontWeight: 'bold' }}>{level.level}</span>
                    </div>
                    <div style={{ marginBottom: 8 }}>
                      <span style={{ fontSize: '24px', fontWeight: 'bold', color: level.color }}>
                        {(value * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      原始值: {value.toFixed(4)}
                    </div>
                  </Card>
                </Col>
              );
            })}
          </Row>
        </Card>

        {/* 改进建议 */}
        <Card title="改进建议" size="small">
          <div style={{ lineHeight: '1.6' }}>
            {report.qualityMetrics.map(([key, value]) => {
              if (value < 0.6) {
                const suggestions = {
                  faithfulness: '建议优化检索质量，确保检索到的文档与问题高度相关',
                  answer_relevancy: '建议改进答案生成逻辑，确保答案直接回答用户问题',
                  context_precision: '建议优化检索算法，提高相关文档的排序准确性',
                  context_recall: '建议扩大检索范围或优化索引，确保检索到所有相关信息',
                  context_relevancy: '建议改进文档分块和索引策略，提高检索精度',
                  answer_correctness: '建议优化LLM模型参数或提示词，提高答案准确性',
                  answer_similarity: '建议检查答案生成的一致性和稳定性'
                };

                return (
                  <div key={key} style={{ marginBottom: 12, padding: 12, backgroundColor: '#fff2e8', borderRadius: 4 }}>
                    <div style={{ fontWeight: 'bold', color: '#fa8c16', marginBottom: 4 }}>
                      {metricNames[key] || key} ({(value * 100).toFixed(1)}%)
                    </div>
                    <div style={{ color: '#666' }}>
                      {suggestions[key as keyof typeof suggestions] || '建议进一步分析该指标的具体问题'}
                    </div>
                  </div>
                );
              }
              return null;
            }).filter(Boolean).length === 0 && (
              <div style={{ textAlign: 'center', color: '#52c41a', fontSize: '16px' }}>
                🎉 所有指标表现良好，无需特别改进！
              </div>
            )}
          </div>
        </Card>
      </div>
    );
  };

  const renderTaskInfo = () => {
    if (!taskResult) return null;

    return (
      <Card title="任务信息" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Statistic title="任务名称" value={taskResult.task_name} />
          </Col>
          <Col span={6}>
            <Statistic
              title="状态"
              value="已完成"
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col span={6}>
            <Statistic title="样本数量" value={taskResult.samples_count} />
          </Col>
          <Col span={6}>
            <Statistic
              title="创建时间"
              value={
                taskResult.create_date
                  ? new Date(taskResult.create_date).toLocaleDateString()
                  : '-'
              }
            />
          </Col>
        </Row>
      </Card>
    );
  };

  return (
    <div style={{ width: '100%', maxWidth: '100%' }}>
      <Card style={{ width: '100%', maxWidth: '100%' }}>
        <div style={{ marginBottom: 16, width: '100%' }}>
          <Space style={{ width: '100%', justifyContent: 'flex-start' }}>
            <Select
              placeholder="选择要查看的评估任务"
              style={{ width: 300 }}
              value={selectedTaskId}
              onChange={setSelectedTaskId}
              loading={loading}
            >
              {tasks.map((task) => (
                <Option key={task.id} value={task.id!}>
                  {task.name} -{' '}
                  {task.create_date
                    ? new Date(task.create_date).toLocaleDateString()
                    : ''}
                </Option>
              ))}
            </Select>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              loading={loading || resultsLoading}
              title="刷新数据"
            >
              刷新
            </Button>
          </Space>
        </div>

        {resultsLoading ? (
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>加载结果中...</div>
          </div>
        ) : taskResult ? (
          <div style={{ width: '100%', maxWidth: '100%' }}>
            {renderTaskInfo()}

            <Tabs defaultActiveKey="overview" style={{ width: '100%' }}>
              <TabPane tab="指标概览" key="overview">
                <div style={{ width: '100%', maxWidth: '100%' }}>
                  {renderMetricsOverview()}
                </div>
              </TabPane>

              <TabPane tab="可视化分析" key="charts">
                <div style={{ width: '100%', maxWidth: '100%' }}>
                  {renderCharts()}
                </div>
              </TabPane>

              <TabPane tab="详细数据" key="data">
                <div style={{ width: '100%', maxWidth: '100%' }}>
                  {renderDetailedDataTable()}
                </div>
              </TabPane>

              <TabPane tab="评估报告" key="report">
                <div style={{ width: '100%', maxWidth: '100%' }}>
                  {renderDetailedReport()}
                </div>
              </TabPane>
            </Tabs>
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            {tasks.length === 0 ? (
              <Empty
                description="暂无已完成的评估任务"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ) : (
              <Empty
                description="请选择一个任务查看结果"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )}
          </div>
        )}
      </Card>
    </div>
  );
};

export default ResultsView;
