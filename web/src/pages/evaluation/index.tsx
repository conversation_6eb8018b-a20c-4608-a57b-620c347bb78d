import { useTranslate } from '@/hooks/common-hooks';
import { Card, Tabs, Typography } from 'antd';
import React, { useState } from 'react';
import DatasetManagement from './components/DatasetManagement';
import ResultsView from './components/ResultsView';
import TaskManagement from './components/TaskManagement';
import styles from './index.less';

const { Title } = Typography;

const EvaluationPage: React.FC = () => {
  const { t } = useTranslate('evaluation');
  const [activeTab, setActiveTab] = useState('datasets');

  const tabItems = [
    {
      key: 'datasets',
      label: t('datasets', '数据集管理'),
      children: <DatasetManagement />,
    },
    {
      key: 'tasks',
      label: t('tasks', '评估任务'),
      children: <TaskManagement onTabChange={setActiveTab} />,
    },
    {
      key: 'results',
      label: t('results', '结果分析'),
      children: <ResultsView />,
    },
  ];

  return (
    <div className={styles.evaluationPage}>
      <Card>
        <Title level={2}>{t('title', 'RAG系统评估')}</Title>
        <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} />
      </Card>
    </div>
  );
};

export default EvaluationPage;
