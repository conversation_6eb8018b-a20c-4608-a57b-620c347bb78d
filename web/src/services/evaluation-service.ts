import { api_host } from '@/utils/api';
import request, { post } from '@/utils/request';

export interface EvaluationDataset {
  id?: string;
  name: string;
  description?: string;
  kb_id: string;
  dataset_type: 'qa_pairs' | 'questions_only';
  total_samples?: number;
  status?: 'active' | 'archived';
  create_date?: string;
  created_by?: string;
}

export interface EvaluationSample {
  id?: string;
  dataset_id: string;
  question: string;
  ground_truth?: string;
  contexts?: string[];
  sample_type: 'qa_pair' | 'question_only';
}

export interface LLMConfig {
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  presence_penalty?: number;
  frequency_penalty?: number;
}

export interface EvaluationTask {
  id?: string;
  name: string;
  description?: string;
  dataset_id: string;
  kb_id: string;
  metrics: string[];
  llm_id?: string;
  llm_config?: LLMConfig;
  status?: 'pending' | 'running' | 'completed' | 'failed' | 'stopped';
  progress?: number;
  config?: any;
  results?: any;
  create_date?: string;
  update_date?: string;
}

export interface EvaluationResult {
  task_id: string;
  metrics: Record<string, number>;
  details?: any;
}

class EvaluationService {
  // 数据集管理
  async createDataset(data: EvaluationDataset) {
    return post(`${api_host}/evaluation/datasets`, data);
  }

  async getDatasets(kb_id?: string) {
    const params = kb_id ? { kb_id } : {};
    return request.get(`${api_host}/evaluation/datasets`, { params });
  }

  async getDataset(datasetId: string) {
    return request.get(`${api_host}/evaluation/datasets/${datasetId}`);
  }

  async updateDataset(datasetId: string, data: Partial<EvaluationDataset>) {
    return post(`${api_host}/evaluation/datasets/${datasetId}`, data);
  }

  async deleteDataset(datasetId: string) {
    return request.delete(`${api_host}/evaluation/datasets/${datasetId}`);
  }

  // 样本管理
  async createSample(data: EvaluationSample) {
    return post(
      `${api_host}/evaluation/datasets/${data.dataset_id}/samples`,
      data,
    );
  }

  async getSamples(datasetId: string, page = 1, page_size = 20) {
    return request.get(`${api_host}/evaluation/datasets/${datasetId}/samples`, {
      params: { page, page_size },
    });
  }

  async updateSample(
    datasetId: string,
    sampleId: string,
    data: Partial<EvaluationSample>,
  ) {
    return post(
      `${api_host}/evaluation/datasets/${datasetId}/samples/${sampleId}`,
      data,
    );
  }

  async deleteSample(datasetId: string, sampleId: string) {
    return request.delete(
      `${api_host}/evaluation/datasets/${datasetId}/samples/${sampleId}`,
    );
  }

  async importSamples(datasetId: string, formData: FormData) {
    return post(
      `${api_host}/evaluation/datasets/${datasetId}/import`,
      formData,
    );
  }

  async exportSamples(datasetId: string, format = 'csv') {
    return request.get(`${api_host}/evaluation/datasets/${datasetId}/export`, {
      params: { format },
      responseType: 'blob',
    });
  }

  // 任务管理
  async createTask(data: EvaluationTask) {
    return post(`${api_host}/evaluation/tasks`, data);
  }

  async getTasks(dataset_id?: string, status?: string) {
    const params: any = {};
    if (dataset_id) params.dataset_id = dataset_id;
    if (status) params.status = status;
    return request.get(`${api_host}/evaluation/tasks`, { params });
  }

  async getTask(taskId: string) {
    return request.get(`${api_host}/evaluation/tasks/${taskId}`);
  }

  async startTask(taskId: string) {
    return post(`${api_host}/evaluation/tasks/${taskId}/start`, {});
  }

  async stopTask(taskId: string) {
    return post(`${api_host}/evaluation/tasks/${taskId}/stop`, {});
  }

  async restartTask(taskId: string, maxWorkers?: number) {
    return post(`${api_host}/evaluation/tasks/${taskId}/restart`, {
      max_workers: maxWorkers || 3,
    });
  }

  async deleteTask(taskId: string) {
    return request.delete(`${api_host}/evaluation/tasks/${taskId}`);
  }

  // 结果查询
  async getTaskResults(taskId: string) {
    return request.get(`${api_host}/evaluation/tasks/${taskId}/results`);
  }

  async getTaskReport(taskId: string) {
    return request.get(`${api_host}/evaluation/tasks/${taskId}/report`);
  }

  // 工具接口
  async getAvailableMetrics() {
    return request.get(`${api_host}/evaluation/metrics`);
  }

  async getAvailableLLMs(kb_id?: string) {
    const params = kb_id ? { kb_id } : {};
    return request.get(`${api_host}/evaluation/llms`, { params });
  }
}

export const evaluationService = new EvaluationService();
export default evaluationService;
