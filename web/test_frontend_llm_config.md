# 前端LLM参数配置界面测试指南

## 📋 **修改内容总结**

### **1. 接口类型定义更新**
- 添加了 `LLMConfig` 接口
- 更新了 `EvaluationTask` 接口，包含 `llm_config` 字段
- 更新了 `TaskFormValues` 接口

### **2. 前端界面更新**
- 添加了可折叠的"LLM生成参数配置"面板
- 包含5个参数配置项：
  - 温度 (Temperature): 0.0-2.0
  - 最大Tokens: 1-8192
  - Top P: 0.0-1.0
  - 存在惩罚 (Presence Penalty): -2.0-2.0
  - 频率惩罚 (Frequency Penalty): -2.0-2.0

### **3. 表单处理更新**
- 更新了 `handleCreateTask` 函数
- 过滤空值和无效值
- 只在有有效配置时才发送 `llm_config`

## 🧪 **测试步骤**

### **步骤1: 启动前端服务**
```bash
cd web
npm run dev
```

### **步骤2: 访问评估页面**
1. 打开浏览器访问 `http://localhost:3000`
2. 导航到评估页面
3. 点击"创建任务"按钮

### **步骤3: 验证界面元素**
检查以下元素是否正确显示：

#### **基本表单字段**
- ✅ 任务名称
- ✅ 描述
- ✅ 评估数据集
- ✅ 目标知识库
- ✅ 评估模型
- ✅ 评估指标

#### **新增的LLM配置面板**
- ✅ "LLM生成参数配置（可选）" 折叠面板
- ✅ 温度 (Temperature) 输入框，默认值 0.1
- ✅ 最大Tokens 输入框，默认值 1024
- ✅ Top P 输入框，默认值 0.9
- ✅ 存在惩罚 输入框，默认值 0.0
- ✅ 频率惩罚 输入框，默认值 0.0

### **步骤4: 测试参数输入**

#### **测试默认值**
1. 展开LLM配置面板
2. 验证所有字段显示正确的默认值
3. 不修改任何值，直接创建任务
4. 检查后端是否收到正确的默认配置

#### **测试自定义值**
1. 修改温度为 0.3
2. 修改最大Tokens为 2048
3. 修改Top P为 0.8
4. 修改存在惩罚为 0.1
5. 修改频率惩罚为 0.1
6. 创建任务
7. 检查后端是否收到自定义配置

#### **测试边界值**
1. 温度设置为 0.0 和 2.0
2. 最大Tokens设置为 1 和 8192
3. Top P设置为 0.0 和 1.0
4. 惩罚参数设置为 -2.0 和 2.0
5. 验证输入框限制是否正确

### **步骤5: 验证API调用**

#### **检查网络请求**
1. 打开浏览器开发者工具
2. 切换到Network标签
3. 创建任务时观察API请求
4. 验证请求体包含 `llm_config` 字段

#### **预期的API请求格式**
```json
{
  "name": "测试任务",
  "description": "测试描述",
  "dataset_id": "dataset_123",
  "kb_id": "kb_456",
  "metrics": ["faithfulness", "answer_relevancy"],
  "llm_id": "gpt-4o-2024-11-20@TogetherAI",
  "llm_config": {
    "temperature": 0.3,
    "max_tokens": 2048,
    "top_p": 0.8,
    "presence_penalty": 0.1,
    "frequency_penalty": 0.1
  }
}
```

### **步骤6: 验证后端处理**

#### **检查服务端日志**
1. 观察后端日志中的任务创建请求
2. 确认 `llm_config` 参数被正确接收
3. 验证参数被正确传递给评估器

#### **预期的后端日志**
```
INFO: 创建评估任务，配置: {
  "temperature": 0.3,
  "max_tokens": 2048,
  "top_p": 0.8,
  "presence_penalty": 0.1,
  "frequency_penalty": 0.1
}
```

## 🎯 **验证要点**

### **界面交互**
- ✅ 折叠面板可以正常展开/收起
- ✅ 所有输入框都有正确的范围限制
- ✅ Tooltip提示信息显示正确
- ✅ 默认值正确显示

### **数据处理**
- ✅ 空值被正确过滤
- ✅ 只有有效配置才发送到后端
- ✅ 表单重置时配置也被清空

### **用户体验**
- ✅ 配置是可选的，不影响基本功能
- ✅ 参数说明清晰易懂
- ✅ 输入验证友好

## 🚀 **预期效果**

修改完成后，用户可以：

1. **使用默认配置**：
   - 不展开LLM配置面板
   - 系统使用合理的默认值

2. **自定义配置**：
   - 展开配置面板
   - 根据需要调整参数
   - 获得更精确的控制

3. **参数说明**：
   - 鼠标悬停查看参数说明
   - 了解每个参数的作用

4. **灵活配置**：
   - 只配置需要的参数
   - 其他参数使用默认值

## 📝 **注意事项**

1. **兼容性**：新界面向后兼容，不影响现有功能
2. **可选性**：LLM配置完全可选，不配置也能正常工作
3. **验证**：前端有输入范围验证，后端也应该有参数验证
4. **默认值**：提供的默认值适合大多数使用场景

现在前端界面已经支持LLM参数配置，用户可以根据需要调整模型的生成行为！
