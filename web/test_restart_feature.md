# 评估任务重新运行功能测试指南

## 📋 **功能概述**

为评估任务添加了重新运行功能，用户不再需要每次都重新创建任务，可以直接重新运行已完成、失败或停止的任务。

## 🔧 **实现内容**

### **1. 后端API更新**

#### **新增重新运行接口**
```
POST /api/evaluation/tasks/{task_id}/restart
```

**请求参数**：
```json
{
  "max_workers": 3  // 可选，默认为3
}
```

**响应格式**：
```json
{
  "code": 0,
  "data": {
    "success": true,
    "message": "任务已重新开始运行"
  }
}
```

#### **EvaluationRunner新增restart_task方法**
- 检查任务状态（只允许已完成、失败、停止的任务重新运行）
- 清理历史评估结果和报告
- 重置任务状态和进度
- 重新启动后台执行线程

### **2. 前端界面更新**

#### **TaskManagement组件更新**
- 添加了`restartTask`服务方法
- 添加了`handleRestartTask`处理函数
- 在操作列中添加了"重新运行"按钮
- 使用`ReloadOutlined`图标

#### **按钮显示逻辑**
```typescript
{(record.status === 'completed' || record.status === 'failed' || record.status === 'stopped') && (
  <Popconfirm
    title="确定重新运行这个任务吗？"
    description="将清除之前的评估结果并重新开始"
    onConfirm={() => handleRestartTask(record.id!)}
  >
    <Button type="link" icon={<ReloadOutlined />}>
      重新运行
    </Button>
  </Popconfirm>
)}
```

## 🧪 **测试步骤**

### **步骤1: 准备测试环境**
1. 启动后端服务
2. 启动前端服务
3. 确保有可用的数据集和知识库

### **步骤2: 创建测试任务**
1. 访问评估页面
2. 创建一个新的评估任务
3. 启动任务并等待完成（或手动停止）

### **步骤3: 测试重新运行功能**

#### **测试用例1: 已完成任务重新运行**
1. 找到状态为"已完成"的任务
2. 点击"重新运行"按钮
3. 确认重新运行对话框
4. 验证：
   - ✅ 任务状态变为"运行中"
   - ✅ 进度重置为0%
   - ✅ 历史结果被清除
   - ✅ 任务重新开始执行

#### **测试用例2: 失败任务重新运行**
1. 找到状态为"失败"的任务
2. 点击"重新运行"按钮
3. 验证任务能够重新启动

#### **测试用例3: 停止任务重新运行**
1. 启动一个任务然后立即停止
2. 点击"重新运行"按钮
3. 验证任务能够重新启动

#### **测试用例4: 运行中任务不能重新运行**
1. 找到状态为"运行中"的任务
2. 验证没有"重新运行"按钮显示

#### **测试用例5: 等待中任务不能重新运行**
1. 找到状态为"等待中"的任务
2. 验证没有"重新运行"按钮显示

### **步骤4: 验证API调用**

#### **检查网络请求**
1. 打开浏览器开发者工具
2. 切换到Network标签
3. 点击重新运行按钮
4. 验证API请求：
   ```
   POST /shangou_ai_rag/api/v1/evaluation/tasks/{task_id}/restart
   Request Body: {"max_workers": 3}
   Response: {"code": 0, "data": {"success": true, "message": "任务已重新开始运行"}}
   ```

#### **检查服务端日志**
```
INFO: 重新运行评估任务: {task_id}
INFO: 清理任务 {task_id} 的历史评估结果
INFO: 清理任务 {task_id} 的历史评估报告
INFO: 启动评估任务: {task_id}
```

## 🎯 **预期行为**

### **重新运行流程**
1. **状态检查**：只有已完成、失败、停止的任务可以重新运行
2. **数据清理**：清除历史评估结果和报告
3. **状态重置**：
   - status: "running"
   - progress: 0
   - processed_samples: 0
   - started_at: 当前时间
   - completed_at: null
4. **任务执行**：使用原始配置重新执行评估

### **用户体验**
- ✅ **便捷性**：不需要重新创建任务
- ✅ **安全性**：有确认对话框防止误操作
- ✅ **清晰性**：明确说明会清除历史结果
- ✅ **一致性**：使用相同的配置和参数

## 🔍 **错误处理**

### **后端错误处理**
- 任务不存在：返回404错误
- 任务正在运行：返回操作错误
- 任务状态为pending：返回操作错误
- 数据库操作失败：返回服务器错误

### **前端错误处理**
- 网络请求失败：显示"重新运行失败"消息
- 服务器返回错误：显示具体错误信息
- 操作成功：显示"任务已重新开始运行"消息

## 📊 **状态转换图**

```
pending → start → running → completed → restart → running
                     ↓           ↑
                   failed → restart
                     ↓           ↑  
                  stopped → restart
```

## 💡 **使用场景**

1. **调试和优化**：
   - 调整LLM参数后重新测试
   - 修复配置错误后重新运行

2. **结果对比**：
   - 使用不同参数多次运行同一任务
   - 对比不同配置的评估结果

3. **故障恢复**：
   - 网络问题导致失败后重新运行
   - 系统重启后恢复中断的任务

4. **定期评估**：
   - 定期重新评估知识库性能
   - 跟踪模型性能变化

## 🚀 **优势**

1. **提高效率**：避免重复配置任务
2. **保持一致性**：使用相同的配置参数
3. **简化操作**：一键重新运行
4. **数据清理**：自动清除历史数据避免混淆

现在用户可以方便地重新运行评估任务，无需每次都重新创建！
