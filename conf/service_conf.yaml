ragflow:
  host: 0.0.0.0
  http_port: 9380
mysql:
  name: 'rag_flow'
  user: 'root'
  password: 'infini_rag_flow'
  host: 'localhost'
  port: 5455
  max_connections: 100
  stale_timeout: 30
minio:
  user: 'rag_flow'
  password: 'infini_rag_flow'
  host: 'localhost:9000'
es:
  # 标准ES连接配置
  hosts: 'http://localhost:1200'
  username: 'elastic'
  password: 'infini_rag_flow'
  
  # 公司ES8集群配置
  use_company_infra: true
  cluster_name: 'shangou_sgrag_default'
  app_key: 'com.sankuai.shangou.ai.sgrag'
  access_key: '2A139D9E4D7563C536A5435E6AA2103F'
  discovery_url: 'http://openapi.eagle.test.sankuai.com/openapi'
  port: 8080
  use_ssl: false
  verify_certs: false
infinity:
  uri: 'localhost:23817'
  db_name: 'default_db'
redis:
  db: 1
  password: 'infini_rag_flow'
  host: 'localhost:6379'

# 美团MSS S3配置
mss_s3:
  # 推荐使用透明密钥方式
  appkey: 'com.sankuai.shangou.ai.sgrag'
  # 访问域名，如msstest.vip.sankuai.com，请勿包含http或https
  endpoint: 'msstest.vip.sankuai.com'
  # 是否使用HTTPS
  use_https: false
  # 是否启用直连
  use_direct_conn: true
  # 账号密钥方式（可选，优先使用appkey透明密钥）
  # access_key: 'your_access_key'
  # secret_key: 'your_secret_key'


user_default_llm:
  factory: "TogetherAI"
  base_url: "https://aigc.sankuai.com/v1/openai/native/"
  api_key: "1910229650153103381"  # 替换为你的TogetherAI API密钥
  default_models:
    chat_model: "gpt-4o-2024-11-20"  # 默认聊天模型
    embedding_model: "text-embedding-miffy-002"  # 默认嵌入模型


# postgres:
#   name: 'rag_flow'
#   user: 'rag_flow'
#   password: 'infini_rag_flow'
#   host: 'postgres'
#   port: 5432
#   max_connections: 100
#   stale_timeout: 30
# s3:
#   access_key: 'access_key'
#   secret_key: 'secret_key'
#   region: 'region'
# oss:
#   access_key: 'access_key'
#   secret_key: 'secret_key'
#   endpoint_url: 'http://oss-cn-hangzhou.aliyuncs.com'
#   region: 'cn-hangzhou'
#   bucket: 'bucket_name'
# azure:
#   auth_type: 'sas'
#   container_url: 'container_url'
#   sas_token: 'sas_token'
# azure:
#   auth_type: 'spn'
#   account_url: 'account_url'
#   client_id: 'client_id'
#   secret: 'secret'
#   tenant_id: 'tenant_id'
#   container_name: 'container_name'
# user_default_llm:
#   factory: 'Tongyi-Qianwen'
#   api_key: 'sk-xxxxxxxxxxxxx'
#   base_url: ''
oauth:
#   github:
#     client_id: xxxxxxxxxxxxxxxxxxxxxxxxx
#     secret_key: xxxxxxxxxxxxxxxxxxxxxxxxxxxx
#     url: https://github.com/login/oauth/access_token
#   feishu:
#     app_id: cli_xxxxxxxxxxxxxxxxxxx
#     app_secret: xxxxxxxxxxxxxxxxxxxxxxxxxxxx
#     app_access_token_url: https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal
#     user_access_token_url: https://open.feishu.cn/open-apis/authen/v1/oidc/access_token
#     grant_type: 'authorization_code'
  sso:
    # 环境通用配置
    login_uri: '/login'
    auth_uri: '/oauth2.0/access-token'
    user_info_uri: '/api/session/userinfo'
    logout_uri: '/oauth2.0/logout'
    
    # 环境特定配置
    env:
      # 测试环境配置
      test:
        client_id: 'd7885a354f'
        secret: 'a5b0392c3cdf40f28e3c9a1742fae749'
        sso_host: 'https://ssosv.it.test.sankuai.com/sson'
        api_host: 'https://ssosv.it.test.sankuai.com/open'
      
      # ST环境配置
      st:
        client_id: 'd7885a354f'
        secret: 'a5b0392c3cdf40f28e3c9a1742fae749'
        sso_host: 'https://ssosv.it.st.sankuai.com/sson'
        api_host: 'https://ssosv.it.st.sankuai.com/open'
      
      # 生产环境配置
      prod:
        client_id: 'd7885a354f'
        secret: 'a5b0392c3cdf40f28e3c9a1742fae749'
        sso_host: 'https://ssosv.sankuai.com/sson'
        api_host: 'https://ssosv.sankuai.com/open'
# authentication:
#   client:
#     switch: false
#     http_app_key:
#     http_secret_key:
#   site:
#     switch: false
# permission:
#   switch: false
#   component: false
#   dataset: false
