# 统一数据库配置文件
# 包含标准MySQL和Zebra代理的配置

# 标准数据库配置（Docker/直连方式）
standard:
  # MySQL配置
  mysql:
    name: 'rag_flow'
    user: 'root'
    password: 'infini_rag_flow'
    host: 'localhost'  # Docker环境中使用'mysql'
    port: 5455  # Docker环境中使用3306
    max_connections: 100
    stale_timeout: 30
  
  # PostgreSQL配置（可选）
  postgres:
    name: 'rag_flow'
    user: 'rag_flow'
    password: 'infini_rag_flow'
    host: 'postgres'
    port: 5432
    max_connections: 100
    stale_timeout: 30

# Zebra代理配置（公司内部MySQL组件）
zebra:
  app_key: 'com.sankuai.shangou.ai.sgrag'  # 公司应用Key
  ref_keys:
    default: 'sgai_rag_flow_test'  # 默认数据库引用Key
    # 可以添加其他环境的引用key
    # prod: 'sgai_rag_flow_prod'
    # staging: 'sgai_rag_flow_staging'
  
  # 连接池配置
  pool_size: 100  # 最大连接数
  pool_timeout: 30  # 连接超时时间（秒）
  pool_recycle: 3600  # 连接回收时间（秒）

# 全局配置
global:
  # 默认使用哪种连接方式: standard 或 zebra
  default_connection: 'standard' 