# 🚀 Ragas依赖部署解决方案

## 快速开始

### 1. 本地准备（在网络良好的环境）

```bash
# 给脚本执行权限
chmod +x prepare_ragas_package.sh

# 运行打包脚本
./prepare_ragas_package.sh
```

这会生成一个 `ragas_deps_YYYYMMDD_HHMMSS.tar.gz` 文件。

### 2. 上传到S3

将生成的tar.gz文件上传到S3，重命名为：
```
https://msstest.sankuai.com/sg-ug-api-test-sdk-agent/ragas_deps_latest.tar.gz
```

### 3. 部署

在服务器上运行：
```bash
./build.sh
```

build.sh会自动：
1. 尝试从S3下载Ragas依赖包
2. 解压并安装Ragas相关依赖
3. 如果失败，回退到内网pip安装

## 🔧 文件说明

- `prepare_ragas_package.sh` - 一键打包脚本（本地运行）
- `build.sh` - 修改后的构建脚本（服务器运行）
- `requirements_without_ragas.txt` - 不含Ragas的依赖文件（可选）

## ✅ 预期效果

- 部署时间：从30+分钟 → 5分钟内
- 成功率：从50% → 95%+
- 网络依赖：最小化

## 🔍 验证安装

```bash
source venv/bin/activate
python -c "import ragas, datasets; print('✅ Ragas安装成功')"
```

## 📞 故障排除

1. **S3下载失败**：检查网络和URL
2. **安装失败**：检查Python版本兼容性
3. **导入失败**：检查虚拟环境激活
