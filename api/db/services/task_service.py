#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
import os
import random
import xxhash
from datetime import datetime
import logging

from api.db.db_utils import bulk_insert_into_db
from deepdoc.parser import PdfParser
from peewee import JOIN
from api.db.db_models import DB, File2Document, File
from api.db import StatusEnum, FileType, TaskStatus
from api.db.db_models import Task, Document, Knowledgebase, Tenant
from api.db.services.common_service import CommonService
from api.db.services.document_service import DocumentService
from api.utils import current_timestamp, get_uuid
from deepdoc.parser.excel_parser import RA<PERSON><PERSON><PERSON>ExcelParser
from rag.settings import SVR_QUEUE_NAME
from rag.utils.storage_factory import STORAGE_IMPL
from rag.utils.redis_conn import REDIS_CONN
from api import settings
from rag.nlp import search
from rag.utils.redis_conn import RedisDistributedLock
from rag.utils.squirrel_conn import MTSquirrelDistributedLock


def trim_header_by_lines(text: str, max_length) -> str:
    len_text = len(text)
    if len_text <= max_length:
        return text
    for i in range(len_text):
        if text[i] == '\n' and len_text - i <= max_length:
            return text[i + 1:]
    return text


class TaskService(CommonService):
    model = Task

    @classmethod
    @DB.connection_context()
    def get_task(cls, task_id):
        logging.info(f"[任务服务] 获取任务信息开始: task_id={task_id}")
        try:
            fields = [
                cls.model.id,
                cls.model.doc_id,
                cls.model.from_page,
                cls.model.to_page,
                cls.model.retry_count,
                Document.kb_id,
                Document.parser_id,
                Document.parser_config,
                Document.name,
                Document.type,
                Document.location,
                Document.size,
                Knowledgebase.tenant_id,
                Knowledgebase.language,
                Knowledgebase.embd_id,
                Knowledgebase.pagerank,
                Knowledgebase.parser_config.alias("kb_parser_config"),
                Tenant.img2txt_id,
                Tenant.asr_id,
                Tenant.llm_id,
                cls.model.update_time,
            ]
            

            docs = (
                cls.model.select(*fields)
                    .join(Document, on=(cls.model.doc_id == Document.id))
                    .join(Knowledgebase, on=(Document.kb_id == Knowledgebase.id))
                    .join(Tenant, on=(Knowledgebase.tenant_id == Tenant.id))
                    .where(cls.model.id == task_id)
            )
            docs = list(docs.dicts())
            logging.info(f"[任务服务] 查询任务结果: task_id={task_id}, 找到记录数={len(docs)}")

            if not docs:
                logging.warning(f"[任务服务] 未找到任务: task_id={task_id}")
                return None

            msg = f"\n{datetime.now().strftime('%H:%M:%S')} Task has been received."
            prog = random.random() / 10.0
            if docs[0]["retry_count"] >= 3:
                msg = "\nERROR: Task is abandoned after 3 times attempts."
                prog = -1
                logging.warning(f"[任务服务] 任务重试次数过多，已放弃: task_id={task_id}, retry_count={docs[0]['retry_count']}")

            try:
                logging.info(f"[任务服务] 更新任务进度: task_id={task_id}, progress={prog}")
                cls.model.update(
                    progress_msg=cls.model.progress_msg + msg,
                    progress=prog,
                    retry_count=docs[0]["retry_count"] + 1,
                ).where(cls.model.id == docs[0]["id"]).execute()
            except Exception as e:
                logging.error(f"[任务服务] 更新任务进度失败: task_id={task_id}, error={str(e)}")
                raise

            if docs[0]["retry_count"] >= 3:
                return None

            logging.info(f"[任务服务] 成功获取任务信息: task_id={task_id}")
            return docs[0]
        except Exception as e:
            logging.exception(f"[任务服务] 获取任务异常: task_id={task_id}, error={str(e)}")
            raise

    @classmethod
    @DB.connection_context()
    def get_tasks(cls, doc_id: str):
        fields = [
            cls.model.id,
            cls.model.from_page,
            cls.model.progress,
            cls.model.digest,
            cls.model.chunk_ids,
        ]
        tasks = (
            cls.model.select(*fields).order_by(cls.model.from_page.asc(), cls.model.create_time.desc())
                .where(cls.model.doc_id == doc_id)
        )
        tasks = list(tasks.dicts())
        if not tasks:
            return None
        return tasks

    @classmethod
    @DB.connection_context()
    def update_chunk_ids(cls, id: str, chunk_ids: str):
        cls.model.update(chunk_ids=chunk_ids).where(cls.model.id == id).execute()

    @classmethod
    @DB.connection_context()
    def get_ongoing_doc_name(cls):
        # with RedisDistributedLock("get_task", -1):
        with MTSquirrelDistributedLock("get_task", -1):
            docs = (
                cls.model.select(
                    *[Document.id, Document.kb_id, Document.location, File.parent_id]
                )
                    .join(Document, on=(cls.model.doc_id == Document.id))
                    .join(
                    File2Document,
                    on=(File2Document.document_id == Document.id),
                    join_type=JOIN.LEFT_OUTER,
                )
                    .join(
                    File,
                    on=(File2Document.file_id == File.id),
                    join_type=JOIN.LEFT_OUTER,
                )
                    .where(
                    Document.status == StatusEnum.VALID.value,
                    Document.run == TaskStatus.RUNNING.value,
                    ~(Document.type == FileType.VIRTUAL.value),
                    cls.model.progress < 1,
                    cls.model.create_time >= current_timestamp() - 1000 * 600,
                )
            )
            docs = list(docs.dicts())
            if not docs:
                return []

            return list(
                set(
                    [
                        (
                            d["parent_id"] if d["parent_id"] else d["kb_id"],
                            d["location"],
                        )
                        for d in docs
                    ]
                )
            )

    @classmethod
    @DB.connection_context()
    def do_cancel(cls, id):
        try:
            task = cls.model.get_by_id(id)
            _, doc = DocumentService.get_by_id(task.doc_id)
            return doc.run == TaskStatus.CANCEL.value or doc.progress < 0
        except cls.model.DoesNotExist:
            # 如果任务不存在，返回True表示应该取消执行
            return True
        except Exception:
            # 其他异常也返回True，避免任务继续执行
            return True

    @classmethod
    @DB.connection_context()
    def update_progress(cls, id, info):
        logging.info(f"[任务服务] 开始更新任务进度: task_id={id}, info={info}")
        
        if os.environ.get("MACOS"):
            logging.info(f"[任务服务] 在MACOS环境运行更新进度: task_id={id}")
            try:
                if info["progress_msg"]:
                    task = cls.model.get_by_id(id)
                    progress_msg = trim_header_by_lines(task.progress_msg + "\n" + info["progress_msg"], 3000)
                    cls.model.update(progress_msg=progress_msg).where(cls.model.id == id).execute()
                if "progress" in info:
                    cls.model.update(progress=info["progress"]).where(
                        cls.model.id == id
                    ).execute()
                logging.info(f"[任务服务] MACOS环境完成更新任务进度: task_id={id}")
            except cls.model.DoesNotExist:
                logging.warning(f"[任务服务] 任务不存在，跳过进度更新: task_id={id}")
                return
            except Exception as e:
                logging.exception(f"[任务服务] MACOS环境更新任务进度异常: task_id={id}, error={str(e)}")
                raise
            return

        try:
            logging.info(f"[任务服务] 尝试获取进度更新锁: task_id={id}")
            # with RedisDistributedLock("update_progress", -1):  # 添加10秒超时，避免无限等待
            with MTSquirrelDistributedLock("update_progress", -1):  # 添加10秒超时，避免无限等待
                logging.info(f"[任务服务] 成功获取进度更新锁: task_id={id}")
                
                try:
                    if info["progress_msg"]:
                        task = cls.model.get_by_id(id)
                        progress_msg = trim_header_by_lines(task.progress_msg + "\n" + info["progress_msg"], 3000)
                        cls.model.update(progress_msg=progress_msg).where(cls.model.id == id).execute()
                        logging.info(f"[任务服务] 更新任务消息成功: task_id={id}")
                        
                    if "progress" in info:
                        cls.model.update(progress=info["progress"]).where(
                            cls.model.id == id
                        ).execute()
                        logging.info(f"[任务服务] 更新任务进度成功: task_id={id}, progress={info['progress']}")
                except cls.model.DoesNotExist:
                    logging.warning(f"[任务服务] 任务不存在，跳过进度更新: task_id={id}")
                    return
            
            logging.info(f"[任务服务] 完成更新任务进度: task_id={id}")
        except Exception as e:
            logging.exception(f"[任务服务] 更新任务进度异常: task_id={id}, error={str(e)}")
            raise


def queue_tasks(doc: dict, bucket: str, name: str):
    logging.info(f"[任务队列] 开始创建任务队列: doc_id={doc['id']}, type={doc['type']}")
    
    def new_task():
        task_id = get_uuid()
        logging.info(f"[任务队列] 创建新任务: task_id={task_id}, doc_id={doc['id']}")
        return {"id": task_id, "doc_id": doc["id"], "progress": 0.0, "from_page": 0, "to_page": 100000000}

    parse_task_array = []

    try:
        if doc["type"] == FileType.PDF.value:
            logging.info(f"[任务队列] 处理PDF文档: doc_id={doc['id']}, name={doc['name']}")
            file_bin = STORAGE_IMPL.get(bucket, name)
            do_layout = doc["parser_config"].get("layout_recognize", "DeepDOC")
            pages = PdfParser.total_page_number(doc["name"], file_bin)
            page_size = doc["parser_config"].get("task_page_size", 12)
            if doc["parser_id"] == "paper":
                page_size = doc["parser_config"].get("task_page_size", 22)
            if doc["parser_id"] in ["one", "knowledge_graph"] or do_layout != "DeepDOC":
                page_size = 10 ** 9
            page_ranges = doc["parser_config"].get("pages") or [(1, 10 ** 5)]
            
            logging.info(f"[任务队列] PDF文档信息: pages={pages}, page_size={page_size}, ranges={page_ranges}")
            
            for s, e in page_ranges:
                s -= 1
                s = max(0, s)
                e = min(e - 1, pages)
                for p in range(s, e, page_size):
                    task = new_task()
                    task["from_page"] = p
                    task["to_page"] = min(p + page_size, e)
                    parse_task_array.append(task)
                    logging.info(f"[任务队列] 添加PDF任务: task_id={task['id']}, pages={p}-{task['to_page']}")

        elif doc["parser_id"] == "table":
            logging.info(f"[任务队列] 处理表格文档: doc_id={doc['id']}, name={doc['name']}")
            file_bin = STORAGE_IMPL.get(bucket, name)
            rn = RAGFlowExcelParser.row_number(doc["name"], file_bin)
            for i in range(0, rn, 3000):
                task = new_task()
                task["from_page"] = i
                task["to_page"] = min(i + 3000, rn)
                parse_task_array.append(task)
                logging.info(f"[任务队列] 添加表格任务: task_id={task['id']}, rows={i}-{task['to_page']}")
        else:
            task = new_task()
            parse_task_array.append(task)
            logging.info(f"[任务队列] 添加普通文档任务: task_id={task['id']}, doc_id={doc['id']}")

        logging.info(f"[任务队列] 获取文档分块配置: doc_id={doc['id']}")
        chunking_config = DocumentService.get_chunking_config(doc["id"])
        
        for task in parse_task_array:
            hasher = xxhash.xxh64()
            for field in sorted(chunking_config.keys()):
                if field == "parser_config":
                    for k in ["raptor", "graphrag"]:
                        if k in chunking_config[field]:
                            del chunking_config[field][k]
                hasher.update(str(chunking_config[field]).encode("utf-8"))
            for field in ["doc_id", "from_page", "to_page"]:
                hasher.update(str(task.get(field, "")).encode("utf-8"))
            task_digest = hasher.hexdigest()
            task["digest"] = task_digest
            task["progress"] = 0.0
            logging.info(f"[任务队列] 计算任务摘要: task_id={task['id']}, digest={task_digest}")

        logging.info(f"[任务队列] 检查是否有可重用的任务: doc_id={doc['id']}")
        prev_tasks = TaskService.get_tasks(doc["id"])
        ck_num = 0
        if prev_tasks:
            logging.info(f"[任务队列] 发现之前的任务: doc_id={doc['id']}, count={len(prev_tasks)}")
            for task in parse_task_array:
                reused = reuse_prev_task_chunks(task, prev_tasks, chunking_config)
                ck_num += reused
                if reused > 0:
                    logging.info(f"[任务队列] 重用之前的任务块: task_id={task['id']}, chunks={reused}")
                    
            logging.info(f"[任务队列] 删除之前的任务: doc_id={doc['id']}")
            TaskService.filter_delete([Task.doc_id == doc["id"]])
            
            chunk_ids = []
            for task in prev_tasks:
                if task["chunk_ids"]:
                    chunk_ids.extend(task["chunk_ids"].split())
            if chunk_ids:
                logging.info(f"[任务队列] 删除之前的索引块: doc_id={doc['id']}, chunks={len(chunk_ids)}")
                settings.docStoreConn.delete({"id": chunk_ids}, search.index_name(chunking_config["tenant_id"]),
                                            chunking_config["kb_id"])
        
        DocumentService.update_by_id(doc["id"], {"chunk_num": ck_num})
        logging.info(f"[任务队列] 更新文档块数量: doc_id={doc['id']}, chunks={ck_num}")

        logging.info(f"[任务队列] 批量插入任务到数据库: doc_id={doc['id']}, tasks={len(parse_task_array)}")
        bulk_insert_into_db(Task, parse_task_array, True)
        
        logging.info(f"[任务队列] 标记文档开始解析: doc_id={doc['id']}")
        DocumentService.begin2parse(doc["id"])

        unfinished_task_array = [task for task in parse_task_array if task["progress"] < 1.0]
        logging.info(f"[任务队列] 需要处理的未完成任务: doc_id={doc['id']}, count={len(unfinished_task_array)}")
        
        for unfinished_task in unfinished_task_array:
            try:
                logging.info(f"[任务队列] 发送任务到Redis队列: task_id={unfinished_task['id']}, queue={SVR_QUEUE_NAME}")
                result = REDIS_CONN.queue_product(SVR_QUEUE_NAME, message=unfinished_task)
                if not result:
                    logging.error(f"[任务队列] 发送任务到Redis失败: task_id={unfinished_task['id']}")
                    raise RuntimeError("Can't access Redis. Please check the Redis' status.")
                logging.info(f"[任务队列] 发送任务到Redis成功: task_id={unfinished_task['id']}")
            except Exception as e:
                logging.exception(f"[任务队列] 发送任务到Redis异常: task_id={unfinished_task['id']}, error={str(e)}")
                raise
        
        logging.info(f"[任务队列] 完成任务队列创建: doc_id={doc['id']}, total_tasks={len(parse_task_array)}")
    except Exception as e:
        logging.exception(f"[任务队列] 创建任务队列异常: doc_id={doc['id']}, error={str(e)}")
        raise


def reuse_prev_task_chunks(task: dict, prev_tasks: list[dict], chunking_config: dict):
    idx = 0
    while idx < len(prev_tasks):
        prev_task = prev_tasks[idx]
        if prev_task.get("from_page", 0) == task.get("from_page", 0) \
                and prev_task.get("digest", 0) == task.get("digest", ""):
            break
        idx += 1

    if idx >= len(prev_tasks):
        return 0
    prev_task = prev_tasks[idx]
    if prev_task["progress"] < 1.0 or not prev_task["chunk_ids"]:
        return 0
    task["chunk_ids"] = prev_task["chunk_ids"]
    task["progress"] = 1.0
    if "from_page" in task and "to_page" in task and int(task['to_page']) - int(task['from_page']) >= 10 ** 6:
        task["progress_msg"] = f"Page({task['from_page']}~{task['to_page']}): "
    else:
        task["progress_msg"] = ""
    task["progress_msg"] = " ".join(
        [datetime.now().strftime("%H:%M:%S"), task["progress_msg"], "Reused previous task's chunks."])
    prev_task["chunk_ids"] = ""

    return len(task["chunk_ids"].split())
