#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
import hashlib
from datetime import datetime

import peewee
from werkzeug.security import generate_password_hash, check_password_hash

from api.db import UserTenantRole
from api.db.db_models import DB, UserTenant
from api.db.db_models import User, Tenant
from api.db.services.common_service import CommonService
from api.utils import get_uuid, current_timestamp, datetime_format, timestamp_to_date
from api.db import StatusEnum
from rag.settings import MINIO


class UserService(CommonService):
    model = User

    @classmethod
    @DB.connection_context()
    def filter_by_id(cls, user_id):
        try:
            user = cls.model.select().where(cls.model.id == user_id).get()
            return user
        except peewee.DoesNotExist:
            return None

    @classmethod
    @DB.connection_context()
    def query_user(cls, email, password):
        user = cls.model.select().where((cls.model.email == email),
                                        (cls.model.status == StatusEnum.VALID.value)).first()
        if user and check_password_hash(str(user.password), password):
            return user
        else:
            return None

    @classmethod
    @DB.connection_context()
    def save(cls, **kwargs):
        if "id" not in kwargs:
            kwargs["id"] = get_uuid()
        if "access_token" not in kwargs:
            kwargs["access_token"] = get_uuid()
        if "password" in kwargs:
            kwargs["password"] = generate_password_hash(
                str(kwargs["password"]))
        kwargs["create_time"] = current_timestamp()
        kwargs["create_date"] = timestamp_to_date(kwargs["create_time"])
        kwargs["update_time"] = current_timestamp()
        kwargs["update_date"] = timestamp_to_date(kwargs["update_time"])
        obj = cls.model(**kwargs).save(force_insert=True)
        return obj

    @classmethod
    @DB.connection_context()
    def delete_user(cls, user_ids, update_user_dict):
        with DB.atomic():
            cls.model.update({"status": 0}).where(
                cls.model.id.in_(user_ids)).execute()

    @classmethod
    @DB.connection_context()
    def update_user(cls, user_id, user_dict):
        with DB.atomic():
            if user_dict:
                user_dict["update_time"] = current_timestamp()
                user_dict["update_date"] = timestamp_to_date(user_dict["update_time"])
                cls.model.update(user_dict).where(
                    cls.model.id == user_id).execute()

    @classmethod
    @DB.connection_context()
    def update_by_id(cls, user_id, user):
        user["update_time"] = current_timestamp()
        user["update_date"] = timestamp_to_date(user["update_time"])
        num = cls.model.update(**user).where(cls.model.id == user_id).execute()
        return num

    @classmethod
    @DB.connection_context()
    def update_access_token(cls, user_id, token):
        num = cls.model.update(access_token=token).where(
            cls.model.id == user_id).execute()
        return num

    @classmethod
    @DB.connection_context()
    def get_by_id(cls, user_id):
        try:
            obj = cls.model.get_by_id(user_id)
            return True, obj
        except Exception:
            return False, None

    @classmethod
    @DB.connection_context()
    def get_by_email(cls, email):
        objs = cls.model.select().where(cls.model.email == email)
        if objs:
            return objs[0]
        return None

    @classmethod
    @DB.connection_context()
    def delete_by_id(cls, user_id):
        return cls.model.delete().where(cls.model.id == user_id).execute()

    @classmethod
    @DB.connection_context()
    def get_all(cls):
        objs = cls.model.select()
        return list(objs.dicts())

    @classmethod
    @DB.connection_context()
    def is_superuser(cls, user_id):
        objs = cls.model.select().where(
            cls.model.id == user_id, cls.model.is_superuser == True)
        return len(objs) > 0


class TenantService(CommonService):
    model = Tenant

    @classmethod
    @DB.connection_context()
    def get_info_by(cls, user_id):
        fields = [
            cls.model.id.alias("tenant_id"),
            cls.model.name,
            cls.model.llm_id,
            cls.model.embd_id,
            cls.model.rerank_id,
            cls.model.asr_id,
            cls.model.img2txt_id,
            cls.model.tts_id,
            cls.model.parser_ids,
            UserTenant.role]
        return list(cls.model.select(*fields)
                    .join(UserTenant, on=((cls.model.id == UserTenant.tenant_id) & (UserTenant.user_id == user_id) & (UserTenant.status == StatusEnum.VALID.value) & (UserTenant.role == UserTenantRole.OWNER)))
                    .where(cls.model.status == StatusEnum.VALID.value).dicts())

    @classmethod
    @DB.connection_context()
    def get_joined_tenants_by_user_id(cls, user_id):
        fields = [
            cls.model.id.alias("tenant_id"),
            cls.model.name,
            cls.model.llm_id,
            cls.model.embd_id,
            cls.model.asr_id,
            cls.model.img2txt_id,
            UserTenant.role]
        return list(cls.model.select(*fields)
                    .join(UserTenant, on=((cls.model.id == UserTenant.tenant_id) & (UserTenant.user_id == user_id) & (UserTenant.status == StatusEnum.VALID.value) & (UserTenant.role != UserTenantRole.INVITE)))
                    .where(cls.model.status == StatusEnum.VALID.value).dicts())

    @classmethod
    @DB.connection_context()
    def decrease(cls, user_id, num):
        num = cls.model.update(credit=cls.model.credit - num).where(
            cls.model.id == user_id).execute()
        if num == 0:
            raise LookupError("Tenant not found which is supposed to be there")

    @classmethod
    @DB.connection_context()
    def user_gateway(cls, tenant_id):
        hashobj = hashlib.sha256(tenant_id.encode("utf-8"))
        return int(hashobj.hexdigest(), 16)%len(MINIO)


class UserTenantService(CommonService):
    model = UserTenant

    @classmethod
    @DB.connection_context()
    def save(cls, **kwargs):
        if "id" not in kwargs:
            kwargs["id"] = get_uuid()
        obj = cls.model(**kwargs).save(force_insert=True)
        return obj

    @classmethod
    @DB.connection_context()
    def get_by_tenant_id(cls, tenant_id):
        fields = [
            cls.model.user_id,
            cls.model.status,
            cls.model.role,
            User.nickname,
            User.email,
            User.avatar,
            User.is_authenticated,
            User.is_active,
            User.is_anonymous,
            User.status,
            User.update_date,
            User.is_superuser]
        return list(cls.model.select(*fields)
                    .join(User, on=((cls.model.user_id == User.id) & (cls.model.status == StatusEnum.VALID.value) & (cls.model.role != UserTenantRole.OWNER)))
                    .where(cls.model.tenant_id == tenant_id)
                    .dicts())

    @classmethod
    @DB.connection_context()
    def get_tenants_by_user_id(cls, user_id):
        fields = [
            cls.model.tenant_id,
            cls.model.role,
            User.nickname,
            User.email,
            User.avatar,
            User.update_date
        ]
        return list(cls.model.select(*fields)
                    .join(User, on=((cls.model.user_id == User.id) & (UserTenant.user_id == user_id) & (UserTenant.status == StatusEnum.VALID.value)))
                    .where(cls.model.status == StatusEnum.VALID.value).dicts())


class TeamApplicationService(CommonService):
    model = None  # 需要导入TeamApplication模型

    @classmethod
    @DB.connection_context()
    def save(cls, **kwargs):
        from api.db.db_models import TeamApplication
        cls.model = TeamApplication
        if "id" not in kwargs:
            kwargs["id"] = get_uuid()
        kwargs["create_time"] = current_timestamp()
        kwargs["create_date"] = timestamp_to_date(kwargs["create_time"])
        kwargs["update_time"] = current_timestamp()
        kwargs["update_date"] = timestamp_to_date(kwargs["update_time"])
        obj = cls.model(**kwargs).save(force_insert=True)
        return obj

    @classmethod
    @DB.connection_context()
    def get_pending_applications_by_tenant_ids(cls, tenant_ids):
        """获取指定团队的待审批申请"""
        from api.db.db_models import TeamApplication
        cls.model = TeamApplication
        if not tenant_ids:
            return []
        
        fields = [
            cls.model.id,
            cls.model.user_id,
            cls.model.tenant_id,
            cls.model.status,
            cls.model.message,
            cls.model.create_time,
            cls.model.create_date,
            User.nickname.alias("user_nickname"),
            User.email.alias("user_email"),
            Tenant.name.alias("tenant_name")
        ]
        
        return list(cls.model.select(*fields)
                    .join(User, on=(cls.model.user_id == User.id))
                    .join(Tenant, on=(cls.model.tenant_id == Tenant.id))
                    .where(
                        (cls.model.tenant_id.in_(tenant_ids)) &
                        (cls.model.status == "PENDING")
                    )
                    .order_by(cls.model.create_time.desc())
                    .dicts())

    @classmethod
    @DB.connection_context()
    def count_pending_applications_by_tenant_ids(cls, tenant_ids):
        """统计指定团队的待审批申请数量"""
        from api.db.db_models import TeamApplication
        cls.model = TeamApplication
        if not tenant_ids:
            return 0
        
        return cls.model.select().where(
            (cls.model.tenant_id.in_(tenant_ids)) &
            (cls.model.status == "PENDING")
        ).count()

    @classmethod
    @DB.connection_context()
    def check_existing_application(cls, user_id, tenant_id):
        """检查用户是否已经申请过该团队"""
        from api.db.db_models import TeamApplication
        cls.model = TeamApplication
        
        return cls.model.select().where(
            (cls.model.user_id == user_id) &
            (cls.model.tenant_id == tenant_id) &
            (cls.model.status == "PENDING")
        ).exists()

    @classmethod
    @DB.connection_context()
    def update_application_status(cls, application_id, status, processed_by):
        """更新申请状态"""
        from api.db.db_models import TeamApplication
        import datetime
        cls.model = TeamApplication
        
        current_time = current_timestamp()
        update_data = {
            "status": status,
            "processed_by": processed_by,
            "processed_time": datetime.datetime.now(),
            "update_time": current_time,
            "update_date": timestamp_to_date(current_time)
        }
        
        return cls.model.update(**update_data).where(cls.model.id == application_id).execute()
