#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Zebra与PeeWee的适配器
用于将现有的PeeWee ORM连接替换为Zebra代理连接
"""

import logging
import warnings
import pymysql
from playhouse.pool import PooledMySQLDatabase
from zebraproxyclient.config import ZebraConfig
from zebraproxyclient.api.sqlalchemy import create_engine as zebra_create_engine
from sqlalchemy.engine import Engine
from api.utils.config_loader import get_zebra_config

# 配置日志
logger = logging.getLogger(__name__)

# 禁用所有Zebra相关的警告日志
# logging.getLogger("zebraproxyclient").setLevel(logging.ERROR)
# logging.getLogger("Zebra").setLevel(logging.ERROR)
# logging.getLogger("ZebraProxy").setLevel(logging.ERROR)
# logging.getLogger("sqlalchemy").setLevel(logging.ERROR)
# logging.getLogger("sqlalchemy.engine").setLevel(logging.ERROR)

# # 也禁用与ZebraProxy相关的所有日志处理程序
# for name in logging.root.manager.loggerDict:
#     if any(keyword in name for keyword in ["zebra", "Zebra", "proxy", "Proxy", "sql", "SQL"]):
#         logging.getLogger(name).setLevel(logging.ERROR)

# # 过滤掉Python的标准警告
# warnings.filterwarnings("ignore")

# 创建一个自定义的日志过滤器类
# class ZebraWarningFilter(logging.Filter):
#     def filter(self, record):
#         # 尝试获取完整的消息内容
#         try:
#             message = record.getMessage()
#         except:
#             message = str(record.msg)
        
#         # 过滤掉包含特定关键词的日志消息，无论日志级别
#         keywords = ["empty appkey", "ZebraProxy", "Zebra.CrossRegion", "Zebra.Refresh", "Unchange"]
#         for keyword in keywords:
#             if keyword.lower() in message.lower():
#                 return False
                
#         # 额外过滤所有ZebraProxy相关的WARNING级别日志
#         if record.levelno == logging.WARNING and hasattr(record, 'name'):
#             if "zebra" in record.name.lower() or "proxy" in record.name.lower():
#                 return False
                
#         return True

# # 添加自定义过滤器到根日志处理器
# zebra_filter = ZebraWarningFilter()
# logging.root.addFilter(zebra_filter)

# # 也添加到所有现有的处理器
# for handler in logging.root.handlers:
#     handler.addFilter(zebra_filter)

# 使用PyMySQL作为MySQL驱动
pymysql.install_as_MySQLdb()


class ZebraPooledMySQLDatabase(PooledMySQLDatabase):
    """
    Zebra代理版本的PooledMySQLDatabase
    使用SQLAlchemy引擎创建底层连接
    """
    def __init__(self, database, ref_key=None, **kwargs):
        """
        初始化Zebra代理版本的PooledMySQLDatabase
        
        Args:
            database: 数据库名称，这个参数会被忽略，但为了兼容性需要保留
            ref_key: 数据库引用Key，如果为None则使用配置文件中的ref_key
            **kwargs: 额外的连接参数
        """
        # 获取Zebra配置
        zebra_config = get_zebra_config()
        
        if ref_key is None:
            ref_key = zebra_config.get("ref_key")
        
        # 获取应用Key
        app_key = zebra_config.get("app_key")
        
        # 获取连接池参数
        pool_size = zebra_config.get("pool_size", 100)
        pool_timeout = zebra_config.get("pool_timeout", 30)
        pool_recycle = zebra_config.get("pool_recycle", 3600)
        
        logger.info(f"创建ZebraPooledMySQLDatabase，应用: {app_key}, 引用: {ref_key}")
        
        # 创建Zebra配置
        self.zebra_config = ZebraConfig(
            appname=app_key,
            ref_key=ref_key
        )
        
        # 创建SQLAlchemy引擎
        self.engine = zebra_create_engine(
            self.zebra_config,
            pool_size=kwargs.pop('max_connections', pool_size),
            pool_timeout=kwargs.pop('stale_timeout', pool_timeout),
            pool_recycle=pool_recycle
        )
        
        # 保存数据库名称，虽然不会使用，但为了兼容性保留
        self.database = database
        
        # 设置连接池参数
        max_connections = kwargs.pop('max_connections', pool_size)
        stale_timeout = kwargs.pop('stale_timeout', pool_timeout)
        
        # 正确调用父类的初始化方法
        # 注意：不要跳过PooledMySQLDatabase的初始化
        super().__init__(
            database,
            max_connections=max_connections,
            stale_timeout=stale_timeout,
            **kwargs
        )
    
    def _connect(self, **kwargs):
        """
        创建一个数据库连接
        
        Returns:
            Connection: 数据库连接实例
        """
        # 从SQLAlchemy引擎获取原始连接
        connection = self.engine.raw_connection()
        
        # 直接返回原始连接，不尝试修改其cursor方法
        # Zebra代理的连接对象可能与标准PyMySQL连接不完全兼容
        return connection
    
    def execute_sql(self, sql, params=None, commit=True):
        """
        执行SQL语句
        
        Args:
            sql: SQL语句
            params: SQL参数
            commit: 是否提交事务
        
        Returns:
            cursor: 查询结果游标
        """
        try:
            cursor = self.cursor()
            cursor.execute(sql, params or ())
            if commit and not self.in_transaction():
                self.commit()
            return cursor
        except Exception as e:
            if self.in_transaction():
                self.rollback()
            raise e 