import json
import logging
import requests
import os
import re
from typing import Optional, Dict, Any, List, Union, Callable, Tuple
import time
from dataclasses import dataclass
from dotenv import load_dotenv
from bs4 import BeautifulSoup
import html2text

# 加载.env文件中的环境变量
load_dotenv()

logger = logging.getLogger(__name__)

class CitadelHttpLoader:
    """
    使用salesmind.sankuai.com API接口获取学城文档内容。
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        api_url: str = "https://salesmind.sankuai.com/v1/workflows/run",
        user: str = "rag_system",
        user_type: str = "employee",
    ):
        """
        初始化CitadelHttpLoader
        
        Args:
            api_key: API密钥，如果不提供则从环境变量CITADEL_API_KEY中获取
            api_url: 工作流API的URL
            user: 用户标识，用于定义终端用户的身份
            user_type: 用户类型，默认为'employee'
        """
        # 如果未提供API密钥，则从环境变量获取
        self.api_key = 'app-77emk9nVbuh89Ob6FMenxY8h'
        if not self.api_key:
            raise ValueError("必须提供API密钥或设置CITADEL_API_KEY环境变量")
            
        self.api_url = api_url
        self.response_mode = 'blocking'
        self.user = user
        self.user_type = user_type
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def load_document(self, doc_url: str = None, query: Optional[str] = None, inputs: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        从学城加载单个文档
        
        Args:
            doc_url: 学城文档URL（可选，如果提供则覆盖inputs中的url）
            query: 用户输入/提问内容
            inputs: 包含url字段的字典，指定要加载的文档URL
            
        Returns:
            包含文档内容的字典
            
        Raises:
            ValueError: 如果文档无法访问或需要权限
        """
        if inputs is None:
            inputs = {}
        
        # 如果doc_url参数提供，优先使用doc_url
        if doc_url:
            url = doc_url
            inputs["url"] = url
        # 否则使用inputs中的url字段
        elif "url" in inputs:
            url = inputs["url"]
        else:
            raise ValueError("必须提供doc_url参数或在inputs中包含url字段")
            
        logger.info(f"Sending request to load document: {url}")
        try:
            payload = {
                "inputs": inputs,
                "response_mode": self.response_mode,
                "user": self.user,
                "user_type": self.user_type
            }
            
            if query:
                payload["query"] = query
                
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=payload
            )
            response.raise_for_status()
            
            # 阻塞模式直接返回JSON响应
            return self._process_blocking_response(response.json(), url)
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Error loading document from Citadel: {e}")
            raise ValueError(f"请求学城文档API失败: {str(e)}")
        except ValueError as e:
            # 直接传递ValueError异常，其中包含了我们自定义的权限错误消息
            logger.error(f"处理学城文档失败: {e}")
            raise
        except Exception as e:
            logger.error(f"处理学城文档时出现未知错误: {e}")
            raise ValueError(f"处理学城文档时出现未知错误: {str(e)}")
    
    def _process_blocking_response(self, response_data: Dict[str, Any], doc_url: str) -> Dict[str, Any]:
        """
        处理阻塞模式API响应，确保只提取纯文档内容
        
        Args:
            response_data: API响应的JSON数据
            doc_url: 文档URL(用于元数据)
            
        Returns:
            处理后的文档内容
        """
        document_content = ""
        doc_meta = {}
        
        # 尝试处理多种可能的响应结构
        try:
            # 情况1: workflow_run_id结构 - 多层嵌套
            if "workflow_run_id" in response_data and "data" in response_data:
                if "outputs" in response_data["data"] and "text" in response_data["data"]["outputs"]:
                    # 嵌套在outputs.text中的JSON字符串
                    text_content = response_data["data"]["outputs"]["text"]
                    if text_content.strip().startswith('{'):
                        import json
                        inner_json = json.loads(text_content)
                        
                        # 检查错误码
                        if "code" in inner_json and inner_json["code"] == -1:
                            error_msg = inner_json.get("msg", "未知错误")
                            raise ValueError(f"访问文档失败: {error_msg}。请确保自动化账号test_salesmind有权限访问该文档。")
                        
                        if "data" in inner_json and "content" in inner_json["data"]:
                            document_content = inner_json["data"]["content"]
                            if "docMeta" in inner_json["data"]:
                                doc_meta = inner_json["data"]["docMeta"]
                            logger.info("从多层嵌套JSON中提取到内容")
            
            # 情况2: 直接的data.content结构
            if not document_content and "data" in response_data and isinstance(response_data["data"], dict):
                data = response_data["data"]
                
                # 检查嵌套的错误码
                if "outputs" in data and "text" in data["outputs"] and data["outputs"]["text"].strip().startswith('{'):
                    try:
                        import json
                        text_json = json.loads(data["outputs"]["text"])
                        if "code" in text_json and text_json["code"] == -1:
                            error_msg = text_json.get("msg", "未知错误")
                            raise ValueError(f"访问文档失败: {error_msg}。请确保自动化账号test_salesmind有权限访问该文档。")
                    except Exception as e:
                        logger.warning(f"解析嵌套JSON时出错: {str(e)}")
                
                # 提取文档内容 data.content
                if "content" in data and isinstance(data["content"], str):
                    document_content = data["content"]
                
                # 可选：提取文档元数据 data.docMeta
                if "docMeta" in data and isinstance(data["docMeta"], dict):
                    doc_meta = data["docMeta"]
        
        except Exception as e:
            logger.warning(f"处理JSON响应时出错: {str(e)}")
            raise e
            
        # 如果无法提取内容，记录警告
        if not document_content:
            logger.warning(f"无法从API响应中提取文档内容(data.content)，返回空字符串。URL: {doc_url}")
            # 记录完整响应以便调试
            logger.debug(f"API响应: {response_data}")
        
        # 构建结果
        result = {
            "content": document_content,
            "metadata": {
                "source": "citadel",
                "url": doc_url,
            }
        }
        
        # 添加文档元数据到结果中
        if doc_meta:
            result["metadata"]["doc_meta"] = doc_meta
        
        return result
          
    def load_documents(self, urls: List[str] = None, query: Optional[str] = None, inputs_list: Optional[List[Dict[str, Any]]] = None) -> List[Dict[str, Any]]:
        """
        批量加载多个学城文档
        
        Args:
            urls: 学城文档URL列表（可选，如果提供则覆盖inputs_list）
            query: 用户输入/提问内容
            inputs_list: 包含url字段的字典列表
            
        Returns:
            包含所有文档内容的列表
        """
        documents = []
        
        # 如果提供了urls列表，优先使用urls
        if urls:
            for url in urls:
                try:
                    doc = self.load_document(doc_url=url, query=query)
                    documents.append(doc)
                    # 避免API限流，添加短暂延迟
                    time.sleep(0.5)
                except Exception as e:
                    logger.error(f"Failed to load document {url}: {e}")
                    # 继续处理其他文档
                    continue
        # 否则使用inputs_list
        elif inputs_list:
            for inputs in inputs_list:
                try:
                    if "url" not in inputs:
                        logger.error(f"Missing 'url' field in inputs: {inputs}")
                        continue
                        
                    doc = self.load_document(inputs=inputs, query=query)
                    documents.append(doc)
                    # 避免API限流，添加短暂延迟
                    time.sleep(0.5)
                except Exception as e:
                    url = inputs.get("url", "unknown")
                    logger.error(f"Failed to load document {url}: {e}")
                    # 继续处理其他文档
                    continue
        else:
            raise ValueError("必须提供urls列表或inputs_list")
                
        return documents
    
    @staticmethod
    def clean_html(html_content: str, strategy: str = 'keep_structure') -> str:
        """
        清理HTML内容，根据不同策略处理
        
        Args:
            html_content: 包含HTML标签的内容
            strategy: 清理策略，可选:
                - 'extract_text': 完全提取文本内容，移除所有HTML结构
                - 'keep_structure': 保留基本结构（段落、标题），但移除格式标签
                - 'markdown': 将HTML转换为Markdown格式
                - 'none': 不进行处理
                
        Returns:
            处理后的内容
        """
        if not html_content or html_content.strip() == "":
            return ""
            
        # 检查内容是否包含HTML标签
        if "<" not in html_content and ">" not in html_content:
            return html_content
            
        if strategy == 'extract_text':
            # 使用BeautifulSoup提取纯文本
            soup = BeautifulSoup(html_content, 'html.parser')
            # 去除script和style元素
            for script in soup(["script", "style"]):
                script.extract()
            text = soup.get_text(separator="\n")
            # 删除多余空行
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = '\n'.join(chunk for chunk in chunks if chunk)
            return text
            
        elif strategy == 'keep_structure':
            # 保留基本结构
            soup = BeautifulSoup(html_content, 'html.parser')
            # 去除script和style元素
            for script in soup(["script", "style"]):
                script.extract()
            
            # 保留标题、段落、列表等结构
            for tag in soup.find_all():
                # 移除大多数属性，但保留标签结构
                attrs_to_keep = []
                for attr in list(tag.attrs.keys()):
                    if attr not in attrs_to_keep:
                        del tag[attr]
            
            return str(soup)
            
        elif strategy == 'markdown':
            # 将HTML转换为Markdown
            h = html2text.HTML2Text()
            h.ignore_links = False
            h.ignore_images = False
            h.ignore_tables = False
            h.ignore_emphasis = False
            return h.handle(html_content)
            
        else:  # 'none'
            return html_content

class CitadelLoader:
    """
    CitadelLoader的别名，与命名规范保持一致
    """
    def __new__(cls, *args, **kwargs):
        return CitadelHttpLoader(*args, **kwargs)

if __name__ == "__main__":
    import datetime
    import os
    import json
    from pathlib import Path
    
    # 创建输出目录
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"citadel_output_{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    
    # 主输出文件
    main_output_file = os.path.join(output_dir, "summary.txt")
    markdown_content_path = os.path.join(output_dir, "markdown_content.txt")
    
    # 定义同时输出到控制台和文件的函数
    def print_and_write(file, text, console_output=True):
        if console_output:
            print(text)
        file.write(text + "\n")
    
    with open(main_output_file, "w", encoding="utf-8") as f:
        print_and_write(f, "加载文档中，请稍候...")
        
        # 创建CitadelLoader实例并加载文档
        loader = CitadelLoader()
        doc_url = "https://km.sankuai.com/collabpage/2703860661"
        print_and_write(f, f"正在从{doc_url}加载文档...")
        
        # 加载文档 - CitadelLoader.load_document会自动处理JSON解析和内容提取
        doc = loader.load_document(doc_url=doc_url)
        
        # 获取处理后的内容和元数据
        content = doc.get('content', '')
        metadata = doc.get('metadata', {})
        
        # 保存内容到文件
        with open(markdown_content_path, "w", encoding="utf-8") as md_file:
            md_file.write(content)
        
        # 输出基本信息
        print_and_write(f, f"\n=== 文档信息 ===")
        print_and_write(f, f"文档URL: {metadata.get('url', '未知')}")
        print_and_write(f, f"内容已保存至: {markdown_content_path}")
        print_and_write(f, f"内容长度: {len(content)} 字符")
        
        print_and_write(f, f"\n处理完成! 内容已保存到: {output_dir}")