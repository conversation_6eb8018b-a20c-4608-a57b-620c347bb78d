#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

from flask import request
from flask_login import login_required, current_user
from uuid import uuid4

from api import settings
from api.db import UserTenantRole, StatusEnum
from api.db.db_models import User<PERSON>enant, Tenant, User
from api.db.services.user_service import UserTenantService, UserService, TeamApplicationService

from api.utils import get_uuid, delta_seconds
from api.utils.api_utils import get_json_result, validate_request, server_error_response, get_data_error_result


@manager.route("/<tenant_id>/user/list", methods=["GET"])  # noqa: F821
@login_required
def user_list(tenant_id):
    if current_user.id != tenant_id:
        return get_json_result(
            data=False,
            message='No authorization.',
            code=settings.RetCode.AUTHENTICATION_ERROR)

    try:
        users = UserTenantService.get_by_tenant_id(tenant_id)
        for u in users:
            u["delta_seconds"] = delta_seconds(str(u["update_date"]))
        return get_json_result(data=users)
    except Exception as e:
        return server_error_response(e)


@manager.route('/<tenant_id>/user', methods=['POST'])  # noqa: F821
@login_required
@validate_request("email")
def create(tenant_id):
    if current_user.id != tenant_id:
        return get_json_result(
            data=False,
            message='No authorization.',
            code=settings.RetCode.AUTHENTICATION_ERROR)

    req = request.json
    invite_user_email = req["email"]
    invite_users = UserService.query(email=invite_user_email)
    if not invite_users:
        return get_data_error_result(message="User not found.")

    user_id_to_invite = invite_users[0].id
    user_tenants = UserTenantService.query(user_id=user_id_to_invite, tenant_id=tenant_id)
    if user_tenants:
        user_tenant_role = user_tenants[0].role
        if user_tenant_role == UserTenantRole.NORMAL:
            return get_data_error_result(message=f"{invite_user_email} is already in the team.")
        if user_tenant_role == UserTenantRole.OWNER:
            return get_data_error_result(message=f"{invite_user_email} is the owner of the team.")
        return get_data_error_result(message=f"{invite_user_email} is in the team, but the role: {user_tenant_role} is invalid.")

    UserTenantService.save(
        id=get_uuid(),
        user_id=user_id_to_invite,
        tenant_id=tenant_id,
        invited_by=current_user.id,
        role=UserTenantRole.INVITE,
        status=StatusEnum.VALID.value)

    usr = invite_users[0].to_dict()
    usr = {k: v for k, v in usr.items() if k in ["id", "avatar", "email", "nickname"]}

    return get_json_result(data=usr)


@manager.route('/<tenant_id>/user/<user_id>', methods=['DELETE'])  # noqa: F821
@login_required
def rm(tenant_id, user_id):
    if current_user.id != tenant_id and current_user.id != user_id:
        return get_json_result(
            data=False,
            message='No authorization.',
            code=settings.RetCode.AUTHENTICATION_ERROR)

    try:
        UserTenantService.filter_delete([UserTenant.tenant_id == tenant_id, UserTenant.user_id == user_id])
        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route("/list", methods=["GET"])  # noqa: F821
@login_required
def tenant_list():
    try:
        users = UserTenantService.get_tenants_by_user_id(current_user.id)
        for u in users:
            u["delta_seconds"] = delta_seconds(str(u["update_date"]))
        return get_json_result(data=users)
    except Exception as e:
        return server_error_response(e)


@manager.route("/agree/<tenant_id>", methods=["PUT"])  # noqa: F821
@login_required
def agree(tenant_id):
    try:
        UserTenantService.filter_update([UserTenant.tenant_id == tenant_id, UserTenant.user_id == current_user.id], {"role": UserTenantRole.NORMAL})
        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


def enrich_team_with_creator_name(teams):
    """
    批量为团队对象（dict）增加creator_name字段
    """
    if not teams:
        return teams
    # 支持单个dict或list[dict]
    is_single = False
    if isinstance(teams, dict):
        teams = [teams]
        is_single = True
    creator_ids = {team["created_by"] for team in teams if team.get("created_by")}
    if creator_ids:
        users = User.select().where(User.id.in_(creator_ids))
        id2nickname = {u.id: u.nickname for u in users}
    else:
        id2nickname = {}
    for team in teams:
        creator_id = team.get("created_by")
        team["creator_name"] = id2nickname.get(creator_id, "") if creator_id else ""
    return teams[0] if is_single else teams


@manager.route("/all_list", methods=["GET"])  # 新增接口：获取所有团队
@login_required
def all_team_list():
    """
    获取所有团队列表，支持关键词模糊搜索，按创建时间倒序，支持分页。
    可选参数：keywords（模糊搜索团队名），page，page_size，user_id（可选，查自己创建和加入的团队）
    """
    keywords = request.args.get("keywords", "")
    user_id = request.args.get("user_id", None)
    try:
        page = int(request.args.get("page", 1))
        page_size = int(request.args.get("page_size", 20))
        if user_id:
            # 查自己创建和加入的团队
            # 1. 自己创建的
            created_query = Tenant.select().where((Tenant.created_by == user_id) & (Tenant.status == StatusEnum.VALID.value))
            # 2. 自己加入的
            joined_ids = [ut.tenant_id for ut in UserTenantService.model.select().where((UserTenantService.model.user_id == user_id) & (UserTenantService.model.status == StatusEnum.VALID.value))]
            joined_query = Tenant.select().where(Tenant.id.in_(joined_ids) & (Tenant.status == StatusEnum.VALID.value))
            query = created_query | joined_query
            #query.where(Tenant.status == StatusEnum.VALID.value)
            if keywords:
                query = query.where(Tenant.name.contains(keywords))
            total = query.count()
            query = query.order_by(Tenant.create_time.desc(nulls='LAST'))
            teams = [t.to_dict() for t in query.paginate(page, page_size)]
        else:
            query = Tenant.select().where(Tenant.status == StatusEnum.VALID.value)
            if keywords:
                query = query.where(Tenant.name.contains(keywords))
            total = query.count()
            query = query.order_by(Tenant.create_time.desc(nulls='LAST'))
            teams = [t.to_dict() for t in query.paginate(page, page_size)]
        teams = enrich_team_with_creator_name(teams)
        
        # 获取当前用户的所有申请记录
        from api.db.db_models import TeamApplication
        user_applications = {}
        if teams:
            team_ids = [team['id'] for team in teams]
            applications = TeamApplication.select().where(
                (TeamApplication.user_id == current_user.id) &
                (TeamApplication.tenant_id.in_(team_ids)) &
                (TeamApplication.status == "PENDING")
            )
            user_applications = {app.tenant_id: app.status for app in applications}
        
        # 增加 current_user_role 和 application_status 字段
        for team in teams:
            if str(team.get('created_by')) == str(current_user.id):
                team['current_user_role'] = 'OWNER'
                team['application_status'] = None
            else:
                ut = UserTenant.get_or_none((UserTenant.tenant_id == team['id']) & (UserTenant.user_id == current_user.id))
                team['current_user_role'] = ut.role if ut else ''
                # 设置申请状态
                team['application_status'] = user_applications.get(team['id'], None)
                
        return get_json_result(data={"total": total, "teams": teams})
    except Exception as e:
        return server_error_response(e)


@manager.route("/create", methods=["POST"])
@login_required
@validate_request("name")
def create_team():
    """
    创建团队，必填name，自动生成id和created_by。
    """
    from api.db.services.file_service import FileService
    from api.db import FileType
    from api.db.db_models import TenantLLM
    req = request.json
    try:
        team_id = uuid4().hex
        name = req["name"].strip()
        if not name:
            return get_data_error_result(message="团队名称不能为空")
        # 检查重名
        if Tenant.select().where(Tenant.name == name).exists():
            return get_data_error_result(message="团队名称已存在")
        now = int(__import__('time').time())
        import datetime
        # 1. 初始化 tenant 字段（参考 user_register）
        tenant = {
            "id": team_id,
            "name": name,
            "created_by": current_user.id,
            "status": '1',
            "create_time": now,
            "create_date": datetime.datetime.now(),
            "llm_id": getattr(settings, 'CHAT_MDL', None),
            "embd_id": getattr(settings, 'EMBEDDING_MDL', None),
            "asr_id": getattr(settings, 'ASR_MDL', None),
            "parser_ids": getattr(settings, 'PARSERS', None),
            "img2txt_id": getattr(settings, 'IMAGE2TEXT_MDL', None),
            "rerank_id": getattr(settings, 'RERANK_MDL', None),
        }
        Tenant.insert(**tenant).execute()

        # 2. 创建 OWNER 成员
        UserTenantService.save(
            id=uuid4().hex,
            user_id=current_user.id,
            tenant_id=team_id,
            invited_by=current_user.id,
            role=UserTenantRole.OWNER,
            status=StatusEnum.VALID.value
        )

        # 3. 初始化根文件夹
        file_id = get_uuid()
        file = {
            "id": file_id,
            "parent_id": file_id,
            "tenant_id": team_id,
            "created_by": current_user.id,
            "name": "/",
            "type": FileType.FOLDER.value,
            "size": 0,
            "location": "",
        }
        FileService.insert(file)

        # 4. 初始化 TenantLLM（如有默认配置）
        tenant_llm = []
        default_llm = getattr(settings, 'LLM', {}) or {}
        default_factory = default_llm.get("factory", "")
        default_api_key = default_llm.get("api_key", "")
        default_base_url = default_llm.get("base_url", "")
        default_models = default_llm.get("default_models", {})
        from api.db.services.llm_service import LLMService, TenantLLMService
        if default_factory and default_api_key:
            factory_models = LLMService.query(fid=default_factory)
            chat_model_name = default_models.get("chat_model")
            if chat_model_name:
                chat_model = next((m for m in factory_models if m.llm_name == chat_model_name), None)
                if chat_model:
                    tenant_llm.append({
                        "tenant_id": team_id,
                        "llm_factory": default_factory,
                        "llm_name": chat_model_name,
                        "model_type": chat_model.model_type,
                        "api_key": default_api_key,
                        "api_base": default_base_url,
                        "max_tokens": getattr(chat_model, 'max_tokens', 8192)
                    })
                    # 设置为默认聊天模型
                    Tenant.update(llm_id=f"{chat_model_name}@{default_factory}").where(Tenant.id == team_id).execute()
            embedding_model_name = default_models.get("embedding_model")
            if embedding_model_name:
                embedding_model = next((m for m in factory_models if m.llm_name == embedding_model_name), None)
                if embedding_model:
                    tenant_llm.append({
                        "tenant_id": team_id,
                        "llm_factory": default_factory,
                        "llm_name": embedding_model_name,
                        "model_type": embedding_model.model_type,
                        "api_key": default_api_key,
                        "api_base": default_base_url,
                        "max_tokens": getattr(embedding_model, 'max_tokens', 8192)
                    })
                    # 设置为默认嵌入模型
                    Tenant.update(embd_id=f"{embedding_model_name}@{default_factory}").where(Tenant.id == team_id).execute()
            if tenant_llm:
                TenantLLMService.insert_many(tenant_llm)
        return get_json_result(data={"id": team_id, "name": name})
    except Exception as e:
        return server_error_response(e)


@manager.route("/update", methods=["POST"])
@login_required
@validate_request("id", "name")
def update_team():
    """
    编辑团队名称
    """
    req = request.json
    try:
        team_id = req["id"]
        name = req["name"].strip()
        if not name:
            return get_data_error_result(message="团队名称不能为空")
        # 检查重名
        if Tenant.select().where((Tenant.name == name) & (Tenant.id != team_id)).exists():
            return get_data_error_result(message="团队名称已存在")
        updated = Tenant.update(name=name, update_time=int(__import__('time').time()), update_date=__import__('datetime').datetime.now()).where(Tenant.id == team_id).execute()
        if not updated:
            return get_data_error_result(message="团队不存在")
        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route("/offline", methods=["POST"])
@login_required
@validate_request("id")
def offline_team():
    """
    下线团队（软删除，status=0）
    """
    req = request.json
    try:
        team_id = req["id"]
        updated = Tenant.update(status='0', update_time=int(__import__('time').time()), update_date=__import__('datetime').datetime.now()).where(Tenant.id == team_id).execute()
        if not updated:
            return get_data_error_result(message="团队不存在")
        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route("/detail", methods=["GET"])
@login_required
def team_detail():
    """
    获取团队详情，包括基本信息和成员列表
    参数：id（团队id）
    """
    team_id = request.args.get("id")
    if not team_id:
        return get_data_error_result(message="缺少团队id")
    try:
        team = Tenant.get_or_none(Tenant.id == team_id)
        if not team:
            return get_data_error_result(message="团队不存在")
        team_dict = team.to_dict()
        team_dict = enrich_team_with_creator_name(team_dict)
        members = UserTenantService.get_by_tenant_id(team_id)
        return get_json_result(data={
            "team": team_dict,
            "members": members
        })
    except Exception as e:
        return server_error_response(e)


@manager.route("/member/add", methods=["POST"])
@login_required
@validate_request("tenant_id", "email", "role")
def add_team_member():
    """
    添加团队成员，参数：tenant_id、email、role
    """
    req = request.json
    try:
        tenant_id = req["tenant_id"]
        email = req["email"]
        role = req["role"]
        users = UserService.query(email=email)
        if not users:
            return get_data_error_result(message="用户不存在")
        user_id = users[0].id
        # 检查是否已在团队
        if UserTenantService.query(user_id=user_id, tenant_id=tenant_id):
            return get_data_error_result(message="该用户已在团队中")
        UserTenantService.save(
            id=uuid4().hex,
            user_id=user_id,
            tenant_id=tenant_id,
            invited_by=current_user.id,
            role=role,
            status=StatusEnum.VALID.value
        )
        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route("/member/remove", methods=["POST"])
@login_required
@validate_request("tenant_id", "user_id")
def remove_team_member():
    """
    移除团队成员，参数：tenant_id、user_id
    """
    req = request.json
    try:
        tenant_id = req["tenant_id"]
        user_id = req["user_id"]
        UserTenantService.filter_delete([UserTenant.tenant_id == tenant_id, UserTenant.user_id == user_id])
        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route("/member/role", methods=["POST"])
@login_required
@validate_request("tenant_id", "user_id", "role")
def set_team_member_role():
    """
    修改团队成员角色，参数：tenant_id、user_id、role
    """
    req = request.json
    try:
        tenant_id = req["tenant_id"]
        user_id = req["user_id"]
        role = req["role"]
        UserTenantService.filter_update([UserTenant.tenant_id == tenant_id, UserTenant.user_id == user_id], {"role": role})
        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route("/apply", methods=["POST"])
@login_required
@validate_request("tenant_id")
def apply_to_team():
    """
    申请加入团队
    """
    req = request.json
    try:
        tenant_id = req["tenant_id"]
        message = req.get("message", "")
        
        # 检查团队是否存在
        team = Tenant.get_or_none(Tenant.id == tenant_id)
        if not team:
            return get_data_error_result(message="团队不存在")
        
        # 检查用户是否已经是团队成员
        existing_member = UserTenant.get_or_none(
            (UserTenant.tenant_id == tenant_id) & 
            (UserTenant.user_id == current_user.id) &
            (UserTenant.status == StatusEnum.VALID.value)
        )
        if existing_member:
            return get_data_error_result(message="您已经是该团队的成员")
        
        # 检查是否已有待审批的申请
        if TeamApplicationService.check_existing_application(current_user.id, tenant_id):
            return get_data_error_result(message="您已经申请过该团队，请等待审批")
        
        # 创建申请记录
        TeamApplicationService.save(
            user_id=current_user.id,
            tenant_id=tenant_id,
            message=message,
            status="PENDING"
        )
        
        return get_json_result(data=True, message="申请已提交，请等待审批")
    except Exception as e:
        return server_error_response(e)


@manager.route("/applications/count", methods=["GET"])
@login_required
def get_applications_count():
    """
    获取当前用户管理的团队的待审批申请数量
    """
    try:
        # 获取用户创建和管理的团队ID列表
        managed_tenant_ids = []
        
        # 1. 用户创建的团队
        created_tenants = Tenant.select().where(
            (Tenant.created_by == current_user.id) & 
            (Tenant.status == StatusEnum.VALID.value)
        )
        managed_tenant_ids.extend([t.id for t in created_tenants])
        
        # 2. 用户作为ADMIN管理的团队
        admin_tenants = UserTenant.select().where(
            (UserTenant.user_id == current_user.id) &
            (UserTenant.role == UserTenantRole.ADMIN) &
            (UserTenant.status == StatusEnum.VALID.value)
        )
        managed_tenant_ids.extend([ut.tenant_id for ut in admin_tenants])
        
        # 去重
        managed_tenant_ids = list(set(managed_tenant_ids))
        
        # 统计待审批申请数量
        count = TeamApplicationService.count_pending_applications_by_tenant_ids(managed_tenant_ids)
        
        return get_json_result(data={"count": count})
    except Exception as e:
        return server_error_response(e)


@manager.route("/applications", methods=["GET"])
@login_required
def get_applications():
    """
    获取当前用户管理的团队的待审批申请列表
    """
    try:
        # 获取用户创建和管理的团队ID列表
        managed_tenant_ids = []
        
        # 1. 用户创建的团队
        created_tenants = Tenant.select().where(
            (Tenant.created_by == current_user.id) & 
            (Tenant.status == StatusEnum.VALID.value)
        )
        managed_tenant_ids.extend([t.id for t in created_tenants])
        
        # 2. 用户作为ADMIN管理的团队
        admin_tenants = UserTenant.select().where(
            (UserTenant.user_id == current_user.id) &
            (UserTenant.role == UserTenantRole.ADMIN) &
            (UserTenant.status == StatusEnum.VALID.value)
        )
        managed_tenant_ids.extend([ut.tenant_id for ut in admin_tenants])
        
        # 去重
        managed_tenant_ids = list(set(managed_tenant_ids))
        
        # 获取待审批申请列表
        applications = TeamApplicationService.get_pending_applications_by_tenant_ids(managed_tenant_ids)
        
        # 格式化时间
        for app in applications:
            if app.get("create_date"):
                app["delta_seconds"] = delta_seconds(str(app["create_date"]))
        
        return get_json_result(data=applications)
    except Exception as e:
        return server_error_response(e)


@manager.route("/applications/process", methods=["POST"])
@login_required
@validate_request("application_id", "action")
def process_application():
    """
    处理团队申请（同意或拒绝）
    """
    req = request.json
    try:
        application_id = req["application_id"]
        action = req["action"]  # "approve" 或 "reject"
        
        if action not in ["approve", "reject"]:
            return get_data_error_result(message="无效的操作")
        
        # 获取申请信息
        from api.db.db_models import TeamApplication
        application = TeamApplication.get_or_none(TeamApplication.id == application_id)
        if not application:
            return get_data_error_result(message="申请不存在")
        
        if application.status != "PENDING":
            return get_data_error_result(message="该申请已被处理")
        
        # 检查当前用户是否有权限处理该申请
        tenant_id = application.tenant_id
        has_permission = False
        
        # 检查是否是团队创建者
        team = Tenant.get_or_none(Tenant.id == tenant_id)
        if team and team.created_by == current_user.id:
            has_permission = True
        
        # 检查是否是团队管理员
        if not has_permission:
            admin_relation = UserTenant.get_or_none(
                (UserTenant.tenant_id == tenant_id) &
                (UserTenant.user_id == current_user.id) &
                (UserTenant.role == UserTenantRole.ADMIN) &
                (UserTenant.status == StatusEnum.VALID.value)
            )
            if admin_relation:
                has_permission = True
        
        if not has_permission:
            return get_json_result(
                data=False,
                message='无权限处理该申请',
                code=settings.RetCode.AUTHENTICATION_ERROR
            )
        
        # 更新申请状态
        new_status = "APPROVED" if action == "approve" else "REJECTED"
        TeamApplicationService.update_application_status(
            application_id, 
            new_status, 
            current_user.id
        )
        
        # 如果同意申请，将用户添加到团队
        if action == "approve":
            # 检查用户是否已经是团队成员（防止重复添加）
            existing_member = UserTenant.get_or_none(
                (UserTenant.tenant_id == tenant_id) & 
                (UserTenant.user_id == application.user_id) &
                (UserTenant.status == StatusEnum.VALID.value)
            )
            
            if not existing_member:
                UserTenantService.save(
                    user_id=application.user_id,
                    tenant_id=tenant_id,
                    invited_by=current_user.id,
                    role=UserTenantRole.NORMAL,
                    status=StatusEnum.VALID.value
                )
        
        message = "申请已同意" if action == "approve" else "申请已拒绝"
        return get_json_result(data=True, message=message)
    except Exception as e:
        return server_error_response(e)


@manager.route("/quit", methods=["POST"])
@login_required
@validate_request("tenant_id")
def quit_team():
    """
    退出团队
    """
    req = request.json
    try:
        tenant_id = req["tenant_id"]
        
        # 检查团队是否存在
        team = Tenant.get_or_none(Tenant.id == tenant_id)
        if not team:
            return get_data_error_result(message="团队不存在")
        
        # 检查用户是否是团队创建者（创建者不能退出）
        if team.created_by == current_user.id:
            return get_data_error_result(message="团队创建者不能退出团队")
        
        # 检查用户是否是团队成员
        existing_member = UserTenant.get_or_none(
            (UserTenant.tenant_id == tenant_id) & 
            (UserTenant.user_id == current_user.id) &
            (UserTenant.status == StatusEnum.VALID.value)
        )
        if not existing_member:
            return get_data_error_result(message="您不是该团队的成员")
        
        # 删除用户团队关系
        UserTenantService.filter_delete([
            UserTenant.tenant_id == tenant_id, 
            UserTenant.user_id == current_user.id
        ])
        
        return get_json_result(data=True, message="已成功退出团队")
    except Exception as e:
        return server_error_response(e)
