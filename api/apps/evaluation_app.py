"""
RAG评估API接口
提供评估数据集管理、任务执行、结果查看等功能
"""

import json
import logging
from flask import Blueprint, request
from flask_login import login_required, current_user
from datetime import datetime

from api.db.services.user_service import UserTenantService
from api.utils.api_utils import get_json_result, validate_request
from api import settings
from rag.evaluation import RagasEvaluator, DatasetManager, EvaluationRunner
from api.db.db_models import EvaluationTask, DB

# 创建蓝图
manager = Blueprint('evaluation_app', __name__, url_prefix='/v1/evaluation')


def get_tenant_id():
    """获取当前用户的租户ID"""
    tenants = UserTenantService.query(user_id=current_user.id)
    if not tenants:
        raise ValueError("用户没有关联的租户")
    return tenants[0].tenant_id


# ==================== 数据集管理 ====================

@manager.route('/datasets', methods=['POST'])
@login_required
@validate_request("name", "kb_id")
def create_dataset():
    """创建评估数据集"""
    try:
        req = request.json
        tenant_id = get_tenant_id()

        dataset_manager = DatasetManager(tenant_id)
        dataset_id = dataset_manager.create_dataset(
            name=req["name"],
            kb_id=req["kb_id"],
            created_by=current_user.id,
            description=req.get("description"),
            dataset_type=req.get("dataset_type", "qa_pairs")
        )

        return get_json_result(data={"dataset_id": dataset_id})
    except Exception as e:
        logging.error(f"创建数据集失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


@manager.route('/datasets', methods=['GET'])
@login_required
def list_datasets():
    """列出评估数据集"""
    try:
        tenant_id = get_tenant_id()
        kb_id = request.args.get('kb_id')

        dataset_manager = DatasetManager(tenant_id)
        # 处理None值，只有在有值时才传递参数
        if kb_id:
            datasets = dataset_manager.list_datasets(kb_id=kb_id)
        else:
            datasets = dataset_manager.list_datasets()

        return get_json_result(data=datasets)
    except Exception as e:
        logging.error(f"获取数据集列表失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


@manager.route('/datasets/<dataset_id>', methods=['GET'])
@login_required
def get_dataset(dataset_id):
    """获取数据集详情"""
    try:
        tenant_id = get_tenant_id()

        dataset_manager = DatasetManager(tenant_id)
        dataset = dataset_manager.get_dataset(dataset_id)

        if not dataset:
            return get_json_result(code=settings.RetCode.NOT_FOUND, message="数据集不存在")

        return get_json_result(data={
            "id": dataset.id,
            "name": dataset.name,
            "description": dataset.description,
            "kb_id": dataset.kb_id,
            "dataset_type": dataset.dataset_type,
            "total_samples": dataset.total_samples,
            "created_by": dataset.created_by,
            "create_time": dataset.create_time,
            "update_time": dataset.update_time
        })
    except Exception as e:
        logging.error(f"获取数据集详情失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


@manager.route('/datasets/<dataset_id>', methods=['DELETE'])
@login_required
def delete_dataset(dataset_id):
    """删除数据集"""
    try:
        tenant_id = get_tenant_id()

        dataset_manager = DatasetManager(tenant_id)
        success = dataset_manager.delete_dataset(dataset_id)

        if not success:
            return get_json_result(code=settings.RetCode.NOT_FOUND, message="数据集不存在")

        return get_json_result(data={"success": True})
    except Exception as e:
        logging.error(f"删除数据集失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


# ==================== 样本管理 ====================

@manager.route('/datasets/<dataset_id>/samples', methods=['POST'])
@login_required
@validate_request("question")
def add_sample(dataset_id):
    """添加评估样本"""
    try:
        req = request.json
        tenant_id = get_tenant_id()

        dataset_manager = DatasetManager(tenant_id)
        sample_id = dataset_manager.add_sample(
            dataset_id=dataset_id,
            question=req["question"],
            ground_truth=req.get("ground_truth"),
            contexts=req.get("contexts"),
            metadata=req.get("metadata")
        )

        return get_json_result(data={"sample_id": sample_id})
    except Exception as e:
        logging.error(f"添加样本失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


@manager.route('/datasets/<dataset_id>/samples', methods=['GET'])
@login_required
def get_samples(dataset_id):
    """获取数据集样本"""
    try:
        tenant_id = get_tenant_id()
        limit = request.args.get('limit', type=int)
        offset = request.args.get('offset', type=int, default=0)

        dataset_manager = DatasetManager(tenant_id)
        samples = dataset_manager.get_samples(dataset_id, limit=limit, offset=offset)

        # 转换为字典格式
        samples_data = []
        for sample in samples:
            samples_data.append({
                "question": sample.question,
                "ground_truth": sample.ground_truth,
                "contexts": sample.contexts,
                "metadata": sample.metadata
            })

        return get_json_result(data=samples_data)
    except Exception as e:
        logging.error(f"获取样本失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


@manager.route('/datasets/<dataset_id>/import', methods=['POST'])
@login_required
@validate_request("format", "content")
def import_samples(dataset_id):
    """导入样本"""
    try:
        req = request.json
        tenant_id = get_tenant_id()

        dataset_manager = DatasetManager(tenant_id)

        if req["format"] == "csv":
            imported_count = dataset_manager.import_from_csv(
                dataset_id=dataset_id,
                csv_content=req["content"],
                question_col=req.get("question_col", "question"),
                ground_truth_col=req.get("ground_truth_col", "ground_truth"),
                contexts_col=req.get("contexts_col")
            )
        elif req["format"] == "json":
            imported_count = dataset_manager.import_from_json(
                dataset_id=dataset_id,
                json_content=req["content"]
            )
        else:
            return get_json_result(code=settings.RetCode.ARGUMENT_ERROR, message="不支持的格式")

        return get_json_result(data={"imported_count": imported_count})
    except Exception as e:
        logging.error(f"导入样本失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


@manager.route('/datasets/<dataset_id>/export', methods=['GET'])
@login_required
def export_samples(dataset_id):
    """导出样本"""
    try:
        tenant_id = get_tenant_id()
        format_type = request.args.get('format', 'json')

        dataset_manager = DatasetManager(tenant_id)

        if format_type == "csv":
            content = dataset_manager.export_to_csv(dataset_id)
        elif format_type == "json":
            content = dataset_manager.export_to_json(dataset_id)
        else:
            return get_json_result(code=settings.RetCode.ARGUMENT_ERROR, message="不支持的格式")

        return get_json_result(data={"content": content, "format": format_type})
    except Exception as e:
        logging.error(f"导出样本失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


# ==================== 评估任务 ====================

@manager.route('/tasks', methods=['POST'])
@login_required
@validate_request("name", "dataset_id", "kb_id", "metrics")
def create_task():
    """创建评估任务"""
    try:
        req = request.json
        tenant_id = get_tenant_id()

        runner = EvaluationRunner(tenant_id)
        task_id = runner.create_task(
            name=req["name"],
            dataset_id=req["dataset_id"],
            kb_id=req["kb_id"],
            created_by=current_user.id,
            metrics=req["metrics"],
            llm_id=req.get("llm_id"),  # 用户选择的LLM模型
            retrieval_config=req.get("retrieval_config"),
            llm_config=req.get("llm_config")
        )

        return get_json_result(data={"task_id": task_id})
    except Exception as e:
        logging.error(f"创建评估任务失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


@manager.route('/tasks', methods=['GET'])
@login_required
def list_tasks():
    """列出评估任务"""
    try:
        tenant_id = get_tenant_id()
        kb_id = request.args.get('kb_id')
        status = request.args.get('status')

        runner = EvaluationRunner(tenant_id)
        # 处理None值，只有在有值时才传递参数
        kwargs = {}
        if kb_id:
            kwargs['kb_id'] = kb_id
        if status:
            kwargs['status'] = status

        tasks = runner.list_tasks(**kwargs)

        return get_json_result(data=tasks)
    except Exception as e:
        logging.error(f"获取任务列表失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


@manager.route('/tasks/<task_id>', methods=['GET'])
@login_required
def get_task(task_id):
    """获取任务详情"""
    try:
        tenant_id = get_tenant_id()

        runner = EvaluationRunner(tenant_id)
        task = runner.get_task(task_id)

        if not task:
            return get_json_result(code=settings.RetCode.NOT_FOUND, message="任务不存在")

        return get_json_result(data={
            "id": task.id,
            "name": task.name,
            "dataset_id": task.dataset_id,
            "kb_id": task.kb_id,
            "status": task.status,
            "progress": task.progress,
            "total_samples": task.total_samples,
            "processed_samples": task.processed_samples,
            "metrics": json.loads(task.metrics),
            "retrieval_config": json.loads(task.retrieval_config or "{}"),
            "llm_config": json.loads(task.llm_config or "{}"),
            "created_by": task.created_by,
            "create_time": task.create_time,
            "started_at": task.started_at,
            "completed_at": task.completed_at
        })
    except Exception as e:
        logging.error(f"获取任务详情失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


@manager.route('/tasks/<task_id>/start', methods=['POST'])
@login_required
def start_task(task_id):
    """启动评估任务"""
    try:
        tenant_id = get_tenant_id()
        max_workers = request.json.get('max_workers', 3) if request.json else 3

        runner = EvaluationRunner(tenant_id)
        success = runner.start_task(task_id, max_workers=max_workers)

        if not success:
            return get_json_result(code=settings.RetCode.OPERATING_ERROR, message="任务启动失败")

        return get_json_result(data={"success": True})
    except Exception as e:
        logging.error(f"启动任务失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


@manager.route('/tasks/<task_id>/stop', methods=['POST'])
@login_required
def stop_task(task_id):
    """停止评估任务"""
    try:
        tenant_id = get_tenant_id()

        # 获取任务信息
        try:
            task = EvaluationTask.get(EvaluationTask.id == task_id, EvaluationTask.tenant_id == tenant_id)
        except EvaluationTask.DoesNotExist:
            return get_json_result(code=settings.RetCode.NOT_FOUND, message="任务不存在")

        if task.status != 'running':
            return get_json_result(code=settings.RetCode.OPERATING_ERROR, message="只能停止正在运行的任务")

        # 更新任务状态
        task.status = 'stopped'
        task.update_time = datetime.now()

        try:
            task.save()
            logging.info(f"任务 {task_id} 已停止")
            return get_json_result(data={"task_id": task_id, "status": "stopped"})
        except Exception as e:
            logging.error(f"停止任务失败: {e}")
            return get_json_result(code=settings.RetCode.SERVER_ERROR, message="停止任务失败")

    except Exception as e:
        logging.error(f"停止任务失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


@manager.route('/tasks/<task_id>/restart', methods=['POST'])
@login_required
def restart_task(task_id):
    """重新运行评估任务"""
    try:
        tenant_id = get_tenant_id()
        max_workers = request.json.get('max_workers', 3) if request.json else 3

        runner = EvaluationRunner(tenant_id)

        # 获取任务信息
        task = runner.get_task(task_id)
        if not task:
            return get_json_result(code=settings.RetCode.NOT_FOUND, message="任务不存在")

        # 检查任务状态，只有已完成、失败或停止的任务才能重新运行
        if task.status == 'running':
            return get_json_result(code=settings.RetCode.OPERATING_ERROR, message="任务正在运行中，无法重新运行")

        if task.status == 'pending':
            return get_json_result(code=settings.RetCode.OPERATING_ERROR, message="任务尚未开始，请直接启动")

        # 重置任务状态和进度
        success = runner.restart_task(task_id, max_workers=max_workers)

        if not success:
            return get_json_result(code=settings.RetCode.OPERATING_ERROR, message="任务重新运行失败")

        return get_json_result(data={"success": True, "message": "任务已重新开始运行"})
    except Exception as e:
        logging.error(f"重新运行任务失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


@manager.route('/tasks/<task_id>', methods=['DELETE'])
@login_required
def delete_task(task_id):
    """删除评估任务"""
    try:
        tenant_id = get_tenant_id()

        runner = EvaluationRunner(tenant_id)
        success = runner.delete_task(task_id)

        if not success:
            return get_json_result(code=settings.RetCode.NOT_FOUND, message="任务不存在")

        return get_json_result(data={"success": True})
    except Exception as e:
        logging.error(f"删除任务失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


# ==================== 评估结果 ====================

@manager.route('/tasks/<task_id>/results', methods=['GET'])
@login_required
def get_task_results(task_id):
    """获取任务评估结果"""
    try:
        tenant_id = get_tenant_id()
        limit = request.args.get('limit', type=int)
        offset = request.args.get('offset', type=int, default=0)

        runner = EvaluationRunner(tenant_id)

        # 获取任务信息
        task = runner.get_task(task_id)
        if not task:
            return get_json_result(code=settings.RetCode.NOT_FOUND, message="任务不存在")

        # 获取评估结果
        results = runner.get_task_results(task_id, limit=limit, offset=offset)

        # 获取评估报告
        report = runner.get_task_report(task_id)

        # 计算汇总指标
        if report and report.get("summary"):
            summary_metrics = report["summary"]
        else:
            # 如果没有报告，从结果中计算
            summary_metrics = {}
            if results:
                metrics_sum = {}
                valid_count = 0
                for result in results:
                    if result.get("metrics") and not result.get("error_message"):
                        valid_count += 1
                        for key, value in result["metrics"].items():
                            if value is not None:
                                metrics_sum[key] = metrics_sum.get(key, 0) + value

                if valid_count > 0:
                    for key, total in metrics_sum.items():
                        summary_metrics[f"avg_{key}"] = total / valid_count

        response_data = {
            "task_id": task_id,
            "task_status": task.status,
            "metrics": summary_metrics,
            "details": results,
            "samples_count": len(results),
            "total_samples": task.total_samples,
            "processed_samples": task.processed_samples,
            "progress": task.progress
        }

        return get_json_result(data=response_data)
    except Exception as e:
        logging.error(f"获取任务结果失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


@manager.route('/tasks/<task_id>/report', methods=['GET'])
@login_required
def get_task_report(task_id):
    """获取任务评估报告"""
    try:
        tenant_id = get_tenant_id()

        runner = EvaluationRunner(tenant_id)
        report = runner.get_task_report(task_id)

        if not report:
            return get_json_result(code=settings.RetCode.NOT_FOUND, message="报告不存在")

        return get_json_result(data=report)
    except Exception as e:
        logging.error(f"获取任务报告失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


# ==================== 工具接口 ====================

@manager.route('/metrics', methods=['GET'])
@login_required
def get_available_metrics():
    """获取可用的评估指标"""
    try:
        metrics = RagasEvaluator.get_available_metrics()
        return get_json_result(data={"metrics": metrics})
    except Exception as e:
        logging.error(f"获取可用指标失败: {e}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))


@manager.route('/llms', methods=['GET'])
@login_required
def get_available_llms():
    """获取可用的LLM模型（用于评估）"""
    try:
        tenant_id = get_tenant_id()
        kb_id = request.args.get('kb_id')  # 可选的知识库ID参数

        # 获取租户的Chat类型LLM模型
        from api.db.db_models import TenantLLM, LLM
        from api.db import LLMType

        # 通过TenantLLM表获取租户的模型
        query = (TenantLLM
                .select(TenantLLM, LLM)
                .join(LLM, on=(
                    (TenantLLM.llm_factory == LLM.fid) &
                    (TenantLLM.llm_name == LLM.llm_name)
                ))
                .where(
                    (TenantLLM.tenant_id == tenant_id) &
                    (LLM.model_type == LLMType.CHAT.value) &
                    (LLM.status == 1)  # 启用状态
                ))

        models = []
        for tenant_llm in query:
            # 生成模型ID（格式：model_name@factory）
            model_id = f"{tenant_llm.llm_name}@{tenant_llm.llm_factory}"

            models.append({
                "id": model_id,
                "llm_name": tenant_llm.llm_name,
                "llm_factory": tenant_llm.llm_factory,
                "model_type": tenant_llm.model_type,
                "api_base": tenant_llm.api_base,
                "max_tokens": tenant_llm.max_tokens
            })

        # 如果指定了知识库ID，可以在响应中包含知识库的默认模型信息
        kb_info = None
        if kb_id:
            from api.db.services.knowledgebase_service import KnowledgebaseService
            success, kb = KnowledgebaseService.get_by_id(kb_id)
            if success and kb:
                kb_info = {
                    "kb_id": kb_id,
                    "embedding_model": kb.embd_id,
                    "name": kb.name
                }

        return get_json_result(data={
            "llms": models,
            "kb_info": kb_info
        })
    except Exception as e:
        logging.error(f"获取可用LLM模型失败: {e}")
        import traceback
        logging.error(f"详细错误: {traceback.format_exc()}")
        return get_json_result(code=settings.RetCode.SERVER_ERROR, message=str(e))