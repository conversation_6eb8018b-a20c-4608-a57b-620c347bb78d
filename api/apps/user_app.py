#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
import logging
import json
import re
import urllib.parse
from datetime import datetime

from flask import request, session, redirect
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import login_required, current_user, login_user, logout_user

from api.db.db_models import TenantLLM
from api.db.services.llm_service import TenantLLMService, LLMService
from api.utils.api_utils import (
    server_error_response,
    validate_request,
    get_data_error_result,
)
from api.utils import (
    get_uuid,
    get_format_time,
    decrypt,
    download_img,
    current_timestamp,
    datetime_format,
)
from api.db import UserTenantRole, FileType
from api import settings
from api.db.services.user_service import UserService, TenantService, UserTenantService
from api.db.services.file_service import FileService
from api.utils.api_utils import get_json_result, construct_response

# 导入SSO相关工具
from api.utils.sso_utils import get_ssoid, get_user_info as get_sso_user_info


@manager.route("/login", methods=["POST", "GET"])  # noqa: F821
def login():
    """
    User login endpoint.
    ---
    tags:
      - User
    parameters:
      - in: body
        name: body
        description: Login credentials.
        required: true
        schema:
          type: object
          properties:
            email:
              type: string
              description: User email.
            password:
              type: string
              description: User password.
    responses:
      200:
        description: Login successful.
        schema:
          type: object
      401:
        description: Authentication failed.
        schema:
          type: object
    """
    if not request.json:
        return get_json_result(
            data=False, code=settings.RetCode.AUTHENTICATION_ERROR, message="Unauthorized!"
        )

    email = request.json.get("email", "")
    users = UserService.query(email=email)
    if not users:
        return get_json_result(
            data=False,
            code=settings.RetCode.AUTHENTICATION_ERROR,
            message=f"Email: {email} is not registered!",
        )

    password = request.json.get("password")
    try:
        password = decrypt(password)
    except BaseException:
        return get_json_result(
            data=False, code=settings.RetCode.SERVER_ERROR, message="Fail to crypt password"
        )

    user = UserService.query_user(email, password)
    if user:
        response_data = user.to_json()
        user.access_token = get_uuid()
        login_user(user)
        user.update_time = (current_timestamp(),)
        user.update_date = (datetime_format(datetime.now()),)
        user.save()
        msg = "Welcome back!"
        return construct_response(data=response_data, auth=user.get_id(), message=msg)
    else:
        return get_json_result(
            data=False,
            code=settings.RetCode.AUTHENTICATION_ERROR,
            message="Email and password do not match!",
        )


@manager.route("/github_callback", methods=["GET"])  # noqa: F821
def github_callback():
    """
    GitHub OAuth callback endpoint.
    ---
    tags:
      - OAuth
    parameters:
      - in: query
        name: code
        type: string
        required: true
        description: Authorization code from GitHub.
    responses:
      200:
        description: Authentication successful.
        schema:
          type: object
    """
    import requests

    res = requests.post(
        settings.GITHUB_OAUTH.get("url"),
        data={
            "client_id": settings.GITHUB_OAUTH.get("client_id"),
            "client_secret": settings.GITHUB_OAUTH.get("secret_key"),
            "code": request.args.get("code"),
        },
        headers={"Accept": "application/json"},
    )
    res = res.json()
    if "error" in res:
        return redirect("/?error=%s" % res["error_description"])

    if "user:email" not in res["scope"].split(","):
        return redirect("/?error=user:email not in scope")

    session["access_token"] = res["access_token"]
    session["access_token_from"] = "github"
    user_info = user_info_from_github(session["access_token"])
    email_address = user_info["email"]
    users = UserService.query(email=email_address)
    user_id = get_uuid()
    if not users:
        # User isn't try to register
        try:
            try:
                avatar = download_img(user_info["avatar_url"])
            except Exception as e:
                logging.exception(e)
                avatar = ""
            users = user_register(
                user_id,
                {
                    "access_token": session["access_token"],
                    "email": email_address,
                    "avatar": avatar,
                    "nickname": user_info["login"],
                    "login_channel": "github",
                    "last_login_time": get_format_time(),
                    "is_superuser": False,
                },
            )
            if not users:
                raise Exception(f"Fail to register {email_address}.")
            if len(users) > 1:
                raise Exception(f"Same email: {email_address} exists!")

            # Try to log in
            user = users[0]
            login_user(user)
            return redirect("/?auth=%s" % user.get_id())
        except Exception as e:
            rollback_user_registration(user_id)
            logging.exception(e)
            return redirect("/?error=%s" % str(e))

    # User has already registered, try to log in
    user = users[0]
    user.access_token = get_uuid()
    login_user(user)
    user.save()
    return redirect("/?auth=%s" % user.get_id())


@manager.route("/feishu_callback", methods=["GET"])  # noqa: F821
def feishu_callback():
    """
    Feishu OAuth callback endpoint.
    ---
    tags:
      - OAuth
    parameters:
      - in: query
        name: code
        type: string
        required: true
        description: Authorization code from Feishu.
    responses:
      200:
        description: Authentication successful.
        schema:
          type: object
    """
    import requests

    app_access_token_res = requests.post(
        settings.FEISHU_OAUTH.get("app_access_token_url"),
        data=json.dumps(
            {
                "app_id": settings.FEISHU_OAUTH.get("app_id"),
                "app_secret": settings.FEISHU_OAUTH.get("app_secret"),
            }
        ),
        headers={"Content-Type": "application/json; charset=utf-8"},
    )
    app_access_token_res = app_access_token_res.json()
    if app_access_token_res["code"] != 0:
        return redirect("/?error=%s" % app_access_token_res)

    res = requests.post(
        settings.FEISHU_OAUTH.get("user_access_token_url"),
        data=json.dumps(
            {
                "grant_type": settings.FEISHU_OAUTH.get("grant_type"),
                "code": request.args.get("code"),
            }
        ),
        headers={
            "Content-Type": "application/json; charset=utf-8",
            "Authorization": f"Bearer {app_access_token_res['app_access_token']}",
        },
    )
    res = res.json()
    if res["code"] != 0:
        return redirect("/?error=%s" % res["message"])

    if "contact:user.email:readonly" not in res["data"]["scope"].split():
        return redirect("/?error=contact:user.email:readonly not in scope")
    session["access_token"] = res["data"]["access_token"]
    session["access_token_from"] = "feishu"
    user_info = user_info_from_feishu(session["access_token"])
    email_address = user_info["email"]
    users = UserService.query(email=email_address)
    user_id = get_uuid()
    if not users:
        # User isn't try to register
        try:
            try:
                avatar = download_img(user_info["avatar_url"])
            except Exception as e:
                logging.exception(e)
                avatar = ""
            users = user_register(
                user_id,
                {
                    "access_token": session["access_token"],
                    "email": email_address,
                    "avatar": avatar,
                    "nickname": user_info["en_name"],
                    "login_channel": "feishu",
                    "last_login_time": get_format_time(),
                    "is_superuser": False,
                },
            )
            if not users:
                raise Exception(f"Fail to register {email_address}.")
            if len(users) > 1:
                raise Exception(f"Same email: {email_address} exists!")

            # Try to log in
            user = users[0]
            login_user(user)
            return redirect("/?auth=%s" % user.get_id())
        except Exception as e:
            rollback_user_registration(user_id)
            logging.exception(e)
            return redirect("/?error=%s" % str(e))

    # User has already registered, try to log in
    user = users[0]
    user.access_token = get_uuid()
    login_user(user)
    user.save()
    return redirect("/?auth=%s" % user.get_id())


def user_info_from_feishu(access_token):
    import requests

    headers = {
        "Content-Type": "application/json; charset=utf-8",
        "Authorization": f"Bearer {access_token}",
    }
    res = requests.get(
        "https://open.feishu.cn/open-apis/authen/v1/user_info", headers=headers
    )
    user_info = res.json()["data"]
    user_info["email"] = None if user_info.get("email") == "" else user_info["email"]
    return user_info


def user_info_from_github(access_token):
    import requests

    headers = {"Accept": "application/json", "Authorization": f"token {access_token}"}
    res = requests.get(
        f"https://api.github.com/user?access_token={access_token}", headers=headers
    )
    user_info = res.json()
    email_info = requests.get(
        f"https://api.github.com/user/emails?access_token={access_token}",
        headers=headers,
    ).json()
    user_info["email"] = next(
        (email for email in email_info if email["primary"]), None
    )["email"]
    return user_info


@manager.route("/logout", methods=["GET"])  # noqa: F821
@login_required
def log_out():
    """
    User logout endpoint.
    ---
    tags:
      - User
    security:
      - ApiKeyAuth: []
    responses:
      200:
        description: Logout successful.
        schema:
          type: object
    """
    current_user.access_token = ""
    current_user.save()
    logout_user()
    return get_json_result(data=True)


@manager.route("/setting", methods=["POST"])  # noqa: F821
@login_required
def setting_user():
    """
    Update user settings.
    ---
    tags:
      - User
    security:
      - ApiKeyAuth: []
    parameters:
      - in: body
        name: body
        description: User settings to update.
        required: true
        schema:
          type: object
          properties:
            nickname:
              type: string
              description: New nickname.
            email:
              type: string
              description: New email.
    responses:
      200:
        description: Settings updated successfully.
        schema:
          type: object
    """
    update_dict = {}
    request_data = request.json
    if request_data.get("password"):
        new_password = request_data.get("new_password")
        if not check_password_hash(
                current_user.password, decrypt(request_data["password"])
        ):
            return get_json_result(
                data=False,
                code=settings.RetCode.AUTHENTICATION_ERROR,
                message="Password error!",
            )

        if new_password:
            update_dict["password"] = generate_password_hash(decrypt(new_password))

    for k in request_data.keys():
        if k in [
            "password",
            "new_password",
            "email",
            "status",
            "is_superuser",
            "login_channel",
            "is_anonymous",
            "is_active",
            "is_authenticated",
            "last_login_time",
        ]:
            continue
        update_dict[k] = request_data[k]

    try:
        UserService.update_by_id(current_user.id, update_dict)
        return get_json_result(data=True)
    except Exception as e:
        logging.exception(e)
        return get_json_result(
            data=False, message="Update failure!", code=settings.RetCode.EXCEPTION_ERROR
        )


@manager.route("/info", methods=["GET"])  # noqa: F821
@login_required
def user_profile():
    """
    Get user profile information.
    ---
    tags:
      - User
    security:
      - ApiKeyAuth: []
    responses:
      200:
        description: User profile retrieved successfully.
        schema:
          type: object
          properties:
            id:
              type: string
              description: User ID.
            nickname:
              type: string
              description: User nickname.
            email:
              type: string
              description: User email.
    """
    return get_json_result(data=current_user.to_dict())


def rollback_user_registration(user_id):
    try:
        UserService.delete_by_id(user_id)
    except Exception:
        pass
    try:
        TenantService.delete_by_id(user_id)
    except Exception:
        pass
    try:
        u = UserTenantService.query(tenant_id=user_id)
        if u:
            UserTenantService.delete_by_id(u[0].id)
    except Exception:
        pass
    try:
        TenantLLM.delete().where(TenantLLM.tenant_id == user_id).execute()
    except Exception:
        pass


def user_register(user_id, user):
    user["id"] = user_id
#    tenant = {
#        "id": user_id,
#        "name": user["nickname"] + "‘s Kingdom",
#        "llm_id": settings.CHAT_MDL,
#        "embd_id": settings.EMBEDDING_MDL,
#        "asr_id": settings.ASR_MDL,
#        "parser_ids": settings.PARSERS,
#        "img2txt_id": settings.IMAGE2TEXT_MDL,
#        "rerank_id": settings.RERANK_MDL,
#    }
#    usr_tenant = {
#        "tenant_id": user_id,
#        "user_id": user_id,
#        "invited_by": user_id,
#        "role": UserTenantRole.OWNER,
#    }
#    file_id = get_uuid()
#    file = {
#        "id": file_id,
#        "parent_id": file_id,
#        "tenant_id": user_id,
#        "created_by": user_id,
#        "name": "/",
#        "type": FileType.FOLDER.value,
#        "size": 0,
#        "location": "",
#    }
#    tenant_llm = []
#    
#    # 获取用户默认LLM配置
#    default_llm = settings.LLM or {}
#    default_factory = default_llm.get("factory", "")
#    default_api_key = default_llm.get("api_key", "")
#    default_base_url = default_llm.get("base_url", "")
#    default_models = default_llm.get("default_models", {})
#    
#    # 如果配置了默认LLM工厂，添加其模型
#    if default_factory and default_api_key:
#        # 获取该工厂下的所有模型
#        factory_models = LLMService.query(fid=default_factory)
#        
#        # 添加默认的聊天模型
#        chat_model_name = default_models.get("chat_model")
#        if chat_model_name:
#            chat_model = next((m for m in factory_models if m.llm_name == chat_model_name), None)
#            if chat_model:
#                tenant_llm.append({
#                    "tenant_id": user_id,
#                    "llm_factory": default_factory,
#                    "llm_name": chat_model_name,
#                    "model_type": chat_model.model_type,
#                    "api_key": default_api_key,
#                    "api_base": default_base_url,
#                    "max_tokens": chat_model.max_tokens if chat_model.max_tokens else 8192
#                })
#                # 设置为默认聊天模型
#                tenant["llm_id"] = f"{chat_model_name}@{default_factory}"
#        
#        # 添加默认的嵌入模型
#        embedding_model_name = default_models.get("embedding_model")
#        if embedding_model_name:
#            embedding_model = next((m for m in factory_models if m.llm_name == embedding_model_name), None)
#            if embedding_model:
#                tenant_llm.append({
#                    "tenant_id": user_id,
#                    "llm_factory": default_factory,
#                    "llm_name": embedding_model_name,
#                    "model_type": embedding_model.model_type,
#                    "api_key": default_api_key,
#                    "api_base": default_base_url,
#                    "max_tokens": embedding_model.max_tokens if embedding_model.max_tokens else 8192
#                })
#                # 设置为默认嵌入模型
#                tenant["embd_id"] = f"{embedding_model_name}@{default_factory}"
#
    if not UserService.save(**user):
        return
#    TenantService.insert(**tenant)
#    UserTenantService.insert(**usr_tenant)
#    if tenant_llm:
#        TenantLLMService.insert_many(tenant_llm)
#    FileService.insert(file)
    return UserService.query(email=user["email"])


@manager.route("/register", methods=["POST"])  # noqa: F821
@validate_request("nickname", "email", "password")
def user_add():
    """
    Register a new user.
    ---
    tags:
      - User
    parameters:
      - in: body
        name: body
        description: Registration details.
        required: true
        schema:
          type: object
          properties:
            nickname:
              type: string
              description: User nickname.
            email:
              type: string
              description: User email.
            password:
              type: string
              description: User password.
    responses:
      200:
        description: Registration successful.
        schema:
          type: object
    """
    req = request.json
    email_address = req["email"]

    # Validate the email address
    if not re.match(r"^[\w\._-]+@([\w_-]+\.)+[\w-]{2,}$", email_address):
        return get_json_result(
            data=False,
            message=f"Invalid email address: {email_address}!",
            code=settings.RetCode.OPERATING_ERROR,
        )

    # Check if the email address is already used
    if UserService.query(email=email_address):
        return get_json_result(
            data=False,
            message=f"Email: {email_address} has already registered!",
            code=settings.RetCode.OPERATING_ERROR,
        )

    # Construct user info data
    nickname = req["nickname"]
    user_dict = {
        "access_token": get_uuid(),
        "email": email_address,
        "nickname": nickname,
        "password": decrypt(req["password"]),
        "login_channel": "password",
        "last_login_time": get_format_time(),
        "is_superuser": False,
    }

    user_id = get_uuid()
    try:
        users = user_register(user_id, user_dict)
        if not users:
            raise Exception(f"Fail to register {email_address}.")
        if len(users) > 1:
            raise Exception(f"Same email: {email_address} exists!")
        user = users[0]
        login_user(user)
        return construct_response(
            data=user.to_json(),
            auth=user.get_id(),
            message=f"{nickname}, welcome aboard!",
        )
    except Exception as e:
        rollback_user_registration(user_id)
        logging.exception(e)
        return get_json_result(
            data=False,
            message=f"User registration failure, error: {str(e)}",
            code=settings.RetCode.EXCEPTION_ERROR,
        )


@manager.route("/tenant_info", methods=["GET"])  # noqa: F821
@login_required
def tenant_info():
    """
    Get tenant information.
    ---
    tags:
      - Tenant
    security:
      - ApiKeyAuth: []
    responses:
      200:
        description: Tenant information retrieved successfully.
        schema:
          type: object
          properties:
            tenant_id:
              type: string
              description: Tenant ID.
            name:
              type: string
              description: Tenant name.
            llm_id:
              type: string
              description: LLM ID.
            embd_id:
              type: string
              description: Embedding model ID.
    """
    try:
        tenants = TenantService.get_info_by(current_user.id)
        if not tenants:
            # 用户没有租户时返回空对象，而不是错误
            return get_json_result(data={})
        return get_json_result(data=tenants[0])
    except Exception as e:
        return server_error_response(e)


@manager.route("/my_tenants", methods=["GET"])  # noqa: F821
@login_required
def my_tenants():
    """
    Get all tenants that the user belongs to.
    ---
    tags:
      - Tenant
    security:
      - ApiKeyAuth: []
    responses:
      200:
        description: User tenants retrieved successfully.
        schema:
          type: object
          properties:
            data:
              type: array
              items:
                type: object
                properties:
                  tenant_id:
                    type: string
                    description: Tenant ID.
                  name:
                    type: string
                    description: Tenant name.
                  role:
                    type: string
                    description: User role in this tenant.
                  can_edit_models:
                    type: boolean
                    description: Whether user can edit models in this tenant.
    """
    try:
        tenants = TenantService.get_joined_tenants_by_user_id(current_user.id)
        # 添加权限信息
        for tenant in tenants:
            tenant['can_edit_models'] = tenant['role'] in [UserTenantRole.OWNER, UserTenantRole.ADMIN]
        return get_json_result(data=tenants)
    except Exception as e:
        return server_error_response(e)


@manager.route("/set_tenant_info", methods=["POST"])  # noqa: F821
@login_required
@validate_request("tenant_id", "asr_id", "embd_id", "img2txt_id", "llm_id")
def set_tenant_info():
    """
    Update tenant model settings.
    ---
    tags:
      - Tenant
    security:
      - ApiKeyAuth: []
    parameters:
      - in: body
        name: body
        description: Tenant model settings to update.
        required: true
        schema:
          type: object
          properties:
            tenant_id:
              type: string
              description: Tenant ID.
            llm_id:
              type: string
              description: LLM ID.
            embd_id:
              type: string
              description: Embedding model ID.
            asr_id:
              type: string
              description: ASR model ID.
            img2txt_id:
              type: string
              description: Image to Text model ID.
    responses:
      200:
        description: Tenant model settings updated successfully.
        schema:
          type: object
    """
    req = request.json
    try:
        tid = req.pop("tenant_id")
        
        # 检查用户是否有权限修改该租户信息
        user_tenants = UserTenantService.query(user_id=current_user.id, tenant_id=tid)
        if not user_tenants:
            return get_json_result(
                data=False,
                message="您不属于该团队",
                code=settings.RetCode.AUTHENTICATION_ERROR
            )
        
        user_role = user_tenants[0].role
        if user_role not in [UserTenantRole.OWNER, UserTenantRole.ADMIN]:
            return get_json_result(
                data=False,
                message="权限不足，只有团队所有者和管理员可以修改模型设置",
                code=settings.RetCode.AUTHENTICATION_ERROR
            )
        
        # 只更新模型相关字段，过滤掉其他字段（如name等）
        allowed_fields = ['llm_id', 'embd_id', 'asr_id', 'img2txt_id', 'rerank_id', 'tts_id']
        update_data = {k: v for k, v in req.items() if k in allowed_fields}
        
        if not update_data:
            return get_json_result(
                data=False,
                message="没有可更新的模型设置字段",
                code=settings.RetCode.OPERATING_ERROR
            )
        
        TenantService.update_by_id(tid, update_data)
        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route("/sso_login", methods=["GET"])  # noqa: F821
def sso_login():
    """
    重定向到SSO登录页面
    ---
    tags:
      - OAuth
    responses:
      302:
        description: 重定向到SSO登录页面
    """
    if not settings.SSO_CONFIG:
        return get_json_result(
            data=False, 
            code=settings.RetCode.SERVER_ERROR, 
            message="SSO配置未设置"
        )
    
    client_id = settings.SSO_CONFIG.get("client_id")
    # 构建回调URL，使用硬编码的地址和端口，而不是依赖request.host_url
    # 端口9222是前端实际运行的端口，需要保持一致
    # 从请求的Referer或Origin头提取前端域名
    frontend_url = None
    referer = request.headers.get('Referer')
    origin = request.headers.get('Origin')
    
    if referer:
        # 提取referer的域名部分
        from urllib.parse import urlparse
        parsed_referer = urlparse(referer)
        frontend_url = f"{parsed_referer.scheme}://{parsed_referer.netloc}"
    elif origin:
        frontend_url = origin
    else:
        # 如果无法从请求中获取，则使用配置文件中的默认值
        frontend_url = settings.FRONTEND_URL if hasattr(settings, 'FRONTEND_URL') else request.host_url.rstrip('/')
    
    # 构建完整回调URL
    redirect_uri = urllib.parse.quote(f"{frontend_url}/shangou_ai_rag/api/v1/user/sso_callback")
    t = int(datetime.now().timestamp() * 1000)
    url = settings.SSO_CONFIG.get('sso_host') + settings.SSO_CONFIG.get('login_uri')
    url += f'?client_id={client_id}&redirect_uri={redirect_uri}&t={str(t)}'
    
    return redirect(url)


@manager.route("/sso_callback", methods=["GET"])  # noqa: F821
def sso_callback():
    """
    SSO回调处理
    ---
    tags:
      - OAuth
    parameters:
      - in: query
        name: code
        type: string
        required: true
        description: SSO授权码
    responses:
      302:
        description: 登录成功后重定向到首页
      200:
        description: 登录失败信息
    """
    if not settings.SSO_CONFIG:
        return get_json_result(
            data=False, 
            code=settings.RetCode.SERVER_ERROR, 
            message="SSO配置未设置"
        )
    
    
    code = request.args.get('code')
    if not code:
        return get_json_result(
            data=False, 
            code=settings.RetCode.AUTHENTICATION_ERROR, 
            message="SSO验证失败：未获取到授权码"
        )
    
    # 获取ssoid
    token_data = get_ssoid(code)
    if not token_data or not token_data.get('accessToken'):
        return get_json_result(
            data=False, 
            code=settings.RetCode.AUTHENTICATION_ERROR, 
            message="SSO验证失败：授权码无效"
        )
    
    # 获取用户信息
    ssoid = token_data['accessToken']
    user_info = get_sso_user_info(ssoid)
    if not user_info:
        return get_json_result(
            data=False, 
            code=settings.RetCode.AUTHENTICATION_ERROR, 
            message="SSO验证失败：无法获取用户信息"
        )
    
    # 记录用户信息日志
    logging.warning(f"SSO获取到的用户信息: {user_info}")
    
    # 检查用户是否已注册
    email = user_info.get('email')
    if not email:
        return get_json_result(
            data=False, 
            code=settings.RetCode.AUTHENTICATION_ERROR, 
            message="SSO验证失败：用户邮箱为空"
        )
    
    users = UserService.query(email=email)
    user_id = user_info.get('uid')
    
    if not users:
        # 用户未注册，创建新用户
        try:
            # 尝试多种可能的字段名获取用户名
            nickname = user_info.get('loginName') or user_info.get('name')
            try:
                avatar = download_img(user_info["avatar_url"])
            except Exception as e:
                logging.exception(e)
                avatar = ""
            users = user_register(
                user_id,
                {
                    "access_token": ssoid,
                    "email": email,
                    "avatar": avatar,
                    "nickname": nickname,
                    "login_channel": "sso",
                    "last_login_time": get_format_time(),
                    "is_superuser": False,
                }
            )
            if not users:
                raise Exception(f"注册失败：{email}")
            if len(users) > 1:
                raise Exception(f"邮箱重复：{email}")
            
            # 登录新用户
            user = users[0]
            login_user(user)
            # logging.warning(f"SSO新用户注册并登录成功: {email}, user_id: {user.get_id()}")
            
            return redirect("/shangou_ai_rag/index.html#/?auth=%s" % user.get_id())
            
        except Exception as e:
            rollback_user_registration(user_id)
            logging.exception(e)
            return get_json_result(
                data=False, 
                code=settings.RetCode.EXCEPTION_ERROR, 
                message=f"SSO用户注册失败：{str(e)}"
            )
    
    # 用户已注册，直接登录
    user = users[0]
    user.access_token = get_uuid()
    login_user(user)
    user.update_time = (current_timestamp(),)
    user.update_date = (datetime_format(datetime.now()),)
    user.save()
    
    # 设置session cookie
    session["access_token"] = ssoid
    session["access_token_from"] = "sso"
    
    logging.warning(f"SSO用户登录成功: {email}, user_id: {user.get_id()}")
    return redirect("/shangou_ai_rag/index.html#/?auth=%s" % user.get_id())


@manager.route("/sso_logout", methods=["GET"])  # noqa: F821
@login_required
def sso_logout():
    """
    SSO登出处理
    ---
    tags:
      - OAuth
    security:
      - ApiKeyAuth: []
    responses:
      200:
        description: 登出成功
        schema:
          type: object
    """
    import requests
    from api.utils.sso_utils import build_header, RequestMethod
    
    # 获取当前登录通道
    if current_user.login_channel != 'sso':
        # 非SSO登录用户，使用普通登出流程
        return log_out()
    
    if not settings.SSO_CONFIG:
        return log_out()
    
    # 尝试调用SSO登出接口
    try:
        config = settings.SSO_CONFIG
        url = config.get('sso_host') + config.get('logout_uri')
        headers = build_header(
            config.get('logout_uri'),
            RequestMethod.POST.value,
            config.get('client_id'),
            config.get('secret'),
            '/sson'
        )
        params = {'accessToken': current_user.access_token}
        
        # 调用SSO登出接口
        requests.post(url=url, json=params, headers=headers)
    except Exception as e:
        logging.warning(f"SSO登出调用异常：{str(e)}")
    
    # 执行本地登出
    return log_out()
