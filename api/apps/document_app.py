#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License
#
import json
import os.path
import pathlib
import re
import datetime
import logging

import flask
from flask import request
from flask_login import login_required, current_user

from deepdoc.parser.html_parser import RAGFlowHtmlParser
from rag.nlp import search

from api.db import FileType, TaskStatus, ParserType, FileSource
from api.db.db_models import File, Task
from api.db.services.file2document_service import File2DocumentService
from api.db.services.file_service import FileService
from api.db.services.task_service import queue_tasks
from api.db.services.user_service import UserTenantService
from api.db.services import duplicate_name
from api.db.services.knowledgebase_service import KnowledgebaseService
from api.db.services.task_service import TaskService
from api.db.services.document_service import DocumentService, doc_upload_and_parse
from api.utils.api_utils import (
    server_error_response,
    get_data_error_result,
    validate_request,
)
from api.utils import get_uuid
from api import settings
from api.utils.api_utils import get_json_result
from rag.utils.storage_factory import STORAGE_IMPL
from api.utils.file_utils import filename_type, thumbnail, get_project_base_directory
from api.utils.web_utils import html2pdf, is_valid_url
from api.constants import IMG_BASE64_PREFIX
from bs4 import BeautifulSoup
import requests


@manager.route('/upload', methods=['POST'])  # noqa: F821
@login_required
@validate_request("kb_id")
def upload():
    kb_id = request.form.get("kb_id")
    if not kb_id:
        return get_json_result(
            data=False, message='Lack of "KB ID"', code=settings.RetCode.ARGUMENT_ERROR)
    if 'file' not in request.files:
        return get_json_result(
            data=False, message='No file part!', code=settings.RetCode.ARGUMENT_ERROR)

    file_objs = request.files.getlist('file')
    for file_obj in file_objs:
        if file_obj.filename == '':
            return get_json_result(
                data=False, message='No file selected!', code=settings.RetCode.ARGUMENT_ERROR)

    e, kb = KnowledgebaseService.get_by_id(kb_id)
    if not e:
        raise LookupError("Can't find this knowledgebase!")

    err, _ = FileService.upload_document(kb, file_objs, current_user.id)
    if err:
        return get_json_result(
            data=False, message="\n".join(err), code=settings.RetCode.SERVER_ERROR)
    return get_json_result(data=True)


@manager.route('/web_crawl', methods=['POST'])  # noqa: F821
@login_required
@validate_request("kb_id", "name", "url")
def web_crawl():
    kb_id = request.form.get("kb_id")
    if not kb_id:
        return get_json_result(
            data=False, message='Lack of "KB ID"', code=settings.RetCode.ARGUMENT_ERROR)
    name = request.form.get("name")
    url = request.form.get("url")
    if not is_valid_url(url):
        return get_json_result(
            data=False, message='The URL format is invalid', code=settings.RetCode.ARGUMENT_ERROR)
    e, kb = KnowledgebaseService.get_by_id(kb_id)
    if not e:
        raise LookupError("Can't find this knowledgebase!")

    # 检查是否为学城文档URL
    is_sankuai_doc = re.search(r"km\.sankuai\.com/collabpage/\d+", url) is not None
    
    root_folder = FileService.get_root_folder(current_user.id)
    pf_id = root_folder["id"]
    FileService.init_knowledgebase_docs(pf_id, current_user.id)
    kb_root_folder = FileService.get_kb_folder(current_user.id)
    kb_folder = FileService.new_a_file_from_kb(kb.tenant_id, kb.name, kb_root_folder["id"])

    try:
        if is_sankuai_doc:
            # 处理学城文档 - 这里会调用用户自己实现的函数获取markdown内容
            # 这部分逻辑由用户自行实现，这里仅提供接口
            from api.utils.citadel_loader import  CitadelLoader# 假设用户会实现这个模块
            loader = CitadelLoader()
            try:
                result = loader.load_document(doc_url=url)
                markdown_content = result.get('content', '')
                if not markdown_content:
                    return server_error_response(ValueError("无法获取学城文档内容"))
            except ValueError as e:
                # 如果是权限相关错误，提供更详细的信息
                if "test_salesmind" in str(e) and "权限" in str(e):
                    return server_error_response(ValueError(f"权限错误: {str(e)}"))
                return server_error_response(ValueError(f"获取学城文档失败: {str(e)}"))
            except Exception as e:
                return server_error_response(ValueError(f"处理学城文档时发生错误: {str(e)}"))
            
            # 保存为markdown文件
            filename = duplicate_name(
                DocumentService.query,
                name=name + ".md",
                kb_id=kb.id)
            
            location = filename
            while STORAGE_IMPL.obj_exist(kb_id, location):
                location += "_"
                
            STORAGE_IMPL.put(kb_id, location, markdown_content.encode('utf-8'))
            
            # 添加文档元数据
            meta_fields = {}
            if 'metadata' in result and 'doc_meta' in result['metadata']:
                meta_fields = result['metadata']['doc_meta']
            
            # 添加源类型和源URL到文档记录
            doc = {
                "id": get_uuid(),
                "kb_id": kb.id,
                "parser_id": kb.parser_id,
                "parser_config": kb.parser_config,
                "created_by": current_user.id,
                "type": FileType.DOC.value,  # 使用DOC类型代替不存在的DOCUMENT类型
                "name": filename,
                "location": location,
                "size": len(markdown_content),
                "thumbnail": "",  # Markdown文件不需要缩略图
                "meta_fields": meta_fields,  # 添加元数据
                "source_type": "citadel",  # 标记为学城文档
                "source_url": url,  # 保存原始URL
                "last_update_time": datetime.datetime.now()  # 记录更新时间
            }
            DocumentService.insert(doc)
            FileService.add_file_from_kb(doc, kb_folder["id"], kb.tenant_id)
        else:
            # 原有的处理逻辑，针对普通网页
            blob = html2pdf(url)
            if not blob:
                return server_error_response(ValueError("Download failure."))

            filename = duplicate_name(
                DocumentService.query,
                name=name + ".pdf",
                kb_id=kb.id)
            filetype = filename_type(filename)
            if filetype == FileType.OTHER.value:
                raise RuntimeError("This type of file has not been supported yet!")

            location = filename
            while STORAGE_IMPL.obj_exist(kb_id, location):
                location += "_"
            STORAGE_IMPL.put(kb_id, location, blob)
            doc = {
                "id": get_uuid(),
                "kb_id": kb.id,
                "parser_id": kb.parser_id,
                "parser_config": kb.parser_config,
                "created_by": current_user.id,
                "type": filetype,
                "name": filename,
                "location": location,
                "size": len(blob),
                "thumbnail": thumbnail(filename, blob),
                "source_type": "web",  # 标记为Web文档
                "source_url": url,  # 保存原始URL
                "last_update_time": datetime.datetime.now()  # 记录更新时间
            }
            if doc["type"] == FileType.VISUAL:
                doc["parser_id"] = ParserType.PICTURE.value
            if doc["type"] == FileType.AURAL:
                doc["parser_id"] = ParserType.AUDIO.value
            if re.search(r"\.(ppt|pptx|pages)$", filename):
                doc["parser_id"] = ParserType.PRESENTATION.value
            if re.search(r"\.(eml)$", filename):
                doc["parser_id"] = ParserType.EMAIL.value
            DocumentService.insert(doc)
            FileService.add_file_from_kb(doc, kb_folder["id"], kb.tenant_id)
    except Exception as e:
        return server_error_response(e)
    return get_json_result(data=True)


@manager.route('/create', methods=['POST'])  # noqa: F821
@login_required
@validate_request("name", "kb_id")
def create():
    req = request.json
    kb_id = req["kb_id"]
    if not kb_id:
        return get_json_result(
            data=False, message='Lack of "KB ID"', code=settings.RetCode.ARGUMENT_ERROR)

    try:
        e, kb = KnowledgebaseService.get_by_id(kb_id)
        if not e:
            return get_data_error_result(
                message="Can't find this knowledgebase!")

        if DocumentService.query(name=req["name"], kb_id=kb_id):
            return get_data_error_result(
                message="Duplicated document name in the same knowledgebase.")

        doc = DocumentService.insert({
            "id": get_uuid(),
            "kb_id": kb.id,
            "parser_id": kb.parser_id,
            "parser_config": kb.parser_config,
            "created_by": current_user.id,
            "type": FileType.VIRTUAL,
            "name": req["name"],
            "location": "",
            "size": 0
        })
        return get_json_result(data=doc.to_json())
    except Exception as e:
        return server_error_response(e)


@manager.route('/list', methods=['GET'])  # noqa: F821
@login_required
def list_docs():
    kb_id = request.args.get("kb_id")
    if not kb_id:
        return get_json_result(
            data=False, message='Lack of "KB ID"', code=settings.RetCode.ARGUMENT_ERROR)
    tenants = UserTenantService.query(user_id=current_user.id)
    for tenant in tenants:
        if KnowledgebaseService.query(
                tenant_id=tenant.tenant_id, id=kb_id):
            break
    else:
        return get_json_result(
            data=False, message='Only owner of knowledgebase authorized for this operation.',
            code=settings.RetCode.OPERATING_ERROR)
    keywords = request.args.get("keywords", "")

    page_number = int(request.args.get("page", 1))
    items_per_page = int(request.args.get("page_size", 15))
    orderby = request.args.get("orderby", "create_time")
    desc = request.args.get("desc", True)
    try:
        docs, tol = DocumentService.get_by_kb_id(
            kb_id, page_number, items_per_page, orderby, desc, keywords)

        for doc_item in docs:
            if doc_item['thumbnail'] and not doc_item['thumbnail'].startswith(IMG_BASE64_PREFIX):
                doc_item['thumbnail'] = f"/v1/document/image/{kb_id}-{doc_item['thumbnail']}"

        return get_json_result(data={"total": tol, "docs": docs})
    except Exception as e:
        return server_error_response(e)


@manager.route('/infos', methods=['POST'])  # noqa: F821
@login_required
def docinfos():
    req = request.json
    doc_ids = req["doc_ids"]
    for doc_id in doc_ids:
        if not DocumentService.accessible(doc_id, current_user.id):
            return get_json_result(
                data=False,
                message='No authorization.',
                code=settings.RetCode.AUTHENTICATION_ERROR
            )
    docs = DocumentService.get_by_ids(doc_ids)
    return get_json_result(data=list(docs.dicts()))


@manager.route('/thumbnails', methods=['GET'])  # noqa: F821
# @login_required
def thumbnails():
    doc_ids = request.args.get("doc_ids").split(",")
    if not doc_ids:
        return get_json_result(
            data=False, message='Lack of "Document ID"', code=settings.RetCode.ARGUMENT_ERROR)

    try:
        docs = DocumentService.get_thumbnails(doc_ids)

        for doc_item in docs:
            if doc_item['thumbnail'] and not doc_item['thumbnail'].startswith(IMG_BASE64_PREFIX):
                doc_item['thumbnail'] = f"/v1/document/image/{doc_item['kb_id']}-{doc_item['thumbnail']}"

        return get_json_result(data={d["id"]: d["thumbnail"] for d in docs})
    except Exception as e:
        return server_error_response(e)


@manager.route('/change_status', methods=['POST'])  # noqa: F821
@login_required
@validate_request("doc_id", "status")
def change_status():
    req = request.json
    if str(req["status"]) not in ["0", "1"]:
        return get_json_result(
            data=False,
            message='"Status" must be either 0 or 1!',
            code=settings.RetCode.ARGUMENT_ERROR)

    if not DocumentService.accessible(req["doc_id"], current_user.id):
        return get_json_result(
            data=False,
            message='No authorization.',
            code=settings.RetCode.AUTHENTICATION_ERROR)

    try:
        e, doc = DocumentService.get_by_id(req["doc_id"])
        if not e:
            return get_data_error_result(message="Document not found!")
        e, kb = KnowledgebaseService.get_by_id(doc.kb_id)
        if not e:
            return get_data_error_result(
                message="Can't find this knowledgebase!")

        if not DocumentService.update_by_id(
                req["doc_id"], {"status": str(req["status"])}):
            return get_data_error_result(
                message="Database error (Document update)!")

        status = int(req["status"])
        settings.docStoreConn.update({"doc_id": req["doc_id"]}, {"available_int": status},
                                     search.index_name(kb.tenant_id), doc.kb_id)
        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route('/rm', methods=['POST'])  # noqa: F821
@login_required
@validate_request("doc_id")
def rm():
    req = request.json
    doc_ids = req["doc_id"]
    if isinstance(doc_ids, str):
        doc_ids = [doc_ids]

    for doc_id in doc_ids:
        if not DocumentService.accessible4deletion(doc_id, current_user.id):
            return get_json_result(
                data=False,
                message='No authorization.',
                code=settings.RetCode.AUTHENTICATION_ERROR
            )

    root_folder = FileService.get_root_folder(current_user.id)
    pf_id = root_folder["id"]
    FileService.init_knowledgebase_docs(pf_id, current_user.id)
    errors = ""
    for doc_id in doc_ids:
        try:
            e, doc = DocumentService.get_by_id(doc_id)
            if not e:
                return get_data_error_result(message="Document not found!")
            tenant_id = DocumentService.get_tenant_id(doc_id)
            if not tenant_id:
                return get_data_error_result(message="Tenant not found!")

            b, n = File2DocumentService.get_storage_address(doc_id=doc_id)

            TaskService.filter_delete([Task.doc_id == doc_id])
            if not DocumentService.remove_document(doc, tenant_id):
                return get_data_error_result(
                    message="Database error (Document removal)!")

            f2d = File2DocumentService.get_by_document_id(doc_id)
            FileService.filter_delete([File.source_type == FileSource.KNOWLEDGEBASE, File.id == f2d[0].file_id])
            File2DocumentService.delete_by_document_id(doc_id)

            STORAGE_IMPL.rm(b, n)
        except Exception as e:
            errors += str(e)

    if errors:
        return get_json_result(data=False, message=errors, code=settings.RetCode.SERVER_ERROR)

    return get_json_result(data=True)


@manager.route('/run', methods=['POST'])  # noqa: F821
@login_required
@validate_request("doc_ids", "run")
def run():
    req = request.json
    logging.info(f"[文档解析] 收到解析请求: doc_ids={req['doc_ids']}, run={req['run']}")
    
    for doc_id in req["doc_ids"]:
        if not DocumentService.accessible(doc_id, current_user.id):
            logging.warning(f"[文档解析] 用户无权限访问文档: user_id={current_user.id}, doc_id={doc_id}")
            return get_json_result(
                data=False,
                message='No authorization.',
                code=settings.RetCode.AUTHENTICATION_ERROR
            )
    try:
        for id in req["doc_ids"]:
            logging.info(f"[文档解析] 开始处理文档: doc_id={id}")
            info = {"run": str(req["run"]), "progress": 0}
            if str(req["run"]) == TaskStatus.RUNNING.value and req.get("delete", False):
                info["progress_msg"] = ""
                info["chunk_num"] = 0
                info["token_num"] = 0
                logging.info(f"[文档解析] 清空文档之前的解析结果: doc_id={id}")
                
            logging.info(f"[文档解析] 更新文档状态: doc_id={id}, info={info}")
            DocumentService.update_by_id(id, info)
            
            tenant_id = DocumentService.get_tenant_id(id)
            if not tenant_id:
                logging.error(f"[文档解析] 找不到租户信息: doc_id={id}")
                return get_data_error_result(message="Tenant not found!")
                
            e, doc = DocumentService.get_by_id(id)
            if not e:
                logging.error(f"[文档解析] 找不到文档: doc_id={id}")
                return get_data_error_result(message="Document not found!")
                
            if req.get("delete", False):
                logging.info(f"[文档解析] 删除文档相关任务和索引: doc_id={id}")
                TaskService.filter_delete([Task.doc_id == id])
                if settings.docStoreConn.indexExist(search.index_name(tenant_id), doc.kb_id):
                    settings.docStoreConn.delete({"doc_id": id}, search.index_name(tenant_id), doc.kb_id)

            if str(req["run"]) == TaskStatus.RUNNING.value:
                logging.info(f"[文档解析] 准备创建解析任务: doc_id={id}")
                e, doc = DocumentService.get_by_id(id)
                doc = doc.to_dict()
                doc["tenant_id"] = tenant_id
                
                try:
                    bucket, name = File2DocumentService.get_storage_address(doc_id=doc["id"])
                    logging.info(f"[文档解析] 获取存储地址成功: doc_id={id}, bucket={bucket}, name={name}")
                except Exception as e:
                    logging.error(f"[文档解析] 获取存储地址失败: doc_id={id}, error={str(e)}")
                    raise
                    
                try:
                    logging.info(f"[文档解析] 开始创建任务队列: doc_id={id}")
                    queue_tasks(doc, bucket, name)
                    logging.info(f"[文档解析] 任务队列创建成功: doc_id={id}")
                except Exception as e:
                    logging.error(f"[文档解析] 创建任务队列失败: doc_id={id}, error={str(e)}")
                    raise

        return get_json_result(data=True)
    except Exception as e:
        logging.exception(f"[文档解析] 处理过程发生异常: error={str(e)}")
        return server_error_response(e)


@manager.route('/rename', methods=['POST'])  # noqa: F821
@login_required
@validate_request("doc_id", "name")
def rename():
    req = request.json
    if not DocumentService.accessible(req["doc_id"], current_user.id):
        return get_json_result(
            data=False,
            message='No authorization.',
            code=settings.RetCode.AUTHENTICATION_ERROR
        )
    try:
        e, doc = DocumentService.get_by_id(req["doc_id"])
        if not e:
            return get_data_error_result(message="Document not found!")
        if pathlib.Path(req["name"].lower()).suffix != pathlib.Path(
                doc.name.lower()).suffix:
            return get_json_result(
                data=False,
                message="The extension of file can't be changed",
                code=settings.RetCode.ARGUMENT_ERROR)
        for d in DocumentService.query(name=req["name"], kb_id=doc.kb_id):
            if d.name == req["name"]:
                return get_data_error_result(
                    message="Duplicated document name in the same knowledgebase.")

        if not DocumentService.update_by_id(
                req["doc_id"], {"name": req["name"]}):
            return get_data_error_result(
                message="Database error (Document rename)!")

        informs = File2DocumentService.get_by_document_id(req["doc_id"])
        if informs:
            e, file = FileService.get_by_id(informs[0].file_id)
            FileService.update_by_id(file.id, {"name": req["name"]})

        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route('/get/<doc_id>', methods=['GET'])  # noqa: F821
# @login_required
def get(doc_id):
    try:
        e, doc = DocumentService.get_by_id(doc_id)
        if not e:
            return get_data_error_result(message="Document not found!")

        b, n = File2DocumentService.get_storage_address(doc_id=doc_id)
        response = flask.make_response(STORAGE_IMPL.get(b, n))

        ext = re.search(r"\.([^.]+)$", doc.name)
        if ext:
            if doc.type == FileType.VISUAL.value:
                response.headers.set('Content-Type', 'image/%s' % ext.group(1))
            else:
                response.headers.set(
                    'Content-Type',
                    'application/%s' %
                    ext.group(1))
        return response
    except Exception as e:
        return server_error_response(e)


@manager.route('/change_parser', methods=['POST'])  # noqa: F821
@login_required
@validate_request("doc_id", "parser_id")
def change_parser():
    req = request.json

    if not DocumentService.accessible(req["doc_id"], current_user.id):
        return get_json_result(
            data=False,
            message='No authorization.',
            code=settings.RetCode.AUTHENTICATION_ERROR
        )
    try:
        e, doc = DocumentService.get_by_id(req["doc_id"])
        if not e:
            return get_data_error_result(message="Document not found!")
        if doc.parser_id.lower() == req["parser_id"].lower():
            if "parser_config" in req:
                if req["parser_config"] == doc.parser_config:
                    return get_json_result(data=True)
            else:
                return get_json_result(data=True)

        if ((doc.type == FileType.VISUAL and req["parser_id"] != "picture")
                or (re.search(
                    r"\.(ppt|pptx|pages)$", doc.name) and req["parser_id"] != "presentation")):
            return get_data_error_result(message="Not supported yet!")

        e = DocumentService.update_by_id(doc.id,
                                         {"parser_id": req["parser_id"], "progress": 0, "progress_msg": "",
                                          "run": TaskStatus.UNSTART.value})
        if not e:
            return get_data_error_result(message="Document not found!")
        if "parser_config" in req:
            DocumentService.update_parser_config(doc.id, req["parser_config"])
        if doc.token_num > 0:
            e = DocumentService.increment_chunk_num(doc.id, doc.kb_id, doc.token_num * -1, doc.chunk_num * -1,
                                                    doc.process_duation * -1)
            if not e:
                return get_data_error_result(message="Document not found!")
            tenant_id = DocumentService.get_tenant_id(req["doc_id"])
            if not tenant_id:
                return get_data_error_result(message="Tenant not found!")
            if settings.docStoreConn.indexExist(search.index_name(tenant_id), doc.kb_id):
                settings.docStoreConn.delete({"doc_id": doc.id}, search.index_name(tenant_id), doc.kb_id)

        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route('/image/<image_id>', methods=['GET'])  # noqa: F821
# @login_required
def get_image(image_id):
    try:
        arr = image_id.split("-")
        if len(arr) != 2:
            return get_data_error_result(message="Image not found.")
        bkt, nm = image_id.split("-")
        response = flask.make_response(STORAGE_IMPL.get(bkt, nm))
        response.headers.set('Content-Type', 'image/JPEG')
        return response
    except Exception as e:
        return server_error_response(e)


@manager.route('/upload_and_parse', methods=['POST'])  # noqa: F821
@login_required
@validate_request("conversation_id")
def upload_and_parse():
    if 'file' not in request.files:
        return get_json_result(
            data=False, message='No file part!', code=settings.RetCode.ARGUMENT_ERROR)

    file_objs = request.files.getlist('file')
    for file_obj in file_objs:
        if file_obj.filename == '':
            return get_json_result(
                data=False, message='No file selected!', code=settings.RetCode.ARGUMENT_ERROR)

    doc_ids = doc_upload_and_parse(request.form.get("conversation_id"), file_objs, current_user.id)

    return get_json_result(data=doc_ids)


@manager.route('/parse', methods=['POST'])  # noqa: F821
@login_required
def parse():
    url = request.json.get("url") if request.json else ""
    if url:
        if not is_valid_url(url):
            return get_json_result(
                data=False, message='The URL format is invalid', code=settings.RetCode.ARGUMENT_ERROR)
        download_path = os.path.join(get_project_base_directory(), "logs/downloads")
        os.makedirs(download_path, exist_ok=True)
        from seleniumwire.webdriver import Chrome, ChromeOptions
        options = ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_experimental_option('prefs', {
            'download.default_directory': download_path,
            'download.prompt_for_download': False,
            'download.directory_upgrade': True,
            'safebrowsing.enabled': True
        })
        driver = Chrome(options=options)
        driver.get(url)
        res_headers = [r.response.headers for r in driver.requests if r and r.response]
        if len(res_headers) > 1:
            sections = RAGFlowHtmlParser().parser_txt(driver.page_source)
            driver.quit()
            return get_json_result(data="\n".join(sections))

        class File:
            filename: str
            filepath: str

            def __init__(self, filename, filepath):
                self.filename = filename
                self.filepath = filepath

            def read(self):
                with open(self.filepath, "rb") as f:
                    return f.read()

        r = re.search(r"filename=\"([^\"]+)\"", str(res_headers))
        if not r or not r.group(1):
            return get_json_result(
                data=False, message="Can't not identify downloaded file", code=settings.RetCode.ARGUMENT_ERROR)
        f = File(r.group(1), os.path.join(download_path, r.group(1)))
        txt = FileService.parse_docs([f], current_user.id)
        return get_json_result(data=txt)

    if 'file' not in request.files:
        return get_json_result(
            data=False, message='No file part!', code=settings.RetCode.ARGUMENT_ERROR)

    file_objs = request.files.getlist('file')
    txt = FileService.parse_docs(file_objs, current_user.id)

    return get_json_result(data=txt)


@manager.route('/set_meta', methods=['POST'])  # noqa: F821
@login_required
@validate_request("doc_id", "meta")
def set_meta():
    req = request.json
    if not DocumentService.accessible(req["doc_id"], current_user.id):
        return get_json_result(
            data=False,
            message='No authorization.',
            code=settings.RetCode.AUTHENTICATION_ERROR
        )
    try:
        meta = json.loads(req["meta"])
    except Exception as e:
        return get_json_result(
            data=False, message=f'Json syntax error: {e}', code=settings.RetCode.ARGUMENT_ERROR)
    if not isinstance(meta, dict):
        return get_json_result(
            data=False, message='Meta data should be in Json map format, like {"key": "value"}', code=settings.RetCode.ARGUMENT_ERROR)

    try:
        e, doc = DocumentService.get_by_id(req["doc_id"])
        if not e:
            return get_data_error_result(message="Document not found!")

        if not DocumentService.update_by_id(
                req["doc_id"], {"meta_fields": meta}):
            return get_data_error_result(
                message="Database error (meta updates)!")

        return get_json_result(data=True)
    except Exception as e:
        return server_error_response(e)


@manager.route('/update_from_source', methods=['POST'])  # noqa: F821
@login_required
@validate_request("doc_id")
def update_from_source():
    """
    从原始来源更新文档内容
    支持从学城或网页源更新文档
    """
    req = request.json
    doc_id = req["doc_id"]
    
    if not DocumentService.accessible(doc_id, current_user.id):
        return get_json_result(
            data=False,
            message='No authorization.',
            code=settings.RetCode.AUTHENTICATION_ERROR
        )
        
    try:
        # 获取文档信息
        e, doc = DocumentService.get_by_id(doc_id)
        if not e:
            return get_data_error_result(message="Document not found!")
            
        # 检查文档是否有源URL
        if not hasattr(doc, 'source_url') or not doc.source_url:
            return get_json_result(
                data=False, 
                message='此文档没有关联的源URL，无法更新', 
                code=settings.RetCode.ARGUMENT_ERROR
            )
            
        source_url = doc.source_url
        source_type = getattr(doc, 'source_type', None)
        
        # 根据源类型处理更新
        if source_type == 'citadel' or re.search(r"km\.sankuai\.com/collabpage/\d+", source_url):
            # 学城文档更新
            from api.utils.citadel_loader import CitadelLoader
            
            loader = CitadelLoader()
            try:
                result = loader.load_document(doc_url=source_url)
                new_content = result.get('content', '')
                if not new_content:
                    return get_json_result(
                        data=False, 
                        message='从学城获取文档内容失败，可能文档已被删除或无访问权限', 
                        code=settings.RetCode.SERVER_ERROR
                    )
                    
                # 元数据处理
                meta_fields = {}
                if 'metadata' in result and 'doc_meta' in result['metadata']:
                    meta_fields = result['metadata']['doc_meta']
                    
                # 更新文件内容
                bucket, name = File2DocumentService.get_storage_address(doc_id=doc_id)
                STORAGE_IMPL.put(bucket, name, new_content.encode('utf-8'))
                
                # 更新数据库记录
                update_data = {
                    "size": len(new_content),
                    "last_update_time": datetime.datetime.now()
                }
                
                # 如果有元数据则更新
                if meta_fields:
                    update_data["meta_fields"] = meta_fields
                    
                if not DocumentService.update_by_id(doc_id, update_data):
                    return get_data_error_result(message="更新文档记录失败!")
                
                # 如果文档已经被处理过，需要重置进度以便重新处理
                if int(doc.run) != int(TaskStatus.UNSTART.value):
                    DocumentService.update_by_id(
                        doc_id, 
                        {"run": TaskStatus.UNSTART.value, "progress": 0, "progress_msg": ""}
                    )
                
                return get_json_result(
                    data=True, 
                    message="文档已从学城源成功更新"
                )
                
            except Exception as e:
                return server_error_response(ValueError(f"更新学城文档失败: {str(e)}"))
                
        elif source_type == 'web' or is_valid_url(source_url):
            # 网页文档更新
            try:
                blob = html2pdf(source_url)
                if not blob:
                    return server_error_response(ValueError("下载网页内容失败"))
                    
                # 更新文件内容
                bucket, name = File2DocumentService.get_storage_address(doc_id=doc_id)
                STORAGE_IMPL.put(bucket, name, blob)
                
                # 更新数据库记录
                thumb = thumbnail(doc.name, blob) if doc.type == FileType.VISUAL else doc.thumbnail
                update_data = {
                    "size": len(blob),
                    "thumbnail": thumb,
                    "last_update_time": datetime.datetime.now()
                }
                
                if not DocumentService.update_by_id(doc_id, update_data):
                    return get_data_error_result(message="更新文档记录失败!")
                
                # 如果文档已经被处理过，需要重置进度以便重新处理
                if int(doc.run) != int(TaskStatus.UNSTART.value):
                    DocumentService.update_by_id(
                        doc_id, 
                        {"run": TaskStatus.UNSTART.value, "progress": 0, "progress_msg": ""}
                    )
                
                return get_json_result(
                    data=True, 
                    message="文档已从网页源成功更新"
                )
                
            except Exception as e:
                return server_error_response(ValueError(f"更新网页文档失败: {str(e)}"))
        else:
            return get_json_result(
                data=False, 
                message='不支持此类型文档的更新', 
                code=settings.RetCode.ARGUMENT_ERROR
            )
            
    except Exception as e:
        return server_error_response(e)


@manager.route('/schedule_update', methods=['POST'])  # noqa: F821
@login_required
@validate_request("doc_id", "update_interval")
def schedule_update():
    """
    设置文档的自动更新周期
    
    参数:
    - doc_id: 文档ID
    - update_interval: 更新间隔天数，0表示不自动更新
    """
    req = request.json
    doc_id = req["doc_id"]
    update_interval = int(req["update_interval"])
    
    if update_interval < 0:
        return get_json_result(
            data=False,
            message='更新间隔不能为负数',
            code=settings.RetCode.ARGUMENT_ERROR
        )
    
    if not DocumentService.accessible(doc_id, current_user.id):
        return get_json_result(
            data=False,
            message='No authorization.',
            code=settings.RetCode.AUTHENTICATION_ERROR
        )
        
    try:
        # 获取文档信息
        e, doc = DocumentService.get_by_id(doc_id)
        if not e:
            return get_data_error_result(message="Document not found!")
            
        # 检查文档是否有源URL
        if not hasattr(doc, 'source_url') or not doc.source_url:
            return get_json_result(
                data=False, 
                message='此文档没有关联的源URL，无法设置自动更新', 
                code=settings.RetCode.ARGUMENT_ERROR
            )
            
        # 更新文档的自动更新设置
        update_data = {
            "update_interval": update_interval,
            "next_update_time": datetime.datetime.now() + datetime.timedelta(days=update_interval) if update_interval > 0 else None
        }
        
        if not DocumentService.update_by_id(doc_id, update_data):
            return get_data_error_result(message="设置自动更新失败!")
            
        message = "已成功设置自动更新" if update_interval > 0 else "已关闭自动更新"
        return get_json_result(
            data=True, 
            message=message
        )
        
    except Exception as e:
        return server_error_response(e)


@manager.route('/auto_update_documents', methods=['POST'])  # noqa: F821
def auto_update_documents():
    """
    自动更新到期的文档
    此API应该由定时任务调用，不需要用户登录
    
    可以通过cron job定期调用此API，例如：
    curl -X POST -H "Authorization: Bearer API_KEY" http://your-domain/v1/document/auto_update_documents
    """
    # 安全验证 - 使用API密钥而非用户登录
    api_key = request.headers.get('Authorization', '')
    if not api_key.startswith('Bearer '):
        return get_json_result(
            data=False,
            message='Missing or invalid API key',
            code=settings.RetCode.AUTHENTICATION_ERROR
        )
        
    api_key = api_key.replace('Bearer ', '')
    if api_key != settings.AUTO_UPDATE_API_KEY:  # 需要在settings中定义
        return get_json_result(
            data=False,
            message='Invalid API key',
            code=settings.RetCode.AUTHENTICATION_ERROR
        )
        
    try:
        # 获取所有需要更新的文档
        now = datetime.datetime.now()
        docs_to_update = []
        
        # 这里需要根据实际数据库模型调整查询方式
        # 假设有一个方法可以获取到期的文档
        docs_to_update = DocumentService.get_docs_for_update(now)
        
        if not docs_to_update:
            return get_json_result(
                data=True,
                message='No documents need to be updated'
            )
            
        update_results = {
            "success": 0,
            "failed": 0,
            "details": []
        }
        
        for doc in docs_to_update:
            try:
                # 如果没有源URL或更新间隔，跳过
                if not hasattr(doc, 'source_url') or not doc.source_url or not hasattr(doc, 'update_interval') or not doc.update_interval:
                    continue
                    
                source_url = doc.source_url
                doc_id = doc.id
                
                # 根据源类型更新文档
                if hasattr(doc, 'source_type') and doc.source_type == 'citadel' or re.search(r"km\.sankuai\.com/collabpage/\d+", source_url):
                    # 学城文档更新
                    from api.utils.citadel_loader import CitadelLoader
                    loader = CitadelLoader()
                    result = loader.load_document(doc_url=source_url)
                    new_content = result.get('content', '')
                    
                    if new_content:
                        # 元数据处理
                        meta_fields = {}
                        if 'metadata' in result and 'doc_meta' in result['metadata']:
                            meta_fields = result['metadata']['doc_meta']
                            
                        # 更新文件内容
                        bucket, name = File2DocumentService.get_storage_address(doc_id=doc_id)
                        STORAGE_IMPL.put(bucket, name, new_content.encode('utf-8'))
                        
                        # 更新数据库记录
                        update_data = {
                            "size": len(new_content),
                            "last_update_time": now,
                            "next_update_time": now + datetime.timedelta(days=doc.update_interval)
                        }
                        
                        if meta_fields:
                            update_data["meta_fields"] = meta_fields
                            
                        DocumentService.update_by_id(doc_id, update_data)
                        
                        # 如果文档已经被处理过，需要重置进度以便重新处理
                        if int(doc.run) != int(TaskStatus.UNSTART.value):
                            DocumentService.update_by_id(
                                doc_id, 
                                {"run": TaskStatus.UNSTART.value, "progress": 0, "progress_msg": ""}
                            )
                            
                        update_results["success"] += 1
                        update_results["details"].append({
                            "doc_id": doc_id,
                            "name": doc.name,
                            "status": "success",
                            "message": "更新成功"
                        })
                    else:
                        update_results["failed"] += 1
                        update_results["details"].append({
                            "doc_id": doc_id,
                            "name": doc.name,
                            "status": "failed",
                            "message": "获取内容失败"
                        })
                    
                elif hasattr(doc, 'source_type') and doc.source_type == 'web' or is_valid_url(source_url):
                    # 网页文档更新
                    blob = html2pdf(source_url)
                    if blob:
                        # 更新文件内容
                        bucket, name = File2DocumentService.get_storage_address(doc_id=doc_id)
                        STORAGE_IMPL.put(bucket, name, blob)
                        
                        # 更新数据库记录
                        thumb = thumbnail(doc.name, blob) if doc.type == FileType.VISUAL else doc.thumbnail
                        update_data = {
                            "size": len(blob),
                            "thumbnail": thumb,
                            "last_update_time": now,
                            "next_update_time": now + datetime.timedelta(days=doc.update_interval)
                        }
                        
                        DocumentService.update_by_id(doc_id, update_data)
                        
                        # 如果文档已经被处理过，需要重置进度以便重新处理
                        if int(doc.run) != int(TaskStatus.UNSTART.value):
                            DocumentService.update_by_id(
                                doc_id, 
                                {"run": TaskStatus.UNSTART.value, "progress": 0, "progress_msg": ""}
                            )
                            
                        update_results["success"] += 1
                        update_results["details"].append({
                            "doc_id": doc_id,
                            "name": doc.name,
                            "status": "success",
                            "message": "更新成功"
                        })
                    else:
                        update_results["failed"] += 1
                        update_results["details"].append({
                            "doc_id": doc_id,
                            "name": doc.name,
                            "status": "failed",
                            "message": "下载内容失败"
                        })
                
            except Exception as e:
                update_results["failed"] += 1
                update_results["details"].append({
                    "doc_id": doc.id,
                    "name": doc.name,
                    "status": "failed",
                    "message": str(e)
                })
                logger.error(f"自动更新文档 {doc.id} 失败: {str(e)}")
                
        return get_json_result(
            data=update_results,
            message=f"自动更新完成。成功: {update_results['success']}, 失败: {update_results['failed']}"
        )
                
    except Exception as e:
        logger.error(f"自动更新文档过程中发生错误: {str(e)}")
        return server_error_response(e)
