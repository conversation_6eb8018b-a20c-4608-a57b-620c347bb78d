---
description: 
globs: 
alwaysApply: false
---
# description
本规则适用于 shangou_ai_rag 项目 api 目录下的后端代码（Flask + Peewee/SQLAlchemy + 多服务分层架构）。

- 当你需要生成、补全、重构、理解 api 相关的接口、服务、模型、工具、配置、认证、权限、蓝图注册等代码时，优先参考本规则。
- 典型场景包括：API接口开发、业务服务实现、数据库模型定义、工具函数、配置管理、认证鉴权、异常处理、自动路由注册等。
- 适用于所有涉及 api/ 目录的 Python 代码。

---

# cursor-rules.mdc

## 1. 目录结构与模块职责

- `api/apps/`：每个 *_app.py 为一个业务API蓝图，负责路由注册、请求参数校验、调用 service 层、返回标准响应。自动注册所有 *_app.py 到 Flask 应用。
- `api/db/`：数据库模型（db_models.py）、ORM适配、数据访问层、服务实现（services/），推荐 Peewee/SQLAlchemy，模型定义规范，服务层负责业务逻辑。
- `api/db/services/`：每个 service 负责一类业务（如 user_service、document_service、llm_service 等），service 只处理业务逻辑和数据操作，不直接处理 HTTP 请求。
- `api/utils/`：通用工具函数、配置加载、日志、加解密、SSO、文件处理、API通用逻辑、异常处理等。
- `api/settings.py`：全局配置、环境变量、数据库、LLM、对象存储、认证、SSO等配置集中管理，支持多环境。
- `api/constants.py`：全局常量、枚举、API版本等。
- `api/versions.py`：版本信息、自动获取 git tag、版本号等。
- `api/__init__.py`、`api/apps/__init__.py`：Flask 应用初始化、Swagger、CORS、Session、LoginManager、自动注册蓝图、全局异常处理、数据库连接管理等。

## 2. 代码风格与规范

- 统一使用 Python 3.10+，类型注解推荐但非强制。
- 路由注册统一用 Flask Blueprint，所有业务API均为 *_app.py，自动注册。
- 路由风格 RESTful，路径小写、动词复数，参数校验用 pydantic/自定义校验。
- 业务逻辑与 HTTP 解耦，API 只负责参数校验、调用 service、返回响应。
- 数据库模型定义规范，字段全小写、下划线分隔，主键/索引/外键明确。
- 配置、密钥、环境变量统一在 settings.py 管理，严禁硬编码。
- 工具函数、通用逻辑优先抽到 utils，避免重复代码。
- 异常处理统一，所有异常需捕获并返回标准 JSON 响应。
- 日志、认证、权限、Session、CORS、Swagger 等全局中间件在 __init__.py 配置。
- 代码需有适当 docstring 注释，复杂逻辑需详细说明。

## 3. 主要数据流与交互

- API 层（apps/）只负责 HTTP 路由、参数校验、调用 service、返回响应。
- Service 层（db/services/）负责业务逻辑、数据操作、事务管理。
- Model 层（db_models.py）定义所有表结构、字段、索引、关系。
- 工具层（utils/）负责通用能力，如配置、加解密、日志、文件、SSO、API通用逻辑等。
- 配置层（settings.py）集中管理所有环境、服务、密钥、模型、存储等配置。
- 全局异常、认证、Session、CORS、Swagger、数据库连接等在 __init__.py 统一注册。

## 4. 典型开发流程

- 新增业务API：在 apps/ 下新建 xxx_app.py，定义 Blueprint，注册路由，参数校验，调用 service，返回响应。
- 新增业务逻辑：在 db/services/ 下新建 xxx_service.py，实现具体业务逻辑，暴露方法供 API 调用。
- 新增数据表/模型：在 db/db_models.py 定义新表，字段、索引、关系需规范，支持迁移。
- 新增工具/通用逻辑：在 utils/ 下新建工具模块，供全局复用。
- 配置变更：统一在 settings.py 修改，支持多环境变量。
- 全局中间件、异常、认证、Session、Swagger、CORS 等在 __init__.py 配置。

## 5. 其他约定

- 所有 API 路径前缀统一为 `/shangou_ai_rag/api/v1/`，自动注册。
- 返回值统一为 JSON，包含 code、msg、data 字段，异常需有 code、msg。
- 认证、权限、Session、CORS、Swagger、数据库连接等全局统一处理。
- 复杂业务建议拆分 service 层，API 层只做转发和响应。
- 日志、异常、告警需详细，便于排查和监控。
- 代码提交前需通过 lint、单元测试，重要变更需补充注释和文档。

---

如需扩展 cursor 规则，优先参考现有目录和类型定义，保持风格一致。
