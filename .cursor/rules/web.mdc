---
description: 
globs: 
alwaysApply: false
---
# description
本规则适用于 shangou_ai_rag 项目 web 目录下的前端代码（React + UmiJS + Ant Design + Tailwind CSS）。

- 当你需要生成、补全、重构、理解 web 相关的页面、组件、hooks、service、类型定义、样式、路由等代码时，优先参考本规则。
- 典型场景包括：知识库管理、RAG流程编排、智能问答、文件上传解析、API交互、UI组件复用、类型约束、国际化等。
- 适用于所有涉及 web/src 目录的 TypeScript/React 代码。

---

# cursor-rules.mdc

## 1. 目录结构与模块职责

- `src/pages/knowledge/`：知识库管理主页面，支持知识库的增删查改、搜索、卡片展示、创建弹窗等，核心数据结构为 `IKnowledge`。
- `src/pages/flow/`：RAG流程编排与可视化，基于 ReactFlow，支持节点拖拽、流程设计、侧边栏、头部、画布等，核心数据结构为 `RAGFlowNodeType`。
- `src/pages/chat/`：智能问答与多轮对话，支持会话管理、对话卡片、嵌入、配置、重命名、删除等，核心数据结构为 `IDialog`。
- `src/services/`：前端所有与后端API的交互封装，推荐通过 service 层调用接口，避免直接写 request。
- `src/interfaces/database/`：所有核心数据结构和类型定义，前后端数据流的标准。
- `src/components/`：通用UI组件和业务组件，优先复用，样式统一。
- `src/utils/`：工具函数，包含 request 封装、表单、文件、授权、通用逻辑等。
- `src/hooks/`：自定义 hooks，负责数据拉取、状态管理、业务逻辑复用。
- `src/locales/`：多语言支持，所有文案需通过 i18n 实现。

## 2. 代码风格与规范

- 统一使用 TypeScript，类型安全，类型定义优先查找 `src/interfaces/`。
- 组件、变量、方法命名采用小驼峰（camelCase），类型/接口采用大驼峰（PascalCase）。
- 组件职责单一，拆分合理，复用性强。
- 状态管理优先使用 Zustand，数据请求统一用 React Query。
- UI 风格统一，遵循 Ant Design 设计体系，样式采用 Tailwind CSS 原子化。
- 路由统一在 `src/routes.ts` 配置，页面入口需在 routes 注册。
- API 调用统一通过 `src/services/`，避免直接写 URL。
- 业务数据流动以 hooks + service + 组件 props 为主，props 传递需类型明确。
- 组件样式优先使用 Tailwind，特殊场景可用 less，样式文件与组件同名。

## 3. 主要数据结构

- 知识库：`IKnowledge`，详见 `src/interfaces/database/knowledge.ts`
- 文档：`IKnowledgeFile`
- 分块：`IChunk`
- 流程节点：`RAGFlowNodeType`，详见 `src/interfaces/database/flow.ts`
- 对话：`IDialog`，详见 `src/interfaces/database/chat.ts`

## 4. 典型交互流程

- 知识库页面：通过 `useInfiniteFetchKnowledgeList` 拉取知识库列表，支持搜索、创建、卡片展示，创建弹窗用 `KnowledgeCreatingModal`。
- 流程编排页面：基于 ReactFlow，节点拖拽、连线、属性编辑，数据通过 hooks 管理，节点类型和属性详见 constant.tsx。
- 智能问答页面：会话列表、对话卡片、配置弹窗、重命名、删除等，数据流动通过 hooks 管理，API 通过 chat-service.ts。
- 文件上传、解析、分块、检索等均有对应 service 和 hooks，前端只负责交互和展示，业务逻辑在后端。

## 5. 其他约定

- 多语言文案统一用 `useTranslation` 或 `useTranslate`，文案 key 需在 `src/locales/` 注册。
- 组件/页面如需复用，优先抽到 `src/components/`，并补充类型定义。
- 新增页面需在 routes.ts 注册，目录结构与路由保持一致。
- 复杂表单、弹窗、流程建议拆分 hooks 和 UI，便于维护和测试。
- 业务常量、枚举统一放在 `src/constants/`，避免魔法字符串。

