#!/usr/bin/env bash
set -e

echo "🚀 Ragas依赖包准备脚本"
echo "========================================"

# 检查是否在正确的环境
if ! command -v pip &> /dev/null; then
    echo "❌ 错误: pip未找到，请确保Python环境正确"
    exit 1
fi

# 创建工作目录
WORK_DIR="ragas_package_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$WORK_DIR"
cd "$WORK_DIR"

echo "📁 工作目录: $(pwd)"

# 下载Ragas相关依赖
echo "📦 下载Ragas核心依赖..."
mkdir -p wheels

# 核心包列表
CORE_PACKAGES=(
    "ragas==0.1.9"
    "datasets==2.19.1"
)

# 下载核心包及其所有依赖
for package in "${CORE_PACKAGES[@]}"; do
    echo "📥 下载 $package 及其完整依赖树..."
    pip download --dest wheels "$package"
done

# 额外确保关键依赖
EXTRA_PACKAGES=(
    "langchain-core"
    "langchain" 
    "langsmith"
    "transformers"
    "torch"
    "tokenizers"
    "safetensors"
    "huggingface-hub"
    "fsspec"
    "dill"
    "multiprocess"
    "pyarrow"
    "xxhash"
    "aiohttp"
    "pydantic"
    "numpy"
    "pandas"
)

echo "📥 下载额外关键依赖..."
for package in "${EXTRA_PACKAGES[@]}"; do
    echo "📥 下载 $package..."
    pip download --dest wheels "$package" || echo "⚠️ $package 下载失败，可能已存在"
done

# 统计下载的包
WHEEL_COUNT=$(ls wheels/*.whl 2>/dev/null | wc -l)
echo "📊 共下载 $WHEEL_COUNT 个wheel文件"

# 创建安装脚本
cat > install_ragas.sh << 'EOF'
#!/usr/bin/env bash
set -e

echo "🔧 安装Ragas依赖..."

# 检查wheels目录
if [ ! -d "wheels" ]; then
    echo "❌ 错误: wheels目录不存在"
    exit 1
fi

# 激活虚拟环境（如果存在）
if [ -f "venv/bin/activate" ]; then
    echo "🔧 激活虚拟环境..."
    source venv/bin/activate
fi

# 从本地wheel文件安装
echo "📦 从本地wheel文件安装..."
pip install --find-links wheels --no-index ragas==0.1.9 datasets==2.19.1

echo "✅ Ragas依赖安装完成！"

# 验证安装
python -c "
import ragas
import datasets
print(f'✅ Ragas {ragas.__version__} 安装成功')
print(f'✅ Datasets {datasets.__version__} 安装成功')
"
EOF

chmod +x install_ragas.sh

# 打包
PACKAGE_NAME="ragas_deps_$(date +%Y%m%d_%H%M%S).tar.gz"
echo "📦 打包到: $PACKAGE_NAME"

tar -czf "../$PACKAGE_NAME" wheels/ install_ragas.sh

cd ..

# 显示结果
echo ""
echo "✅ 打包完成！"
echo "📁 包文件: $PACKAGE_NAME"
echo "📊 包大小: $(du -h "$PACKAGE_NAME" | cut -f1)"
echo ""
echo "📋 下一步操作："
echo "1. 将 $PACKAGE_NAME 上传到S3:"
echo "   https://msstest.sankuai.com/sg-ug-api-test-sdk-agent/ragas_deps_latest.tar.gz"
echo ""
echo "2. 在服务器上运行 build.sh 进行部署"
echo ""
echo "🔍 本地测试（可选）:"
echo "   tar -xzf $PACKAGE_NAME"
echo "   cd ragas_package_*"
echo "   ./install_ragas.sh"

# 清理工作目录
rm -rf "$WORK_DIR"
echo "🧹 清理临时目录完成"
