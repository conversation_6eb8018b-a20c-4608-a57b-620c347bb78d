import mysql.connector
import sys

# 数据库连接配置
config = {
    'host': 'localhost',      # Docker MySQL的主机名/IP
    'port': 5455,             # MySQL端口（可能需要根据Docker配置调整）
    'user': 'root',           # MySQL用户名
    'password': 'infini_rag_flow',   # MySQL密码
    'database': 'rag_flow'     # 数据库名
}

# 要添加的字段
fields_to_add = [
    ("source_type", "VARCHAR(20) DEFAULT NULL"),
    ("source_url", "TEXT DEFAULT NULL"),
    ("last_update_time", "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"),
    ("update_interval", "INT DEFAULT NULL"),
    ("next_update_time", "TIMESTAMP NULL DEFAULT NULL")
]

try:
    # 连接到MySQL
    print("正在连接到MySQL数据库...")
    conn = mysql.connector.connect(**config)
    cursor = conn.cursor()
    
    # 获取当前表列
    cursor.execute("DESCRIBE document")
    existing_columns = [row[0] for row in cursor.fetchall()]
    print(f"现有列: {', '.join(existing_columns)}")
    
    # 添加缺失的列
    for field_name, field_def in fields_to_add:
        if field_name not in existing_columns:
            sql = f"ALTER TABLE document ADD COLUMN {field_name} {field_def}"
            print(f"执行: {sql}")
            cursor.execute(sql)
            print(f"字段 {field_name} 添加成功")
        else:
            print(f"字段 {field_name} 已存在，跳过")
    
    # 提交更改
    conn.commit()
    
    # 验证结果
    cursor.execute("DESCRIBE document")
    new_columns = [row[0] for row in cursor.fetchall()]
    print(f"更新后的列: {', '.join(new_columns)}")
    
    print("迁移完成")
    
except mysql.connector.Error as err:
    print(f"MySQL错误: {err}")
    sys.exit(1)
    
finally:
    if 'conn' in locals() and conn.is_connected():
        cursor.close()
        conn.close()
        print("数据库连接已关闭")