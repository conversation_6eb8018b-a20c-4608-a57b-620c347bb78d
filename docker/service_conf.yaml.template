ragflow:
  host: ${RAGFLOW_HOST:-0.0.0.0}
  http_port: 9380
mysql:
  name: '${MYSQL_DBNAME:-rag_flow}'
  user: '${MYSQL_USER:-root}'
  password: '${MYSQL_PASSWORD:-infini_rag_flow}'
  host: '${MYSQL_HOST:-mysql}'
  port: 3306
  max_connections: 100
  stale_timeout: 30
minio:
  user: '${MINIO_USER:-rag_flow}'
  password: '${MINIO_PASSWORD:-infini_rag_flow}'
  host: '${MINIO_HOST:-minio}:9000'
es:
  hosts: 'http://${ES_HOST:-es01}:9200'
  username: '${ES_USER:-elastic}'
  password: '${ELASTIC_PASSWORD:-infini_rag_flow}'
infinity:
  uri: '${INFINITY_HOST:-infinity}:23817'
  db_name: 'default_db'
redis:
  db: 1
  password: '${REDIS_PASSWORD:-infini_rag_flow}'
  host: '${REDIS_HOST:-redis}:6379'

# postgres:
#   name: '${POSTGRES_DBNAME:-rag_flow}'
#   user: '${POSTGRES_USER:-rag_flow}'
#   password: '${POSTGRES_PASSWORD:-infini_rag_flow}'
#   host: '${POSTGRES_HOST:-postgres}'
#   port: 5432
#   max_connections: 100
#   stale_timeout: 30
# s3:
#   access_key: 'access_key'
#   secret_key: 'secret_key'
#   region: 'region'
# oss:
#   access_key: '${ACCESS_KEY}'
#   secret_key: '${SECRET_KEY}'
#   endpoint_url: '${ENDPOINT}'
#   region: '${REGION}'
#   bucket: '${BUCKET}'
#   prefix_path: '${OSS_PREFIX_PATH}'
# azure:
#   auth_type: 'sas'
#   container_url: 'container_url'
#   sas_token: 'sas_token'
# azure:
#   auth_type: 'spn'
#   account_url: 'account_url'
#   client_id: 'client_id'
#   secret: 'secret'
#   tenant_id: 'tenant_id'
#   container_name: 'container_name'
user_default_llm:
   factory: 'Friday'
   api_key: '1837019944677314648'
   base_url: 'https://aigc.sankuai.com/v1/openai/native'
#   default_models:
#     chat_model: 'qwen-plus'
#     embedding_model: 'BAAI/bge-large-zh-v1.5@BAAI'
#     rerank_model: ''
#     asr_model: ''
#     image2text_model: ''
# oauth:
#   github:
#     client_id: xxxxxxxxxxxxxxxxxxxxxxxxx
#     secret_key: xxxxxxxxxxxxxxxxxxxxxxxxxxxx
#     url: https://github.com/login/oauth/access_token
#   feishu:
#     app_id: cli_xxxxxxxxxxxxxxxxxxx
#     app_secret: xxxxxxxxxxxxxxxxxxxxxxxxxxxx
#     app_access_token_url: https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal
#     user_access_token_url: https://open.feishu.cn/open-apis/authen/v1/oidc/access_token
#     grant_type: 'authorization_code'
# authentication:
#   client:
#     switch: false
#     http_app_key:
#     http_secret_key:
#   site:
#     switch: false
# permission:
#   switch: false
#   component: false
#   dataset: false
