#!/bin/bash

# 确保脚本以bash运行
if [ -z "$BASH_VERSION" ]; then
  echo "此脚本必须在bash中运行，正在切换到bash..."
  exec bash "$0" "$@"
  exit $?
fi

# Exit immediately if a command exits with a non-zero status
set -e

# 设置tiktoken缓存目录为项目下的.tiktoken目录（使用绝对路径）
export TIKTOKEN_CACHE_DIR=$(pwd)/.tiktoken
# 转换为绝对路径
export TIKTOKEN_CACHE_DIR=$(realpath "$TIKTOKEN_CACHE_DIR")
export TIKTOKEN_SKIP_BPE_DOWNLOAD=1
echo "tiktoken缓存目录(绝对路径): $TIKTOKEN_CACHE_DIR"

# 验证tiktoken文件是否存在
if [ -f "$TIKTOKEN_CACHE_DIR/cl100k_base.tiktoken" ]; then
    echo "✅ 已找到tiktoken编码文件: $TIKTOKEN_CACHE_DIR/cl100k_base.tiktoken"
    # 显示文件大小和权限
    ls -la "$TIKTOKEN_CACHE_DIR/cl100k_base.tiktoken"
    # 文件头信息
    echo "文件头信息 (前32字节):"
    hexdump -C "$TIKTOKEN_CACHE_DIR/cl100k_base.tiktoken" | head -n 2
    # 修改文件权限确保可读
    chmod 644 "$TIKTOKEN_CACHE_DIR/cl100k_base.tiktoken" 2>/dev/null || true
    echo "✅ 已设置tiktoken文件权限"
    # 再次显示权限
    ls -la "$TIKTOKEN_CACHE_DIR/cl100k_base.tiktoken"
else
    echo "⚠️ 警告: 未找到tiktoken编码文件，服务可能无法正常启动"
    echo "预期文件路径: $TIKTOKEN_CACHE_DIR/cl100k_base.tiktoken"
fi

# Unset HTTP proxies that might be set by Docker daemon
export http_proxy=""; export https_proxy=""; export no_proxy=""; export HTTP_PROXY=""; export HTTPS_PROXY=""; export NO_PROXY=""
#export PYTHONPATH=$(pwd)
export PYTHONPATH=$(pwd)${PYTHONPATH:+:$PYTHONPATH}

export LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu/

# 自动检测Python环境
# 根据环境设置PY变量
if [ -n "$RAG_ENV" ] && [ "$RAG_ENV" = "test" ]; then
  # 测试环境使用的Python路径
  PY=venv/bin/python3
  echo "检测到测试环境，使用Python路径: $PY"
elif [ -d "venv" ] && [ -f "venv/bin/python3" ]; then
  # 如果存在venv虚拟环境，使用它
  PY=venv/bin/python3
  echo "检测到虚拟环境，使用Python路径: $PY"
else
  # 默认使用系统Python
  PY=python3
  echo "使用系统Python路径: $PY"
fi

# 允许通过环境变量强制指定Python路径
if [ -n "$PYTHON_PATH" ]; then
  PY="$PYTHON_PATH"
  echo "使用指定的Python路径: $PY"
fi

# Set default number of workers if WS is not set or less than 1
if [[ -z "$WS" || $WS -lt 1 ]]; then
  WS=1
fi

# Maximum number of retries for each task executor and server
MAX_RETRIES=5

# Flag to control termination
STOP=false

# Array to keep track of child PIDs
PIDS=()

# 添加互斥锁文件，防止多个实例同时运行
LOCK_FILE="/tmp/ragflow_service.lock"
LOCK_PID_FILE="${LOCK_FILE}/pid"

# 检查是否有过期的锁文件（超过1小时）
if [ -d "$LOCK_FILE" ] && [ -f "$LOCK_PID_FILE" ]; then
  LOCK_TIME=$(stat -c %Y "$LOCK_FILE" 2>/dev/null || stat -f %m "$LOCK_FILE" 2>/dev/null)
  CURRENT_TIME=$(date +%s)
  DIFF_TIME=$((CURRENT_TIME - LOCK_TIME))
  
  # 如果锁文件存在超过1小时，可能是之前服务异常退出
  if [ $DIFF_TIME -gt 3600 ]; then
    echo "发现过期的锁文件（超过1小时），正在清理..."
    rm -rf "$LOCK_FILE"
  else
    # 检查PID文件中的进程是否存在
    if [ -f "$LOCK_PID_FILE" ]; then
      OLD_PID=$(cat "$LOCK_PID_FILE")
      if kill -0 "$OLD_PID" 2>/dev/null; then
        # 进程仍在运行
        echo "另一个服务实例(PID: $OLD_PID)正在运行。"
        
        # 如果指定了FORCE=1，则强制终止旧进程并接管
        if [ "$FORCE" = "1" ]; then
          echo "强制标志已设置，正在终止旧进程并接管..."
          kill -TERM "$OLD_PID" 2>/dev/null || true
          sleep 3
          if kill -0 "$OLD_PID" 2>/dev/null; then
            echo "旧进程仍在运行，正在强制终止..."
            kill -9 "$OLD_PID" 2>/dev/null || true
          fi
          rm -rf "$LOCK_FILE"
        else
          echo "如果确定没有其他实例运行，可以通过以下方式解决："
          echo "1. 删除锁文件: rm -rf $LOCK_FILE"
          echo "2. 使用FORCE=1环境变量强制启动: FORCE=1 $0"
          exit 1
        fi
      else
        # PID存在但进程不存在，清理旧锁
        echo "发现过时的锁文件，之前的进程已不存在。正在清理..."
        rm -rf "$LOCK_FILE"
      fi
    fi
  fi
fi

# 创建锁目录
if ! mkdir -p "$LOCK_FILE" 2>/dev/null; then
  echo "无法创建锁文件目录，可能没有权限。"
  exit 1
fi

# 记录当前PID到锁文件
echo $$ > "$LOCK_PID_FILE"

# Function to handle termination signals
cleanup() {
  echo "接收到终止信号。正在关闭所有服务..."
  STOP=true
  
  # 向所有子进程发送SIGTERM，给它们时间清理
  for pid in "${PIDS[@]}"; do
    if kill -0 "$pid" 2>/dev/null; then
      echo "向进程 $pid 发送 SIGTERM 信号"
      kill -TERM "$pid" 2>/dev/null || true
    fi
  done
  
  # 等待子进程退出的时间
  echo "等待子进程安全退出 (5秒)..."
  sleep 5
  
  # 检查是否有进程仍在运行，如果有则强制终止
  for pid in "${PIDS[@]}"; do
    if kill -0 "$pid" 2>/dev/null; then
      echo "进程 $pid 仍在运行，发送 SIGKILL 强制终止"
      kill -9 "$pid" 2>/dev/null || true
    fi
  done
  
  # 删除锁文件
  if [ -d "$LOCK_FILE" ]; then
    echo "删除服务锁文件: $LOCK_FILE"
    rm -rf "$LOCK_FILE"
  fi
  
  # 检查并删除PID文件
  echo "检查是否有残留的PID文件..."
  # 使用通配符安全检查方式，不依赖于shopt
  PID_FILES=$(find /tmp -name "task_executor_*.pid" 2>/dev/null || echo "")
  if [ -n "$PID_FILES" ]; then
    for pid_file in $PID_FILES; do
      echo "删除残留的PID文件: $pid_file"
      rm -f "$pid_file"
    done
  else
    echo "未发现残留的PID文件"
  fi
  
  echo "所有服务已关闭"
  exit 0
}

# Trap SIGINT and SIGTERM to invoke cleanup
trap cleanup SIGINT SIGTERM EXIT

# Function to execute task_executor with retry logic
task_exe(){
    local base_task_id=$1
    local hostname=$(hostname)
    local pid=$$
    local timestamp=$(date +%s)
    # 生成唯一的task_id，结合了基础ID、主机名哈希、进程ID和时间戳
    # 检测操作系统并使用适当的命令来生成MD5哈希
    local md5_cmd
    if command -v md5sum > /dev/null 2>&1; then
        # Linux系统上使用md5sum
        md5_cmd="md5sum | cut -c1-8"
    else
        # macOS系统上使用md5
        md5_cmd="md5 | cut -c1-8"
    fi
    
    # 使用检测到的命令生成哈希
    local unique_suffix=$(echo "$hostname-$pid-$timestamp" | eval $md5_cmd)
    
    # 确保生成了有效的后缀
    if [ -z "$unique_suffix" ]; then
        # 如果仍然失败，使用时间戳和进程ID作为后缀
        unique_suffix="${timestamp:(-4)}_${pid:(-4)}"
        echo "警告: 无法生成MD5哈希，使用备用后缀: $unique_suffix"
    fi
    
    # 添加调试输出
    echo "调试信息: 主机名=$hostname, 进程ID=$pid, 时间戳=$timestamp, 后缀=$unique_suffix"
    
    local task_id="${base_task_id}_${unique_suffix}"
    
    # 允许通过环境变量强制指定task_id
    if [ -n "$FORCE_TASK_ID" ]; then
        task_id="$FORCE_TASK_ID"
        echo "使用指定的任务ID: $task_id"
    fi
    
    # local task_id=300
    local retry_count=0
    while ! $STOP && [ $retry_count -lt $MAX_RETRIES ]; do
        echo "正在启动 task_executor.py，任务 ID: $task_id (尝试 $((retry_count+1)))"
        $PY rag/svr/task_executor.py "$task_id"
        EXIT_CODE=$?
        if [ $EXIT_CODE -eq 0 ]; then
            echo "task_executor.py (任务 ID: $task_id) 成功退出。"
            break
        else
            # 如果是因为收到信号而退出，不要重试
            if [ $EXIT_CODE -eq 130 ] || [ $EXIT_CODE -eq 143 ]; then
                echo "task_executor.py (任务 ID: $task_id) 因接收到终止信号而退出。"
                break
            fi
            echo "task_executor.py (任务 ID: $task_id) 失败，退出码: $EXIT_CODE。正在重试..." >&2
            retry_count=$((retry_count + 1))
            sleep 2
        fi
    done

    if [ $retry_count -ge $MAX_RETRIES ]; then
        echo "task_executor.py (任务 ID: $task_id) 在 $MAX_RETRIES 次尝试后仍然失败。退出..." >&2
        cleanup
    fi
}

# Function to execute ragflow_server with retry logic
run_server(){
    local retry_count=0
    while ! $STOP && [ $retry_count -lt $MAX_RETRIES ]; do
        echo "正在启动 ragflow_server.py (尝试 $((retry_count+1)))"
        $PY api/ragflow_server.py
        EXIT_CODE=$?
        if [ $EXIT_CODE -eq 0 ]; then
            echo "ragflow_server.py 成功退出。"
            break
        else
            # 如果是因为收到信号而退出，不要重试
            if [ $EXIT_CODE -eq 130 ] || [ $EXIT_CODE -eq 143 ]; then
                echo "ragflow_server.py 因接收到终止信号而退出。"
                break
            fi
            echo "ragflow_server.py 失败，退出码: $EXIT_CODE。正在重试..." >&2
            retry_count=$((retry_count + 1))
            sleep 2
        fi
    done

    if [ $retry_count -ge $MAX_RETRIES ]; then
        echo "ragflow_server.py 在 $MAX_RETRIES 次尝试后仍然失败。退出..." >&2
        cleanup
    fi
}

# 在启动新实例前检查并清理可能已经运行的task_executor进程
echo "检查是否有旧的task_executor进程..."
# 使用grep -q检查是否有匹配进程，避免无匹配时出错
if ps aux | grep -q "[t]ask_executor.py"; then
    # 找到匹配进程后再获取PID列表
    OLD_EXECS=$(ps aux | grep "[t]ask_executor.py" | awk '{print $2}')
    echo "发现 $(echo "$OLD_EXECS" | wc -l) 个旧的task_executor进程，正在终止它们..."
    for pid in $OLD_EXECS; do
        echo "正在终止旧的task_executor进程 (PID: $pid)..."
        kill -TERM "$pid" 2>/dev/null || true
    done
    
    # 等待进程退出
    sleep 3
    
    # 检查是否仍有进程运行，如果有则强制终止
    if ps aux | grep -q "[t]ask_executor.py"; then
        STILL_RUNNING=$(ps aux | grep "[t]ask_executor.py" | awk '{print $2}')
        echo "一些task_executor进程仍在运行，将强制终止它们..."
        for pid in $STILL_RUNNING; do
            echo "强制终止进程 (PID: $pid)..."
            kill -9 "$pid" 2>/dev/null || true
        done
    else
        echo "所有task_executor进程已成功终止。"
    fi
else
    echo "未发现旧的task_executor进程，继续启动..."
fi

# 清理可能存在的PID文件
echo "清理旧的PID文件..."
# 使用find命令替代shopt+通配符，以便在任何shell中工作
PID_FILES=$(find /tmp -name "task_executor_*.pid" 2>/dev/null || echo "")
if [ -n "$PID_FILES" ]; then
  for pid_file in $PID_FILES; do
    echo "删除旧的PID文件: $pid_file"
    rm -f "$pid_file"
  done
else
  echo "未发现旧的PID文件"
fi

# 解析参数，支持 -notask 跳过 task_exe 启动
SKIP_TASK_EXE=0
for arg in "$@"; do
  if [ "$arg" = "-notask" ]; then
    SKIP_TASK_EXE=1
    echo "检测到 -notask 参数，将跳过 task_executor 启动"
  fi
done

# Start task executors
if [ "$SKIP_TASK_EXE" -eq 0 ]; then
  for ((i=0;i<WS;i++))
  do
    task_exe "$i" &
    PIDS+=($!)
  done
else
  echo "已跳过 task_executor 启动"
fi

# Start the main server
run_server &
PIDS+=($!)

# Wait for all background processes to finish
wait
