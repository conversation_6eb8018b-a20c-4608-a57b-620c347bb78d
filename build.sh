#!/usr/bin/env bash
set -e
build_dir=$PWD
# 因为venv的依赖路径是写死的，此处需要到部署目录进行安装
target_dir=/opt/meituan/com.sankuai.shangou.ai.sgrag
cp pyproject.toml $target_dir
cp requirements.txt $target_dir
cp build.sh $target_dir
cd $target_dir
# 激活虚拟环境
virtualenv venv
source venv/bin/activate

# 下载并安装Ragas依赖包
echo "从S3下载Ragas依赖包..."
RAGAS_DEPS_URL="https://msstest.sankuai.com/sg-ug-api-test-sdk-agent/ragas_deps_latest.tar.gz"
RAGAS_DEPS_FILE="/tmp/ragas_deps.tar.gz"

# 使用curl下载Ragas依赖包
if curl -s -o "$RAGAS_DEPS_FILE" "$RAGAS_DEPS_URL"; then
    echo "Ragas依赖包下载成功，正在解压..."
    tar -xzf "$RAGAS_DEPS_FILE" -C "$target_dir"

    # 安装Ragas依赖
    if [ -d "$target_dir/ragas_wheels" ]; then
        echo "正在安装Ragas依赖..."
        pip install \
            --find-links "$target_dir/ragas_wheels" \
            --no-index \
            ragas==0.1.9 datasets==2.19.1
        echo "✅ Ragas依赖安装成功！"
    else
        echo "⚠️ Ragas wheels目录不存在"
    fi
else
    echo "❌ Ragas依赖包下载失败，尝试使用内网pip安装..."
    # 回退到原来的安装方式，但排除Ragas相关包
    pip3 install --index-url http://pypi.sankuai.com/simple/ -r requirements.txt --ignore-installed
fi

# 安装其他依赖（排除Ragas相关）
echo "安装其他系统依赖..."
pip3 install --index-url http://pypi.sankuai.com/simple/ -r requirements.txt --ignore-installed

# 下载并安装mini-racer wheel包
#echo "从S3下载mini-racer wheel包..."
#MINI_RACER_URL="https://msstest.sankuai.com/sg-ug-api-test-sdk-agent/mini_racer-0.12.4-py3-none-manylinux_2_31_x86_64.whl"
#MINI_RACER_WHEEL="/tmp/mini_racer-0.12.4-py3-none-manylinux_2_31_x86_64.whl"
#
## 使用curl下载wheel包
#if curl -s -o "$MINI_RACER_WHEEL" "$MINI_RACER_URL"; then
#    echo "mini-racer wheel包下载成功，正在安装..."
#    if pip install --no-index "$MINI_RACER_WHEEL"; then
#        echo "✅ mini-racer wheel包安装成功！"
#    else
#        echo "⚠️ mini-racer wheel包安装失败，可能与系统不兼容"
#    fi
#else
#    echo "❌ mini-racer wheel包下载失败"
#fi

mv venv $build_dir