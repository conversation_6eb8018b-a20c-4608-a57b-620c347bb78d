---
sidebar_position: 2
slug: /construct_knowledge_graph
---

# Construct knowledge graph

Generate a knowledge graph for your knowledge base.

---

To enhance multi-hop question-answering, RAGFlow adds a knowledge graph construction step between data extraction and indexing, as illustrated below. This step creates additional chunks from existing ones generated by your specified chunk method.

![Image](https://github.com/user-attachments/assets/1ec21d8e-f255-4d65-9918-69b72dfa142b)

As of v0.17.0, RAGFlow supports constructing a knowledge graph on a knowledge base, allowing you to construct a *unified* graph across multiple files within your knowledge base. When a newly uploaded file starts parsing, the generated graph will automatically update.

:::danger WARNING
Constructing a knowledge graph requires significant memory, computational resources, and tokens.
:::

## Scenarios

Knowledge graphs are especially useful for multi-hop question-answering involving *nested* logic. They outperform traditional extraction approaches when you are performing question answering on books or works with complex entities and relationships.

## Prerequisites

The system's default chat model is used to generate knowledge graph. Before proceeding, ensure that you have a chat model properly configured:

![Image](https://github.com/user-attachments/assets/6bc34279-68c3-4d99-8d20-b7bd1dafc1c1)

## Configurations

### Entity types (*Required*)

The types of the entities to extract from your knowledge base. The default types are: **organization**, **person**, **event**, and **category**. Add or remove types to suit your specific knowledge base.

### Method

The method to use to construct knowledge graph:

- **General**: Use prompts provided by [GraphRAG](https://github.com/microsoft/graphrag) to extract entities and relationships.
- **Light**: (Default) Use prompts provided by [LightRAG](https://github.com/HKUDS/LightRAG) to extract entities and relationships. This option consumes fewer tokens, less memory, and fewer computational resources.

### Entity resolution

Whether to enable entity resolution. You can think of this as an entity deduplication switch. When enabled, the LLM will combine similar entities - e.g., '2025' and 'the year of 2025', or 'IT' and 'Information Technology' - to construct a more effective graph.

- (Default) Disable entity resolution.
- Enable entity resolution. This option consumes more tokens.

### Community report generation

In a knowledge graph, a community is a cluster of entities linked by relationships. You can have the LLM generate an abstract for each community, known as a community report. See [here](https://www.microsoft.com/en-us/research/blog/graphrag-improving-global-search-via-dynamic-community-selection/) for more information. This indicates whether to generate community reports:

- Generate community reports. This option consumes more tokens.
- (Default) Do not generate community reports.

## Procedure

1. On the **Configuration** page of your knowledge base, switch on **Extract knowledge graph** or adjust its settings as needed, and click **Save** to confirm your changes.

   - *The default knowledge graph configurations for your knowledge base are now set and files uploaded from this point onward will automatically use these settings during parsing.*
   - *Files parsed before this update will retain their original knowledge graph settings.*

2. The knowledge graph of your knowledge base does *not* automatically update *until* a newly uploaded file is parsed.

   _A **Knowledge graph** entry appears under **Configuration** once a knowledge graph is created._

3. Click **Knowledge graph** to view the details of the generated graph.

## Frequently asked questions

### Can I have different knowledge graph settings for different files in my knowledge base?

Yes, you can. Just one graph is generated per knowledge base. The smaller graphs of your files will be *combined* into one big, unified graph at the end of the graph extraction process.

### Does the knowledge graph automatically update when I remove a related file?

Nope. The knowledge graph does *not* automatically update *until* a newly uploaded graph is parsed.

### How to remove a generated knowledge graph?

To remove the generated knowledge graph, delete all related files in your knowledge base. Although the **Knowledge graph** entry will still be visible, the graph has actually been deleted.