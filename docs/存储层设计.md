# RAGFlow 知识库存储结构分析

## 1. 数据库架构概览

RAGFlow 使用多层存储结构来管理知识库数据：

1. **关系型数据库**：存储元数据信息，使用 Peewee ORM 进行管理
2. **向量数据库**：存储文档内容和向量表示，使用 Infinity 作为向量存储引擎
3. **对象存储**：用于存储原始文件和图片，采用 MinIO

## 2. 知识库元数据存储

### Knowledgebase（知识库）表

```python
class Knowledgebase(DataBaseModel):
    id = CharField(max_length=32, primary_key=True)
    avatar = TextField(null=True, help_text="avatar base64 string")
    tenant_id = CharField(max_length=32, null=False, index=True)  # 租户ID
    name = CharField(max_length=128, null=False, help_text="KB name", index=True)  # 知识库名称
    language = CharField(max_length=32, null=True, default="Chinese", help_text="English|Chinese", index=True)  # 语言
    description = TextField(null=True, help_text="KB description")  # 知识库描述
    embd_id = CharField(max_length=128, null=False, help_text="default embedding model ID", index=True)  # 默认embedding模型ID
    permission = CharField(max_length=16, null=False, help_text="me|team", default="me", index=True)  # 权限
    created_by = CharField(max_length=32, null=False, index=True)  # 创建者
    doc_num = IntegerField(default=0, index=True)  # 文档数量
    token_num = IntegerField(default=0, index=True)  # token数量
    chunk_num = IntegerField(default=0, index=True)  # 知识片段数量
    similarity_threshold = FloatField(default=0.2, index=True)  # 相似度阈值
    vector_similarity_weight = FloatField(default=0.3, index=True)  # 向量相似度权重
    parser_id = CharField(max_length=32, null=False, help_text="default parser ID", default=ParserType.NAIVE.value, index=True)  # 默认解析器ID
    parser_config = JSONField(null=False, default={"pages": [[1, 1000000]]})  # 解析器配置
    pagerank = IntegerField(default=0, index=False)  # PageRank
    status = CharField(max_length=1, null=True, help_text="is it validate(0: wasted, 1: validate)", default="1", index=True)  # 状态
```

### Document（文档）表

```python
class Document(DataBaseModel):
    id = CharField(max_length=32, primary_key=True)
    thumbnail = TextField(null=True, help_text="thumbnail base64 string")  # 缩略图
    kb_id = CharField(max_length=256, null=False, index=True)  # 所属知识库ID
    parser_id = CharField(max_length=32, null=False, help_text="default parser ID", index=True)  # 解析器ID
    parser_config = JSONField(null=False, default={"pages": [[1, 1000000]]})  # 解析器配置
    source_type = CharField(max_length=128, null=False, default="local", help_text="where dose this document come from", index=True)  # 来源类型
    type = CharField(max_length=32, null=False, help_text="file extension", index=True)  # 文件类型
    created_by = CharField(max_length=32, null=False, help_text="who created it", index=True)  # 创建者
    name = CharField(max_length=255, null=True, help_text="file name", index=True)  # 文件名
    location = CharField(max_length=255, null=True, help_text="where dose it store", index=True)  # 存储位置
    size = IntegerField(default=0, index=True)  # 文件大小
    token_num = IntegerField(default=0, index=True)  # token数量
    chunk_num = IntegerField(default=0, index=True)  # chunk数量
    progress = FloatField(default=0, index=True)  # 处理进度
    progress_msg = TextField(null=True, help_text="process message", default="")  # 处理消息
    process_begin_at = DateTimeField(null=True, index=True)  # 处理开始时间
    process_duation = FloatField(default=0)  # 处理时长
    meta_fields = JSONField(null=True, default={})  # 元数据字段
    run = CharField(max_length=1, null=True, help_text="start to run processing or cancel.(1: run it; 2: cancel)", default="0", index=True)  # 运行状态
    status = CharField(max_length=1, null=True, help_text="is it validate(0: wasted, 1: validate)", default="1", index=True)  # 状态
```

### Task（任务）表
用于记录文档处理任务：

```python
class Task(DataBaseModel):
    id = CharField(max_length=32, primary_key=True)
    doc_id = CharField(max_length=32, null=False, index=True)  # 文档ID
    from_page = IntegerField(default=0)  # 起始页
    to_page = IntegerField(default=100000000)  # 结束页
    begin_at = DateTimeField(null=True, index=True)  # 开始时间
    process_duation = FloatField(default=0)  # 处理时长
    progress = FloatField(default=0, index=True)  # 进度
    progress_msg = TextField(null=True, help_text="process message", default="")  # 进度消息
    retry_count = IntegerField(default=0)  # 重试次数 
    digest = TextField(null=True, help_text="task digest", default="")  # 任务摘要
    chunk_ids = LongTextField(null=True, help_text="chunk ids", default="")  # 生成的chunk IDs
```

### File2Document（文件到文档映射）表

```python
class File2Document(DataBaseModel):
    id = CharField(max_length=32, primary_key=True)
    file_id = CharField(max_length=32, null=True, help_text="file id", index=True)  # 文件ID
    document_id = CharField(max_length=32, null=True, help_text="document id", index=True)  # 文档ID
```

## 3. 知识片段(Chunks)存储结构

知识片段不存储在关系型数据库中，而是直接存储在向量数据库里。从接口定义可以看出，chunk包含以下字段：

```typescript
export interface IChunk {
  available_int: number; // 是否启用，0: 未启用, 1: 启用
  chunk_id: string;  // 片段ID
  content_with_weight: string;  // 内容
  doc_id: string;  // 所属文档ID
  doc_name: string;  // 文档名称
  img_id: string;  // 图片ID（如果有）
  important_kwd?: string[];  // 重要关键词
  question_kwd?: string[];  // 问题关键词
  tag_kwd?: string[];  // 标签关键词
  positions: number[][];  // 位置信息
  tag_feas?: Record<string, number>;  // 标签特征
}
```


## 4. 向量存储设计

RAGFlow 使用 Infinity 作为向量数据库，用于存储文档的向量表示和进行向量检索。

### 索引模式设计 (mapping.json)

系统针对不同类型的数据使用特定的索引字段模式：

```json
{
  "kwd": { // 关键字段索引模式
    "match_pattern": "regex",
    "match": "^(.*_(kwd|id|ids|uid|uids)|uid)$",
    "mapping": {
      "type": "keyword",
      "similarity": "boolean",
      "store": true
    }
  },
  "dense_vector": { // 多种维度向量的索引模式
    "match": "*_512_vec",
    "mapping": {
      "type": "dense_vector",
      "index": true,
      "similarity": "cosine",
      "dims": 512
    }
  },
  // 还有768、1024、1536维度的向量索引
}
```

### 文档索引与检索

文档索引在 Infinity 中的表结构（根据 infinity_mapping.json）：

```json
{
  "id": {"type": "varchar", "default": ""},
  "doc_id": {"type": "varchar", "default": ""},
  "kb_id": {"type": "varchar", "default": ""},
  "create_time": {"type": "varchar", "default": ""},
  "create_timestamp_flt": {"type": "float", "default": 0.0},
  "img_id": {"type": "varchar", "default": ""},
  "docnm_kwd": {"type": "varchar", "default": "", "analyzer": "whitespace"},
  "title_tks": {"type": "varchar", "default": "", "analyzer": "whitespace"},
  "title_sm_tks": {"type": "varchar", "default": "", "analyzer": "whitespace"},
  "name_kwd": {"type": "varchar", "default": "", "analyzer": "whitespace"},
  "important_kwd": {"type": "varchar", "default": "", "analyzer": "whitespace-#"},
  "tag_kwd": {"type": "varchar", "default": "", "analyzer": "whitespace-#"},
  "important_tks": {"type": "varchar", "default": "", "analyzer": "whitespace"},
  "question_kwd": {"type": "varchar", "default": "", "analyzer": "whitespace-#"},
  "question_tks": {"type": "varchar", "default": "", "analyzer": "whitespace"},
  "content_with_weight": {"type": "varchar", "default": ""},
  "content_ltks": {"type": "varchar", "default": "", "analyzer": "whitespace"},
  "content_sm_ltks": {"type": "varchar", "default": "", "analyzer": "whitespace"},
  "q_512_vec": {"type": "vector,512,float"},
  "q_768_vec": {"type": "vector,768,float"},
  "q_1024_vec": {"type": "vector,1024,float"},
  "q_1536_vec": {"type": "vector,1536,float"}
}
```

### 向量嵌入与存储

文档内容会通过Embedding模型生成向量表示，然后存储在向量数据库中：

```python
def embedding(self, docs):
    texts = [d["content_with_weight"] for d in docs]
    embeddings, _ = self.embd_mdl.encode(texts)
    assert len(docs) == len(embeddings)
    vector_size = 0
    for i, d in enumerate(docs):
        v = embeddings[i]
        vector_size = len(v)
        d["q_%d_vec" % len(v)] = v
    return docs, vector_size
```

向量索引的创建与使用：

```python
def init_index(self, vector_size: int):
    if self.initialized_index:
        return
    if settings.docStoreConn.indexExist(self.index_name, self.kb_id):
        settings.docStoreConn.deleteIdx(self.index_name, self.kb_id)
    settings.docStoreConn.createIdx(self.index_name, self.kb_id, vector_size)
    self.initialized_index = True
```

## 5. 索引命名与组织方式

索引名称的生成规则：
```python
def index_name(uid): return f"ragflow_{uid}"
```

实际使用时：
- 索引名称会基于租户ID生成
- 知识库ID用于区分不同的知识库
- 索引表名：`{index_name}_{knowledgebaseId}`

## 6. 检索与搜索流程

检索过程中，系统会：
1. 将查询文本通过相同的embedding模型转换为向量
2. 在向量数据库中检索最相似的文档片段
3. 根据相似度排序并返回结果

```python
def _get_retrieval(self, qrels):
    run = defaultdict(dict)
    query_list = list(qrels.keys())
    for query in query_list:
        ranks = settings.retrievaler.retrieval(query, self.embd_mdl, self.tenant_id, [self.kb.id], 1, 30,
                                        0.0, self.vector_similarity_weight)
        # ... 处理结果
    return run
```

## 7. 知识图谱相关存储

系统支持实体和关系的存储：

```python
def set_entity(tenant_id, kb_id, embd_mdl, ent_name, meta):
    chunk = {
        "important_kwd": [ent_name],
        "title_tks": rag_tokenizer.tokenize(ent_name),
        "entity_kwd": ent_name,
        "knowledge_graph_kwd": "entity",
        "entity_type_kwd": meta["entity_type"],
        "content_with_weight": json.dumps(meta, ensure_ascii=False),
        "content_ltks": rag_tokenizer.tokenize(meta["description"]),
        "source_id": list(set(meta["source_id"])),
        "kb_id": kb_id,
        "available_int": 0
    }
    # ... 嵌入并存储实体
```

## 8. 完整的存储层次结构

综合以上分析，RAGFlow的知识库存储结构形成了以下层次：

1. **元数据层**：
   - Knowledgebase表：存储知识库基本信息
   - Document表：存储文档元数据
   - Task表：存储文档处理任务信息
   - File2Document表：文件与文档的映射关系

2. **向量存储层**：
   - 使用Infinity向量数据库
   - 按租户ID和知识库ID组织索引
   - 存储文档内容、分词和向量表示
   - 支持多维度向量（512/768/1024/1536）

3. **对象存储层**：
   - 使用MinIO存储原始文件
   - 存储文档图片和原始内容

这种多层次的存储结构设计，使RAGFlow能够高效地管理大量文档并提供快速的向量相似度检索能力。 