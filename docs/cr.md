# Shangou AI RAG 项目 Code Review 标准化 Prompt

---

**请你以专业 code reviewer 的视角，基于以下项目背景和规范，对 shangou_ai_rag 项目的代码进行全面、细致的 code review。请重点关注架构合理性、代码规范、可维护性、可扩展性、可读性、异常与边界处理、注释文档、测试覆盖、业务与技术解耦等方面。**

## 项目背景
- Shangou AI RAG 是一个现代化的检索增强生成（RAG）知识库管理与智能问答平台，支持多格式文档解析、智能分块、向量检索、LLM 问答、可视化工作流等。
- 后端：Python 3.10+，Flask，Peewee/SQLAlchemy，Infinity/Elasticsearch，MinIO，多 LLM 支持，任务队列，Docker/Helm。
- 前端：React 18 + TypeScript，UmiJS，Ant Design/Pro Components，Tailwind CSS，Zustand，React Query，Jest/Testing Library，ESLint/Prettier。

## 代码规范与目录结构
- 后端遵循 PEP8，英文命名，PascalCase 类名，snake_case 变量/函数名，重要类/函数需 docstring，业务与接口解耦，配置集中管理。
- 前端采用 TypeScript，camelCase 命名，类型/接口用 PascalCase，组件职责单一，状态管理用 Zustand，数据请求用 React Query，UI 统一，样式用 Tailwind。
- 目录结构清晰，api/ 负责后端接口，rag/ 为核心流程，deepdoc/ 负责文档解析，web/ 为前端源码，agent/ 及 agentic_reasoning/ 负责智能体与推理，sdk/ 便于集成，docker/helm/ 支持部署。

## 详细开发规则
- API 层只做路由、参数校验、调用 service、返回响应，service 层处理业务逻辑，model 层定义表结构，utils 层抽象通用能力，配置集中于 settings.py。
- 前端页面、组件、hooks、service、类型、样式、路由分层明确，API 调用统一封装，数据流动清晰，组件复用优先，国际化、常量、样式有统一约定。
- 所有 API 路径前缀统一，返回值结构标准，异常需有 code、msg，认证、权限、Session、CORS、Swagger、数据库连接等全局统一处理。
- 代码需有适当注释，复杂逻辑需详细说明，提交前需 lint、单测，重要变更需补充文档。

## code review 重点
1. **架构设计**：是否分层清晰、职责单一、解耦合理，目录结构是否符合规范。
2. **命名规范**：是否符合项目命名约定，变量、函数、类、文件、API 路径、数据库表字段等是否统一。
3. **代码风格**：是否符合 PEP8/ESLint/Prettier 规范，是否有格式化、类型安全、注释齐全。
4. **业务与技术解耦**：API 层与 service 层、前端 hooks/service/组件是否解耦，通用逻辑是否抽象。
5. **异常与边界处理**：是否有统一异常处理，边界条件、错误返回、日志告警是否完善。
6. **可维护性与可扩展性**：代码是否易于维护、扩展，是否有重复代码，是否便于测试。
7. **测试与文档**：是否有单元测试、集成测试，文档与注释是否完善。
8. **安全与配置**：密钥、配置是否集中管理，是否有硬编码，权限认证是否规范。
9. **性能与资源管理**：是否有明显性能隐患，资源释放、连接池、异步处理是否合理。
10. **前端交互与体验**：组件拆分、复用、样式、国际化、数据流动、API 封装是否规范。

---

**请结合上述背景和规范，对指定代码进行逐条点评，指出优点与不足，并给出具体改进建议。**

---

**附加要求：**

在 code review 结论部分，请简明扼要总结本次代码修改实现了哪些功能、带来了哪些新能力或优化，便于团队成员和相关方直观了解本次变更的核心价值和影响。 