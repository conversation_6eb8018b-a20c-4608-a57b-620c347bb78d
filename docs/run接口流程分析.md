# `/run`接口触发的chunk和embedding处理流程分析

## 一、整体流程概述

`/run`接口是触发文档解析过程的入口，它会启动一系列任务来完成文档的解析、分块(chunking)和向量化(embedding)处理。整个流程主要包括：

1. 接收文档处理请求
2. 创建任务队列
3. 任务执行
   - 文档分块(chunking)
   - 向量化(embedding)
4. 索引存储
5. 进度更新与状态管理

## 二、接口调用入口

位于`api/apps/document_app.py`中的`/run`接口：

```python
@manager.route('/run', methods=['POST'])
@login_required
@validate_request("doc_ids", "run")
def run():
    req = request.json
    logging.info(f"[文档解析] 收到解析请求: doc_ids={req['doc_ids']}, run={req['run']}")
    
    # 权限检查
    # 状态更新
    # 任务队列创建
```

## 三、任务队列创建

当`run`状态为`RUNNING`时，系统会创建解析任务：

```python
if str(req["run"]) == TaskStatus.RUNNING.value:
    # 获取文档信息
    e, doc = DocumentService.get_by_id(id)
    doc = doc.to_dict()
    doc["tenant_id"] = tenant_id
    
    # 获取文件存储地址
    bucket, name = File2DocumentService.get_storage_address(doc_id=doc["id"])
    
    # 创建任务队列
    queue_tasks(doc, bucket, name)
```

`queue_tasks`函数在`api/db/services/task_service.py`中实现，负责：
1. 根据文档类型(PDF、Excel等)分析页数/行数
2. 创建适当的任务分片
3. 计算任务摘要，检查是否可以重用之前的结果
4. 将任务添加到Redis队列以供后续处理

## 四、文档分块(Chunking)过程

任务执行器(`rag/svr/task_executor.py`)中的`build_chunks`函数负责文档分块处理：

```python
async def build_chunks(task, progress_callback):
    # ...
    cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], 
                          binary=binary, 
                          from_page=task["from_page"],
                          to_page=task["to_page"], 
                          lang=task["language"], 
                          callback=progress_callback,
                          kb_id=task["kb_id"], 
                          parser_config=task["parser_config"], 
                          tenant_id=task["tenant_id"]))
    # ...
```

根据文档类型，系统会使用不同的chunker实现：

1. **普通文本/PDF文档**: 使用`naive.py`中的分块方法，按照指定的token数量和分隔符进行分块
2. **结构化数据(Excel)**: 使用专门的表格解析器
3. **邮件文档**: 使用`email.py`中的处理逻辑
4. **专门格式(QA对等)**: 使用`tag.py`中的处理逻辑
5. **一次性加载**: 使用`one.py`将整个文档作为一个块处理

分块后，会为每个chunk创建唯一ID和相关元数据。

## 五、向量化(Embedding)过程

`embedding`函数在`rag/svr/task_executor.py`中实现：

```python
async def embedding(docs, mdl, parser_config=None, callback=None):
    # 批处理文档
    batch_size = 16
    tts, cnts = [], []
    
    # 准备标题和内容
    for d in docs:
        tts.append(d.get("docnm_kwd", "Title"))
        c = "\n".join(d.get("question_kwd", []))
        if not c:
            c = d["content_with_weight"]
        # ...
        cnts.append(c)
    
    # 处理标题向量
    vts, c = await trio.to_thread.run_sync(lambda: mdl.encode(tts[0: 1]))
    tts = np.concatenate([vts for _ in range(len(tts))], axis=0)
    tk_count += c
    
    # 处理内容向量(分批)
    for i in range(0, len(cnts), batch_size):
        vts, c = await trio.to_thread.run_sync(lambda: mdl.encode(cnts[i: i + batch_size]))
        # 合并结果
        # ...
    
    # 标题和内容向量加权合并
    title_w = float(parser_config.get("filename_embd_weight", 0.1))
    vects = (title_w * tts + (1 - title_w) * cnts) if len(tts) == len(cnts) else cnts
    
    # 保存到文档对象中
    for i, d in enumerate(docs):
        v = vects[i].tolist()
        vector_size = len(v)
        d["q_%d_vec" % len(v)] = v
        
    return tk_count, vector_size
```

主要流程：
1. 提取文档标题和内容文本
2. 对标题进行向量化
3. 对内容进行批量向量化
4. 融合标题向量和内容向量(加权融合)
5. 将结果存储在文档对象中

## 六、任务流程整合

在`do_handle_task`函数中，整个处理流程被整合在一起：

```python
async def do_handle_task(task):
    # 初始化
    # ...
    
    # 准备embedding模型
    embedding_model = LLMBundle(task_tenant_id, LLMType.EMBEDDING, llm_name=task_embedding_id, lang=task_language)
    
    # 初始化知识库
    init_kb(task, vector_size)
    
    # 根据任务类型执行不同处理
    if task.get("task_type") == "raptor":
        # 处理RAPTOR任务
    elif task.get("task_type") == "graphrag":
        # 处理GraphRAG任务
    elif task.get("task_type") == "graph_resolution":
        # 处理图解析任务
    elif task.get("task_type") == "graph_community":
        # 处理图社区任务
    else:
        # 标准分块方法
        chunks = await build_chunks(task, progress_callback)
        
        # 向量化处理
        token_count, vector_size = await embedding(chunks, embedding_model, task_parser_config, progress_callback)
        
        # 存储到文档库
        # ...
```

## 七、检索过程与向量使用

完成分块和向量化后，这些向量在检索过程中被使用。检索主要通过`settings.retrievaler.retrieval`函数实现：

```python
kbinfos = settings.retrievaler.retrieval(query, embd_mdl, kbs[0].tenant_id, self._param.kb_ids,
                                1, self._param.top_n,
                                self._param.similarity_threshold, 1 - self._param.keywords_similarity_weight,
                                aggs=False, rerank_mdl=rerank_mdl,
                                rank_feature=label_question(query, kbs))
```

检索流程：
1. 将查询文本转换为向量
2. 在向量数据库中进行相似度搜索
3. 根据向量相似度和关键词相似度进行加权排序
4. 根据需要进行重排序(rerank)
5. 返回最相关的chunks

## 八、状态更新与进度管理

整个处理过程中，系统会不断更新任务进度和文档状态：

- 通过`progress_callback`函数更新任务进度
- `DocumentService.update_progress`方法定期检查未完成文档的进度
- 当所有任务完成时，文档状态更新为`DONE`

## 九、总结

`/run`接口触发的chunk和embedding流程是一个多阶段的异步处理过程：

1. **接口调用**: 用户触发`/run`接口
2. **任务队列创建**: 根据文档类型创建适当的任务
3. **异步处理**: 任务被放入Redis队列，由任务执行器异步处理
4. **分块处理**: 根据文档类型和配置进行文档分块
5. **向量化**: 使用embedding模型将文本块转换为向量
6. **索引存储**: 将分块和向量存储到后端存储系统
7. **状态管理**: 整个过程中持续更新状态和进度

这个完整的pipeline确保了从原始文档到可检索的向量化文本块的高效转换，为后续的RAG检索提供基础。 