# 美团Redis连接使用指南

本文档介绍如何在项目中使用美团Redis的两种连接方式：valkey直连和Squirrel SDK。

## 配置文件设置

在 `conf/mt_redis.yaml` 中配置Redis连接参数：

```yaml
# 美团Redis配置
# 集群名称
cluster_name: redis-sg-fission-activity_qa

# 环境：test, prod
env: test

# Proxy地址（valkey直连方式使用）
proxy_address: *************:80

# 是否使用Squirrel SDK连接Redis（true使用SDK，false使用valkey直连）
use_squirrel_sdk: false

# Squirrel SDK配置文件路径（SDK方式使用）
# proxy_config_path: /path/to/squirrel-proxy.conf

# 应用标识（SDK方式使用）
appkey: com.sankuai.sg.ai

# Category配置
category_config:
  category: sg_ai
  duration: "-1"  # -1表示不过期
  indexTemplate: "c{0}"
  version: 0
```

## 切换连接方式

有两种方式可以切换Redis的连接方式：

1. 通过配置文件：修改 `conf/mt_redis.yaml` 中的 `use_squirrel_sdk` 为 `true` 或 `false`
2. 通过环境变量：设置 `MT_REDIS_USE_SQUIRREL_SDK=true` 或 `MT_REDIS_USE_SQUIRREL_SDK=false`

## 使用Squirrel SDK连接方式

1. 安装SDK依赖：

```bash
pip install mt-squirrel-proxy --index-url http://pypi.sankuai.com/simple --trusted-host pypi.sankuai.com
```

2. 确保在 `rag/utils/squirrel-proxy.conf` 中配置了正确的Proxy信息：

```
[proxy_config]
cluster_name:=redis-sg-fission-activity_qa
env:=test
proxy_address:=*************:80
category_config:={"category":"sg_ai","duration":"-1","indexTemplate":"c{0}","version":0}

[proxy_config]
cluster_name:=redis-sg-fission-activity_qa
env:=prod
proxy_address:=*************:80
category_config:={"category":"sg_ai","duration":"-1","indexTemplate":"c{0}","version":0}
```

3. 设置 `use_squirrel_sdk` 为 `true`

## 在代码中使用

无论使用哪种连接方式，代码中的使用方式都是一致的：

```python
from rag.utils import REDIS_CONN

# 存储值
REDIS_CONN.set("test_key", "test_value", 3600)  # 3600秒过期

# 获取值
value = REDIS_CONN.get("test_key")

# 存储对象
user_data = {"id": "12345", "name": "测试用户", "score": 100}
REDIS_CONN.set_obj(f"user:12345", user_data, 3600)

# 获取对象
user_value_str = REDIS_CONN.get(f"user:12345")
user_value = json.loads(user_value_str)

# 使用集合
REDIS_CONN.sadd("test_set", "member1")
members = REDIS_CONN.smembers("test_set")
REDIS_CONN.srem("test_set", "member1")

# 使用有序集合
REDIS_CONN.zadd("test_zset", "member1", 1.0)
count = REDIS_CONN.zcount("test_zset", 0, 2)
items = REDIS_CONN.zrangebyscore("test_zset", 0, 2)

# 使用分布式锁
from rag.utils.mt_redis_conn import MTRedisDistributedLock
# 或 from rag.utils.mt_squirrel_conn import MTSquirrelDistributedLock

# 方式1：显式获取和释放锁
lock = MTRedisDistributedLock("test_lock", timeout=5)
if lock.acquire_lock():
    try:
        # 执行需要互斥的操作
        pass
    finally:
        lock.release_lock()

# 方式2：使用with语句
with MTRedisDistributedLock("test_lock", timeout=5):
    # 执行需要互斥的操作
    pass

# 清除锁
MTRedisDistributedLock.clean_lock("test_lock")

# 使用队列功能
# 发送消息到队列
message = {"id": "msg_id", "content": "测试队列消息", "timestamp": time.time()}
REDIS_CONN.queue_product("test_queue", message)

# 消费消息
msg = REDIS_CONN.queue_consumer("test_queue", "test_group", "consumer1")
if msg:
    received_msg = msg.get_message()
    # 处理消息...
    msg.ack()  # 确认消息已处理
```

## 注意事项

1. Squirrel SDK 不支持 "MSET", "MSETNX" 命令。
2. Squirrel SDK 仅支持 master-only 路由策略。
3. 使用Squirrel SDK时，过期时间的处理有所不同，详见学城文档。
4. 如果项目中混合使用两种方式，注意导入正确的分布式锁实现类。 