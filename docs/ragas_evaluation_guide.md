# RAG系统Ragas评估指南

## 概述

本系统集成了Ragas评估框架，为RAG（检索增强生成）系统提供全面的评估能力。通过多维度的评估指标，帮助您了解和优化RAG系统的性能。

## 功能特性

### 1. 评估指标

支持以下Ragas评估指标：

- **faithfulness（忠实度）**: 评估生成答案与检索上下文的一致性
- **answer_relevancy（答案相关性）**: 评估生成答案与问题的相关程度
- **context_precision（上下文精确度）**: 评估检索到的上下文的精确性
- **context_recall（上下文召回率）**: 评估检索系统的召回能力
- **context_relevancy（上下文相关性）**: 评估检索上下文与问题的相关性
- **answer_correctness（答案正确性）**: 评估答案的正确性（需要标准答案）
- **answer_similarity（答案相似性）**: 评估答案与标准答案的相似度

### 2. 数据集管理

- 创建和管理评估数据集
- 支持CSV和JSON格式的数据导入/导出
- 灵活的样本管理（问题、标准答案、上下文）

### 3. 评估任务

- 异步任务执行，支持大规模评估
- 实时进度监控
- 并发控制，提高评估效率
- 任务状态管理（待执行、运行中、已完成、失败、已取消）

### 4. 结果分析

- 详细的评估报告
- 多维度指标可视化
- 单个样本结果查看
- 统计信息汇总

## 使用流程

### 步骤1：准备评估数据

1. **创建数据集**
   ```bash
   POST /api/v1/evaluation/datasets
   {
     "name": "测试数据集",
     "kb_id": "your_kb_id",
     "description": "用于评估的测试数据集"
   }
   ```

2. **添加评估样本**
   ```bash
   POST /api/v1/evaluation/datasets/{dataset_id}/samples
   {
     "question": "什么是人工智能？",
     "ground_truth": "人工智能是计算机科学的一个分支...",
     "contexts": ["相关文档片段1", "相关文档片段2"]
   }
   ```

3. **批量导入数据**
   - CSV格式：包含question、ground_truth、contexts列
   - JSON格式：数组形式，每个对象包含question、ground_truth、contexts字段

### 步骤2：创建评估任务

```bash
POST /api/v1/evaluation/tasks
{
  "name": "RAG系统性能评估",
  "dataset_id": "your_dataset_id",
  "kb_id": "your_kb_id",
  "metrics": ["faithfulness", "answer_relevancy", "context_precision"],
  "retrieval_config": {
    "similarity_threshold": 0.2,
    "vector_similarity_weight": 0.3,
    "top_n": 8,
    "top_k": 1024,
    "rerank_id": "your_rerank_model_id"
  },
  "llm_config": {
    "llm_id": "your_llm_id",
    "embedding_id": "your_embedding_id"
  }
}
```

### 步骤3：执行评估

```bash
POST /api/v1/evaluation/tasks/{task_id}/start
{
  "max_workers": 3
}
```

### 步骤4：查看结果

1. **获取任务状态**
   ```bash
   GET /api/v1/evaluation/tasks/{task_id}
   ```

2. **获取评估报告**
   ```bash
   GET /api/v1/evaluation/tasks/{task_id}/report
   ```

3. **获取详细结果**
   ```bash
   GET /api/v1/evaluation/tasks/{task_id}/results
   ```

## 前端界面使用

### 1. 数据集管理

- 访问评估页面的"数据集管理"标签
- 点击"创建数据集"按钮，填写数据集信息
- 在数据集列表中管理样本：
  - 手动添加样本
  - 批量导入CSV/JSON文件
  - 导出数据集

### 2. 评估任务

- 切换到"评估任务"标签
- 点击"创建任务"，配置评估参数：
  - 选择数据集和知识库
  - 选择评估指标
  - 配置检索和LLM参数
- 启动任务并监控进度

### 3. 结果分析

- 在"结果分析"标签查看：
  - 评估指标概览
  - 详细结果表格
  - 可视化图表
  - 导出评估报告

## 配置说明

### 检索配置参数

- `similarity_threshold`: 相似度阈值（0-1）
- `vector_similarity_weight`: 向量相似度权重（0-1）
- `top_n`: 返回的文档数量
- `top_k`: 检索候选数量
- `rerank_id`: 重排序模型ID（可选）

### LLM配置参数

- `llm_id`: 大语言模型ID
- `embedding_id`: 嵌入模型ID（可选，默认使用知识库配置）

## 最佳实践

### 1. 数据集准备

- **质量优于数量**: 确保评估样本的质量，包含代表性问题
- **标准答案**: 为需要answer_correctness和answer_similarity指标的评估提供高质量标准答案
- **多样性**: 包含不同类型和难度的问题

### 2. 指标选择

- **基础评估**: faithfulness, answer_relevancy, context_precision
- **检索优化**: context_recall, context_relevancy
- **答案质量**: answer_correctness, answer_similarity（需要标准答案）

### 3. 参数调优

- **相似度阈值**: 从0.2开始，根据结果调整
- **文档数量**: top_n建议5-10，top_k建议512-2048
- **并发数**: max_workers根据服务器性能调整，建议2-5

### 4. 结果解读

- **faithfulness > 0.8**: 答案与上下文一致性良好
- **answer_relevancy > 0.7**: 答案相关性较好
- **context_precision > 0.6**: 检索精确度可接受
- **综合评估**: 关注多个指标的平衡，避免单一指标优化

## 故障排除

### 常见问题

1. **Ragas库未安装**
   ```bash
   pip install ragas==0.1.9 datasets==2.14.6
   ```

2. **评估任务失败**
   - 检查LLM和embedding模型配置
   - 确认知识库数据完整性
   - 查看任务错误日志

3. **性能问题**
   - 减少并发数量
   - 优化数据集大小
   - 检查服务器资源

4. **指标异常**
   - 验证数据集质量
   - 检查模型配置
   - 确认评估参数设置

### 日志查看

```bash
# 查看评估相关日志
tail -f logs/rag_evaluation.log

# 查看任务执行日志
grep "evaluation" logs/application.log
```

## API参考

详细的API文档请参考：
- 数据集管理API: `/api/v1/evaluation/datasets`
- 任务管理API: `/api/v1/evaluation/tasks`
- 结果查询API: `/api/v1/evaluation/tasks/{task_id}/results`

## 扩展开发

### 自定义评估指标

可以通过扩展`RagasEvaluator`类添加自定义评估指标：

```python
class CustomRagasEvaluator(RagasEvaluator):
    def __init__(self, tenant_id: str, kb_id: str):
        super().__init__(tenant_id, kb_id)
        # 添加自定义指标
        self.available_metrics['custom_metric'] = custom_metric_function
```

### 集成其他评估框架

系统架构支持集成其他评估框架，只需实现相应的评估器接口。

## 更新日志

- v1.0.0: 初始版本，支持基础Ragas评估功能
- v1.1.0: 添加批量导入导出功能
- v1.2.0: 优化并发执行和错误处理 