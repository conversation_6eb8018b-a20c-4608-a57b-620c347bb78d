# RAG评估系统使用指南

## 重要开发规范

### 前端API调用规范 ⚠️
**所有前端API请求必须使用正确的前缀！**

```typescript
// ✅ 正确：使用api_host前缀
import { api_host } from '@/utils/api';
return request.post(`${api_host}/evaluation/datasets`, data);

// ❌ 错误：缺少前缀
return request.post('/api/v1/evaluation/datasets', data);
```

**规范说明**：
- 所有API请求必须从`@/utils/api`导入`api_host`
- 所有请求路径格式：`${api_host}/模块名/资源名`
- 参考`web/src/utils/api.ts`文件中的现有模式
- 后端路径前缀为`/api/v1/`，但前端通过api_host变量统一管理

---

## 系统概述

RAG评估系统是基于ragas框架构建的全面评估解决方案，用于评估检索增强生成(RAG)系统的性能。系统支持7种评估指标，提供从数据准备到结果分析的完整评估流程。

## 功能特性

### 1. 评估指标
- **忠实度 (Faithfulness)**: 评估生成答案是否忠实于检索到的上下文
- **答案相关性 (Answer Relevancy)**: 评估答案与问题的相关程度
- **上下文精确度 (Context Precision)**: 评估检索上下文的精确度
- **上下文召回率 (Context Recall)**: 评估检索上下文的召回率
- **上下文相关性 (Context Relevancy)**: 评估检索上下文与问题的相关性
- **答案正确性 (Answer Correctness)**: 评估答案的正确性
- **答案相似性 (Answer Similarity)**: 评估答案与标准答案的相似度

### 2. 核心功能
- **数据集管理**: 创建、编辑、删除评估数据集
- **样本管理**: 批量导入/导出评估样本
- **任务管理**: 创建、启动、停止、监控评估任务
- **结果分析**: 可视化展示评估结果和详细分析报告

## 使用流程

### 第一步：数据集管理

1. **创建数据集**
   - 点击"数据集管理"标签页
   - 点击"创建数据集"按钮
   - 填写数据集信息：
     - 名称：数据集的显示名称
     - 关联知识库：选择要评估的知识库
     - 类型：选择"Q&A对"或"仅问题"
     - 描述：数据集的详细描述

2. **数据格式要求**
   
   **Q&A对格式** (CSV):
   ```csv
   question,ground_truth,contexts
   "什么是人工智能？","人工智能是计算机科学的一个分支...","相关文档内容1|相关文档内容2"
   ```
   
   **仅问题格式** (CSV):
   ```csv
   question
   "什么是人工智能？"
   "机器学习的应用有哪些？"
   ```

3. **导入样本数据**
   - 在数据集列表中找到目标数据集
   - 点击"导入数据"按钮
   - 上传符合格式要求的CSV文件

### 第二步：创建评估任务

1. **任务配置**
   - 切换到"评估任务"标签页
   - 点击"创建评估任务"按钮
   - 配置任务参数：
     - 任务名称：任务的显示名称
     - 评估数据集：选择已创建的数据集
     - 目标知识库：选择要评估的知识库
     - 评估指标：选择一个或多个评估指标
     - 任务描述：任务的详细说明

2. **启动任务**
   - 创建完成后，任务状态为"等待中"
   - 点击"开始"按钮启动任务
   - 任务状态变为"运行中"，显示实时进度

3. **监控任务**
   - 可以实时查看任务进度
   - 如需停止任务，点击"停止"按钮
   - 任务完成后状态变为"已完成"

### 第三步：结果分析

1. **查看结果**
   - 切换到"结果分析"标签页
   - 选择已完成的评估任务
   - 系统会自动加载评估结果

2. **结果展示**
   - **指标概览**: 显示各项指标的具体数值和百分比
   - **可视化分析**: 提供柱状图和雷达图展示
   - **详细报告**: 提供深入的分析报告（开发中）

## API接口

### 数据集管理

```bash
# 创建数据集
POST /api/v1/evaluation/datasets
{
  "name": "测试数据集",
  "kb_id": "知识库ID",
  "dataset_type": "qa_pairs",
  "description": "数据集描述"
}

# 获取数据集列表
GET /api/v1/evaluation/datasets

# 导入样本
POST /api/v1/evaluation/datasets/{dataset_id}/import
Content-Type: multipart/form-data
```

### 任务管理

```bash
# 创建评估任务
POST /api/v1/evaluation/tasks
{
  "name": "评估任务",
  "dataset_id": "数据集ID",
  "kb_id": "知识库ID",
  "metrics": ["faithfulness", "answer_relevancy"]
}

# 启动任务
POST /api/v1/evaluation/tasks/{task_id}/start

# 停止任务
POST /api/v1/evaluation/tasks/{task_id}/stop

# 获取任务结果
GET /api/v1/evaluation/tasks/{task_id}/results
```

## 最佳实践

### 1. 数据准备
- 确保问题清晰具体，避免歧义
- 标准答案应该准确完整
- 上下文信息要相关且充分
- 建议样本数量不少于50个

### 2. 指标选择
- **基础评估**: faithfulness + answer_relevancy
- **检索质量**: context_precision + context_recall
- **全面评估**: 选择所有7个指标
- **快速验证**: 仅选择faithfulness

### 3. 结果解读
- **优秀**: 指标值 > 0.8
- **良好**: 指标值 0.6-0.8
- **需改进**: 指标值 < 0.6

### 4. 性能优化
- 大数据集建议分批评估
- 并发任务数量建议不超过3个
- 定期清理历史任务和结果

## 故障排除

### 常见问题

1. **知识库列表为空**
   - 检查是否已创建知识库
   - 确认用户权限是否正确

2. **任务创建失败**
   - 检查数据集是否存在样本
   - 确认知识库状态是否正常

3. **任务一直运行**
   - 检查后台服务状态
   - 查看系统日志了解详情

4. **结果显示为空**
   - 确认任务是否真正完成
   - 检查数据库中的结果记录

### 日志查看

```bash
# 查看评估服务日志
tail -f logs/evaluation.log

# 查看数据库连接日志
tail -f logs/database.log
```

## 系统配置

### 环境要求
- Python 3.8+
- MySQL 5.7+
- Redis (用于任务队列)

### 依赖包
```txt
ragas>=0.1.0
datasets>=2.0.0
pandas>=1.5.0
numpy>=1.20.0
```

### 配置文件
```python
# 评估系统配置
EVALUATION_CONFIG = {
    "max_concurrent_tasks": 3,
    "task_timeout": 3600,  # 1小时
    "batch_size": 10,
    "retry_times": 3
}
```

## 更新日志

### v1.0.0 (2024-01-20)
- 初始版本发布
- 支持7种ragas评估指标
- 完整的数据集和任务管理功能
- 基础的结果可视化

### 后续规划
- [ ] 支持更多评估指标
- [ ] 批量评估任务
- [ ] 评估报告导出
- [ ] 评估历史趋势分析
- [ ] API性能优化 