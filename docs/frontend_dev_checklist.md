# 前端开发检查清单

## API服务开发必检项 🔥

### 1. API路径前缀 ⚠️ 
- [ ] 从`@/utils/api`导入了`api_host`
- [ ] 所有API请求使用`${api_host}/路径`格式
- [ ] 没有硬编码`/api/v1/`前缀
- [ ] 参考了`api.ts`中的现有模式

### 2. TypeScript类型
- [ ] 定义了完整的接口类型
- [ ] 使用了正确的泛型约束
- [ ] 处理了可选属性

### 3. 错误处理
- [ ] 添加了try-catch包围
- [ ] 检查了response.data.code
- [ ] 提供了用户友好的错误提示

### 4. Hook集成
- [ ] 使用了项目现有的Hooks（如`useFetchKnowledgeList`）
- [ ] 避免了重复的API调用逻辑

## 示例代码模板

```typescript
// ✅ 标准API服务模板
import request from '@/utils/request';
import { api_host } from '@/utils/api';

class ServiceName {
  async methodName(params: ParamsType) {
    return request.post(`${api_host}/module/resource`, params);
  }
}

export default new ServiceName();
```

## 常见错误

### ❌ 忘记API前缀
```typescript
// 错误
return request.post('/api/v1/evaluation/datasets', data);
```

### ✅ 正确做法
```typescript
// 正确
import { api_host } from '@/utils/api';
return request.post(`${api_host}/evaluation/datasets`, data);
``` 