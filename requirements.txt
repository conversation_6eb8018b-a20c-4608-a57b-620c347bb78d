aiohappyeyeballs==2.6.1 ; python_version >= "3.10" and python_version < "3.13"
aiohttp==3.11.14 ; python_version >= "3.10" and python_version < "3.13"
aiolimiter==1.2.1 ; python_version >= "3.10" and python_version < "3.13"
aiosignal==1.3.2 ; python_version >= "3.10" and python_version < "3.13"
aiosqlite==0.20.0 ; python_version >= "3.10" and python_version < "3.13"
akracer==0.0.13 ; python_version >= "3.10" and python_version < "3.13" and platform_system == "Linux"
akshare==1.16.72 ; python_version >= "3.10" and python_version < "3.13"
alabaster==1.0.0 ; python_version >= "3.10" and python_version < "3.13"
annotated-types==0.7.0 ; python_version >= "3.10" and python_version < "3.13"
anthropic==0.34.1 ; python_version >= "3.10" and python_version < "3.13"
anyio==4.9.0 ; python_version >= "3.10" and python_version < "3.13"
anytree==2.12.1 ; python_version >= "3.10" and python_version < "3.13"
appdirs==1.4.4 ; python_version >= "3.10" and python_version < "3.13"
argon2-cffi-bindings==21.2.0 ; python_version >= "3.10" and python_version < "3.13"
argon2-cffi==23.1.0 ; python_version >= "3.10" and python_version < "3.13"
arrow==1.3.0 ; python_version >= "3.10" and python_version < "3.13"
arxiv==2.1.3 ; python_version >= "3.10" and python_version < "3.13"
aspose-slides==24.12.0 ; python_version >= "3.10" and python_version < "3.13" and (sys_platform == "darwin" or platform_machine == "x86_64") and (platform_machine == "x86_64" or platform_machine == "arm64")
async-timeout==5.0.1 ; python_version == "3.10" or python_version == "3.11"
attrs==25.3.0 ; python_version >= "3.10" and python_version < "3.13"
autograd==1.7.0 ; python_version >= "3.10" and python_version < "3.13"
azure-core==1.32.0 ; python_version >= "3.10" and python_version < "3.13"
azure-identity==1.17.1 ; python_version >= "3.10" and python_version < "3.13"
azure-storage-blob==12.22.0 ; python_version >= "3.10" and python_version < "3.13"
azure-storage-file-datalake==12.16.0 ; python_version >= "3.10" and python_version < "3.13"
babel==2.17.0 ; python_version >= "3.10" and python_version < "3.13"
bce-python-sdk==0.9.29 ; python_version >= "3.10" and python_version < "3.13"
beartype==0.18.5 ; python_version >= "3.10" and python_version < "3.13"
beautifulsoup4==4.12.3 ; python_version >= "3.10" and python_version < "3.13"
bibtexparser==1.4.3 ; python_version >= "3.10" and python_version < "3.13"
bio==1.7.1 ; python_version >= "3.10" and python_version < "3.13"
biopython==1.85 ; python_version >= "3.10" and python_version < "3.13"
biothings-client==0.4.1 ; python_version >= "3.10" and python_version < "3.13"
blinker==1.7.0 ; python_version >= "3.10" and python_version < "3.13"
boto3==1.34.140 ; python_version >= "3.10" and python_version < "3.13"
botocore==1.34.140 ; python_version >= "3.10" and python_version < "3.13"
brotli==1.1.0 ; python_version >= "3.10" and python_version < "3.13"
cachelib==0.13.0 ; python_version >= "3.10" and python_version < "3.13"
cachetools==5.3.3 ; python_version >= "3.10" and python_version < "3.13"
cbor2==5.6.5 ; python_version >= "3.10" and python_version < "3.13"
cbor==1.0.0 ; python_version >= "3.10" and python_version < "3.13"
certifi==2025.1.31 ; python_version >= "3.10" and python_version < "3.13"
cffi==1.17.1 ; python_version >= "3.10" and python_version < "3.13"
chardet==5.2.0 ; python_version >= "3.10" and python_version < "3.13"
charset-normalizer==3.4.1 ; python_version >= "3.10" and python_version < "3.13"
click==8.1.8 ; python_version >= "3.10" and python_version < "3.13"
cn2an==0.5.22 ; python_version >= "3.10" and python_version < "3.13"
cohere==5.6.2 ; python_version >= "3.10" and python_version < "3.13"
colorama==0.4.6 ; python_version >= "3.10" and python_version < "3.13" and (platform_system == "Windows" or sys_platform == "win32")
coloredlogs==15.0.1 ; python_version >= "3.10" and python_version < "3.13" and (sys_platform == "darwin" or platform_machine != "x86_64")
contourpy==1.3.1 ; python_version >= "3.10" and python_version < "3.13"
cramjam==2.9.1 ; python_version >= "3.10" and python_version < "3.13"
crawl4ai==0.3.8 ; python_version >= "3.10" and python_version < "3.13"
cryptography==44.0.2 ; python_version >= "3.10" and python_version < "3.13"
cssselect==1.3.0 ; python_version >= "3.10" and python_version < "3.13"
cycler==0.12.1 ; python_version >= "3.10" and python_version < "3.13"
dashscope==1.20.11 ; python_version >= "3.10" and python_version < "3.13"
datrie==0.8.2 ; python_version >= "3.10" and python_version < "3.13"
decorator==5.2.1 ; python_version >= "3.10" and python_version < "3.13"
deepl==1.18.0 ; python_version >= "3.10" and python_version < "3.13"
demjson3==3.0.6 ; python_version >= "3.10" and python_version < "3.13"
deprecated==1.2.18 ; python_version >= "3.10" and python_version < "3.13"
dill>=0.3.0,<0.3.8 ; python_version >= "3.10" and python_version < "3.13"
discord-py==2.3.2 ; python_version >= "3.10" and python_version < "3.13"
diskcache==5.6.3 ; python_version >= "3.10" and python_version < "3.13"
distro==1.9.0 ; python_version >= "3.10" and python_version < "3.13"
docstring-parser==0.16 ; python_version >= "3.10" and python_version < "3.13"
docutils==0.21.2 ; python_version >= "3.10" and python_version < "3.13"
duckduckgo-search==7.5.2 ; python_version >= "3.10" and python_version < "3.13"
editdistance==0.8.1 ; python_version >= "3.10" and python_version < "3.13"
elastic-transport==8.12.0 ; python_version >= "3.10" and python_version < "3.13"
elasticsearch-dsl==8.12.0 ; python_version >= "3.10" and python_version < "3.13"
elasticsearch==8.12.1 ; python_version >= "3.10" and python_version < "3.13"
et-xmlfile==2.0.0 ; python_version >= "3.10" and python_version < "3.13"
exceptiongroup==1.2.2 ; python_version == "3.10"
fake-useragent==1.5.1 ; python_version >= "3.10" and python_version < "3.13"
fastavro==1.10.0 ; python_version >= "3.10" and python_version < "3.13"
fastparquet==2024.11.0 ; python_version >= "3.10" and python_version < "3.13"
feedparser==6.0.11 ; python_version >= "3.10" and python_version < "3.13"
filelock==3.15.4 ; python_version >= "3.10" and python_version < "3.13"
flasgger==0.9.7.1 ; python_version >= "3.10" and python_version < "3.13"
flask-cors==5.0.0 ; python_version >= "3.10" and python_version < "3.13"
flask-login==0.6.3 ; python_version >= "3.10" and python_version < "3.13"
flask-session==0.8.0 ; python_version >= "3.10" and python_version < "3.13"
flask==3.0.3 ; python_version >= "3.10" and python_version < "3.13"
flatbuffers==25.2.10 ; python_version >= "3.10" and python_version < "3.13" and (sys_platform == "darwin" or platform_machine != "x86_64")
fonttools==4.56.0 ; python_version >= "3.10" and python_version < "3.13"
free-proxy==1.1.3 ; python_version >= "3.10" and python_version < "3.13"
frozenlist==1.5.0 ; python_version >= "3.10" and python_version < "3.13"
fsspec==2024.12.0 ; python_version >= "3.10" and python_version < "3.13"
future==1.0.0 ; python_version >= "3.10" and python_version < "3.13"
gensim==4.3.3 ; python_version >= "3.10" and python_version < "3.13"
google-ai-generativelanguage==0.6.15 ; python_version >= "3.10" and python_version < "3.13"
google-api-core==2.24.2 ; python_version >= "3.10" and python_version < "3.13"
google-api-python-client==2.165.0 ; python_version >= "3.10" and python_version < "3.13"
google-auth-httplib2==0.2.0 ; python_version >= "3.10" and python_version < "3.13"
google-auth==2.38.0 ; python_version >= "3.10" and python_version < "3.13"
google-cloud-aiplatform==1.64.0 ; python_version >= "3.10" and python_version < "3.13"
google-cloud-bigquery==3.30.0 ; python_version >= "3.10" and python_version < "3.13"
google-cloud-core==2.4.3 ; python_version >= "3.10" and python_version < "3.13"
google-cloud-resource-manager==1.14.2 ; python_version >= "3.10" and python_version < "3.13"
google-cloud-storage==2.19.0 ; python_version >= "3.10" and python_version < "3.13"
google-crc32c==1.7.0 ; python_version >= "3.10" and python_version < "3.13"
google-generativeai==0.8.4 ; python_version >= "3.10" and python_version < "3.13"
google-resumable-media==2.7.2 ; python_version >= "3.10" and python_version < "3.13"
google-search-results==2.4.2 ; python_version >= "3.10" and python_version < "3.13"
google==3.0.0 ; python_version >= "3.10" and python_version < "3.13"
googleapis-common-protos==1.69.2 ; python_version >= "3.10" and python_version < "3.13"
gprofiler-official==1.0.0 ; python_version >= "3.10" and python_version < "3.13"
graspologic-native==1.2.3 ; python_version >= "3.10" and python_version < "3.13"
graspologic==3.4.1 ; python_version >= "3.10" and python_version < "3.13"
greenlet==3.0.3 ; python_version >= "3.10" and python_version < "3.13"
groq==0.9.0 ; python_version >= "3.10" and python_version < "3.13"
grpc-google-iam-v1==0.14.2 ; python_version >= "3.10" and python_version < "3.13"
grpcio-status==1.71.0 ; python_version >= "3.10" and python_version < "3.13"
grpcio==1.71.0 ; python_version >= "3.10" and python_version < "3.13"
h11==0.14.0 ; python_version >= "3.10" and python_version < "3.13"
h2==4.2.0 ; python_version >= "3.10" and python_version < "3.13"
hanziconv==0.3.2 ; python_version >= "3.10" and python_version < "3.13"
hf-transfer==0.1.9 ; python_version >= "3.10" and python_version < "3.13"
hpack==4.1.0 ; python_version >= "3.10" and python_version < "3.13"
html-text==0.6.2 ; python_version >= "3.10" and python_version < "3.13"
html2text==2024.2.26 ; python_version >= "3.10" and python_version < "3.13"
html5lib==1.1 ; python_version >= "3.10" and python_version < "3.13"
httpcore==1.0.7 ; python_version >= "3.10" and python_version < "3.13"
httplib2==0.22.0 ; python_version >= "3.10" and python_version < "3.13"
httpx-sse==0.4.0 ; python_version >= "3.10" and python_version < "3.13"
httpx==0.27.0 ; python_version >= "3.10" and python_version < "3.13"
huggingface-hub==0.25.2 ; python_version >= "3.10" and python_version < "3.13"
humanfriendly==10.0 ; python_version >= "3.10" and python_version < "3.13" and (sys_platform == "darwin" or platform_machine != "x86_64")
hyperframe==6.1.0 ; python_version >= "3.10" and python_version < "3.13"
hyppo==0.4.0 ; python_version >= "3.10" and python_version < "3.13"
idna==3.10 ; python_version >= "3.10" and python_version < "3.13"
ijson==3.3.0 ; python_version >= "3.10" and python_version < "3.13"
imagesize==1.4.1 ; python_version >= "3.10" and python_version < "3.13"
importlib-metadata==8.6.1 ; python_version >= "3.10" and python_version < "3.13"
infinity-emb==0.0.66 ; python_version >= "3.10" and python_version < "3.13"
infinity-sdk==0.6.0.dev3 ; python_version >= "3.10" and python_version < "3.13"
iniconfig==2.1.0 ; python_version >= "3.10" and python_version < "3.13"
inscriptis==2.5.3 ; python_version >= "3.10" and python_version < "3.13"
ir-datasets==0.5.10 ; python_version >= "3.10" and python_version < "3.13"
isodate==0.7.2 ; python_version >= "3.10" and python_version < "3.13"
itsdangerous==2.1.2 ; python_version >= "3.10" and python_version < "3.13"
jinja2==3.1.6 ; python_version >= "3.10" and python_version < "3.13"
jiter==0.9.0 ; python_version >= "3.10" and python_version < "3.13"
jmespath==1.0.1 ; python_version >= "3.10" and python_version < "3.13"
joblib==1.4.2 ; python_version >= "3.10" and python_version < "3.13"
json-repair==0.35.0 ; python_version >= "3.10" and python_version < "3.13"
jsonpath==0.82.2 ; python_version >= "3.10" and python_version < "3.13"
jsonschema-specifications==2024.10.1 ; python_version >= "3.10" and python_version < "3.13"
jsonschema==4.23.0 ; python_version >= "3.10" and python_version < "3.13"
kaitaistruct==0.10 ; python_version >= "3.10" and python_version < "3.13"
kiwisolver==1.4.8 ; python_version >= "3.10" and python_version < "3.13"
litellm==1.48.0 ; python_version >= "3.10" and python_version < "3.13"
llvmlite==0.44.0 ; python_version >= "3.10" and python_version < "3.13"
lxml-html-clean==0.4.1 ; python_version >= "3.10" and python_version < "3.13"
lxml==5.3.0 ; python_version >= "3.10" and python_version < "3.13"
lz4==4.4.3 ; python_version >= "3.10" and python_version < "3.13"
markdown-it-py==3.0.0 ; python_version >= "3.10" and python_version < "3.13"
markdown-to-json==2.1.1 ; python_version >= "3.10" and python_version < "3.13"
markdown==3.6 ; python_version >= "3.10" and python_version < "3.13"
markupsafe==3.0.2 ; python_version >= "3.10" and python_version < "3.13"
matplotlib==3.10.1 ; python_version >= "3.10" and python_version < "3.13"
mdurl==0.1.2 ; python_version >= "3.10" and python_version < "3.13"
#mini-racer==0.12.4 ; python_version >= "3.10" and python_version < "3.13" build机器无法访问mini编译的所需依赖，改为本地安装
minio==7.2.4 ; python_version >= "3.10" and python_version < "3.13"
mistralai==0.4.2 ; python_version >= "3.10" and python_version < "3.13"
mistune==3.1.3 ; python_version >= "3.10" and python_version < "3.13"
mpmath==1.3.0 ; python_version >= "3.10" and python_version < "3.13" and (sys_platform == "darwin" or platform_machine != "x86_64")
msal-extensions==1.3.1 ; python_version >= "3.10" and python_version < "3.13"
msal==1.32.0 ; python_version >= "3.10" and python_version < "3.13"
msgspec==0.19.0 ; python_version >= "3.10" and python_version < "3.13"
multidict==6.2.0 ; python_version >= "3.10" and python_version < "3.13"
multiprocess==0.70.15 ; python_version >= "3.10" and python_version < "3.13"
multitasking==0.0.11 ; python_version >= "3.10" and python_version < "3.13"
mygene==3.2.2 ; python_version >= "3.10" and python_version < "3.13"
nest-asyncio==1.6.0 ; python_version >= "3.10" and python_version < "3.13"
networkx==3.4.2 ; python_version >= "3.10" and python_version < "3.13"
nltk==3.9.1 ; python_version >= "3.10" and python_version < "3.13"
numba==0.61.0 ; python_version >= "3.10" and python_version < "3.13"
numpy==1.26.4 ; python_version >= "3.10" and python_version < "3.13"
ollama==0.2.1 ; python_version >= "3.10" and python_version < "3.13"
#onnxruntime==1.19.2 ; python_version >= "3.10" and python_version < "3.13"
onnxruntime==1.16.3 ; python_version >= "3.10" and python_version < "3.13"  # 使用1.16.3版本，因为1.19.2版本在build机器上找不到
openai==1.45.0 ; python_version >= "3.10" and python_version < "3.13"
opencv-python-headless==********* ; python_version >= "3.10" and python_version < "3.13"
opencv-python==********* ; python_version >= "3.10" and python_version < "3.13"
openpyxl==3.1.5 ; python_version >= "3.10" and python_version < "3.13"
orjson==3.10.15 ; python_version >= "3.10" and python_version < "3.13"
ormsgpack==1.5.0 ; python_version >= "3.10" and python_version < "3.13"
outcome==1.3.0.post0 ; python_version >= "3.10" and python_version < "3.13"
packaging==24.2 ; python_version >= "3.10" and python_version < "3.13"
pandas==2.2.3 ; python_version >= "3.10" and python_version < "3.13"
parameterized==0.9.0 ; python_version >= "3.10" and python_version < "3.13"
patsy==1.0.1 ; python_version >= "3.10" and python_version < "3.13"
pdfminer-six==20221105 ; python_version >= "3.10" and python_version < "3.13"
pdfplumber==0.10.4 ; python_version >= "3.10" and python_version < "3.13"
peewee==3.17.1 ; python_version >= "3.10" and python_version < "3.13"
pillow==10.4.0 ; python_version >= "3.10" and python_version < "3.13"
platformdirs==4.3.7 ; python_version >= "3.10" and python_version < "3.13"
playwright-stealth==1.0.6 ; python_version >= "3.10" and python_version < "3.13"
playwright==1.47.0 ; python_version >= "3.10" and python_version < "3.13"
pluggy==1.5.0 ; python_version >= "3.10" and python_version < "3.13"
polars-lts-cpu==1.9.0 ; python_version >= "3.10" and python_version < "3.13"
pooch==1.8.2 ; python_version >= "3.10" and python_version < "3.13"
pot==0.9.5 ; python_version >= "3.10" and python_version < "3.13"
primp==0.14.0 ; python_version >= "3.10" and python_version < "3.13"
proces==0.1.7 ; python_version >= "3.10" and python_version < "3.13"
prompt-toolkit==3.0.50 ; python_version >= "3.10" and python_version < "3.13"
propcache==0.3.0 ; python_version >= "3.10" and python_version < "3.13"
proto-plus==1.26.1 ; python_version >= "3.10" and python_version < "3.13"
protobuf==5.27.2 ; python_version >= "3.10" and python_version < "3.13"
psycopg2-binary==2.9.9 ; python_version >= "3.10" and python_version < "3.13"
py-mini-racer==0.6.0 ; python_version >= "3.10" and python_version < "3.13" and platform_system == "Linux"
py==1.11.0 ; python_version >= "3.10" and python_version < "3.13"
pyarrow==17.0.0 ; python_version >= "3.10" and python_version < "3.13"
pyasn1-modules==0.4.1 ; python_version >= "3.10" and python_version < "3.13"
pyasn1==0.6.1 ; python_version >= "3.10" and python_version < "3.13"
pyclipper==1.3.0.post5 ; python_version >= "3.10" and python_version < "3.13"
pycparser==2.22 ; python_version >= "3.10" and python_version < "3.13"
pycryptodome==3.9.9 ; python_version >= "3.10" and python_version < "3.13"
pycryptodomex==3.20.0 ; python_version >= "3.10" and python_version < "3.13"
pydantic-core==2.23.4 ; python_version >= "3.10" and python_version < "3.13"
pydantic==2.9.2 ; python_version >= "3.10" and python_version < "3.13"
pydash==7.0.7 ; python_version >= "3.10" and python_version < "3.13"
pydivert==2.1.0 ; python_version >= "3.10" and python_version < "3.13" and sys_platform == "win32"
pyee==12.0.0 ; python_version >= "3.10" and python_version < "3.13"
pyexecjs==1.5.1 ; python_version >= "3.10" and python_version < "3.13"
pygments==2.19.1 ; python_version >= "3.10" and python_version < "3.13"
pyicu==2.4.3 ; python_version >= "3.10" and python_version < "3.13" # 因centos7不支持c++17，下调到2.4.3版本，原版本2.14
pyjwt==2.8.0 ; python_version >= "3.10" and python_version < "3.13"
pymysql==1.1.1 ; python_version >= "3.10" and python_version < "3.13"
pynndescent==0.5.13 ; python_version >= "3.10" and python_version < "3.13"
pyodbc==5.2.0 ; python_version >= "3.10" and python_version < "3.13"
pyopenssl==25.0.0 ; python_version >= "3.10" and python_version < "3.13"
pyparsing==3.2.1 ; python_version >= "3.10" and python_version < "3.13"
pypdf2==3.0.1 ; python_version >= "3.10" and python_version < "3.13"
pypdf==5.4.0 ; python_version >= "3.10" and python_version < "3.13"
pypdfium2==4.30.1 ; python_version >= "3.10" and python_version < "3.13"
pyreadline3==3.5.4 ; python_version >= "3.10" and python_version < "3.13" and platform_machine != "x86_64" and sys_platform == "win32"
pysocks==1.7.1 ; python_version >= "3.10" and python_version < "3.13"
pytest==8.3.5 ; python_version >= "3.10" and python_version < "3.13"
python-dateutil==2.8.2 ; python_version >= "3.10" and python_version < "3.13"
python-docx==1.1.2 ; python_version >= "3.10" and python_version < "3.13"
python-dotenv==1.0.1 ; python_version >= "3.10" and python_version < "3.13"
python-pptx==1.0.2 ; python_version >= "3.10" and python_version < "3.13"
pytz==2020.5 ; python_version >= "3.10" and python_version < "3.13"
pywencai==0.12.2 ; python_version >= "3.10" and python_version < "3.13"
pyyaml==6.0.2 ; python_version >= "3.10" and python_version < "3.13"
qianfan==0.4.6 ; python_version >= "3.10" and python_version < "3.13"
ranx==0.3.20 ; python_version >= "3.10" and python_version < "3.13"
readability-lxml==0.8.1 ; python_version >= "3.10" and python_version < "3.13"
readerwriterlock==1.0.9 ; python_version >= "3.10" and python_version < "3.13"
referencing==0.36.2 ; python_version >= "3.10" and python_version < "3.13"
regex==2024.11.6 ; python_version >= "3.10" and python_version < "3.13"
replicate==0.31.0 ; python_version >= "3.10" and python_version < "3.13"
requests==2.32.2 ; python_version >= "3.10" and python_version < "3.13"
retry==0.9.2 ; python_version >= "3.10" and python_version < "3.13"
rich==13.9.4 ; python_version >= "3.10" and python_version < "3.13"
roman-numbers==1.0.2 ; python_version >= "3.10" and python_version < "3.13"
roman-numerals-py==3.1.0 ; python_version >= "3.11" and python_version < "3.13"
rpds-py==0.23.1 ; python_version >= "3.10" and python_version < "3.13"
rsa==4.9 ; python_version >= "3.10" and python_version < "3.13"
ruamel-base==1.0.0 ; python_version >= "3.10" and python_version < "3.13"
ruamel-yaml-clib==0.2.12 ; python_version >= "3.10" and python_version < "3.13" and platform_python_implementation == "CPython"
ruamel-yaml==0.18.10 ; python_version >= "3.10" and python_version < "3.13"
s3transfer==0.10.4 ; python_version >= "3.10" and python_version < "3.13"
scholarly==1.7.11 ; python_version >= "3.10" and python_version < "3.13"
scikit-learn==1.5.0 ; python_version >= "3.10" and python_version < "3.13"
scipy==1.12.0 ; python_version >= "3.10" and python_version < "3.13"
seaborn==0.13.2 ; python_version >= "3.10" and python_version < "3.13"
selenium-wire==5.1.0 ; python_version >= "3.10" and python_version < "3.13"
selenium==4.22.0 ; python_version >= "3.10" and python_version < "3.13"
setuptools==75.2.0 ; python_version >= "3.10" and python_version < "3.13"
sgmllib3k==1.0.0 ; python_version >= "3.10" and python_version < "3.13"
shapely==2.0.5 ; python_version >= "3.10" and python_version < "3.13"
shellingham==1.5.4 ; python_version >= "3.10" and python_version < "3.13"
six==1.16.0 ; python_version >= "3.10" and python_version < "3.13"
smart-open==7.1.0 ; python_version >= "3.10" and python_version < "3.13"
sniffio==1.3.1 ; python_version >= "3.10" and python_version < "3.13"
snowballstemmer==2.2.0 ; python_version >= "3.10" and python_version < "3.13"
sortedcontainers==2.4.0 ; python_version >= "3.10" and python_version < "3.13"
soupsieve==2.6 ; python_version >= "3.10" and python_version < "3.13"
sphinx-rtd-theme==3.0.2 ; python_version >= "3.10" and python_version < "3.13"
sphinx==8.1.3 ; python_version == "3.10"
sphinx==8.2.3 ; python_version >= "3.11" and python_version < "3.13"
sphinxcontrib-applehelp==2.0.0 ; python_version >= "3.10" and python_version < "3.13"
sphinxcontrib-devhelp==2.0.0 ; python_version >= "3.10" and python_version < "3.13"
sphinxcontrib-htmlhelp==2.1.0 ; python_version >= "3.10" and python_version < "3.13"
sphinxcontrib-jquery==4.1 ; python_version >= "3.10" and python_version < "3.13"
sphinxcontrib-jsmath==1.0.1 ; python_version >= "3.10" and python_version < "3.13"
sphinxcontrib-qthelp==2.0.0 ; python_version >= "3.10" and python_version < "3.13"
sphinxcontrib-serializinghtml==2.0.0 ; python_version >= "3.10" and python_version < "3.13"
sqlglot==11.7.1 ; python_version >= "3.10" and python_version < "3.13"
statsmodels==0.14.4 ; python_version >= "3.10" and python_version < "3.13"
strenum==0.4.15 ; python_version >= "3.10" and python_version < "3.13"
sympy==1.13.1 ; python_version >= "3.10" and python_version < "3.13" and (sys_platform == "darwin" or platform_machine != "x86_64")
tabulate==0.9.0 ; python_version >= "3.10" and python_version < "3.13"
tavily-python==0.5.1 ; python_version >= "3.10" and python_version < "3.13"
tenacity==8.5.0 ; python_version >= "3.10" and python_version < "3.13"
tencentcloud-sdk-python==3.0.1215 ; python_version >= "3.10" and python_version < "3.13"
threadpoolctl==3.6.0 ; python_version >= "3.10" and python_version < "3.13"
thrift==0.20.0 ; python_version >= "3.10" and python_version < "3.13"
tika==2.6.0 ; python_version >= "3.10" and python_version < "3.13"
tiktoken==0.7.0 ; python_version >= "3.10" and python_version < "3.13"
tokenizers==0.15.2 ; python_version >= "3.10" and python_version < "3.13"
tomli==2.2.1 ; python_version == "3.10"
tqdm==4.67.1 ; python_version >= "3.10" and python_version < "3.13"
trec-car-tools==2.6 ; python_version >= "3.10" and python_version < "3.13"
trio-websocket==0.12.2 ; python_version >= "3.10" and python_version < "3.13"
trio==0.29.0 ; python_version >= "3.10" and python_version < "3.13"
typer==0.15.2 ; python_version >= "3.10" and python_version < "3.13"
types-python-dateutil==2.9.0.20241206 ; python_version >= "3.10" and python_version < "3.13"
types-requests==2.32.0.20250306 ; python_version >= "3.10" and python_version < "3.13"
typing-extensions==4.12.2 ; python_version >= "3.10" and python_version < "3.13"
tzdata==2025.1 ; python_version >= "3.10" and python_version < "3.13"
umap-learn==0.5.6 ; python_version >= "3.10" and python_version < "3.13"
unlzw3==0.2.3 ; python_version >= "3.10" and python_version < "3.13"
uritemplate==4.1.1 ; python_version >= "3.10" and python_version < "3.13"
urllib3==2.3.0 ; python_version >= "3.10" and python_version < "3.13"
valkey==6.0.2 ; python_version >= "3.10" and python_version < "3.13"
vertexai==1.64.0 ; python_version >= "3.10" and python_version < "3.13"
volcengine==1.0.146 ; python_version >= "3.10" and python_version < "3.13"
voyageai==0.2.3 ; python_version >= "3.10" and python_version < "3.13"
warc3-wet-clueweb09==0.2.5 ; python_version >= "3.10" and python_version < "3.13"
warc3-wet==0.2.5 ; python_version >= "3.10" and python_version < "3.13"
wcwidth==0.2.13 ; python_version >= "3.10" and python_version < "3.13"
webdriver-manager==4.0.1 ; python_version >= "3.10" and python_version < "3.13"
webencodings==0.5.1 ; python_version >= "3.10" and python_version < "3.13"
websocket-client==1.8.0 ; python_version >= "3.10" and python_version < "3.13"
werkzeug==3.0.6 ; python_version >= "3.10" and python_version < "3.13"
wikipedia==1.4.0 ; python_version >= "3.10" and python_version < "3.13"
word2number==1.1 ; python_version >= "3.10" and python_version < "3.13"
wrapt==1.17.2 ; python_version >= "3.10" and python_version < "3.13"
wsproto==1.2.0 ; python_version >= "3.10" and python_version < "3.13"
xgboost==1.5.0 ; python_version >= "3.10" and python_version < "3.13"
xlrd==2.0.1 ; python_version >= "3.10" and python_version < "3.13"
xlsxwriter==3.2.2 ; python_version >= "3.10" and python_version < "3.13"
xpinyin==0.7.6 ; python_version >= "3.10" and python_version < "3.13"
xxhash==3.5.0 ; python_version >= "3.10" and python_version < "3.13"
yarl==1.18.3 ; python_version >= "3.10" and python_version < "3.13"
yfinance==0.1.96 ; python_version >= "3.10" and python_version < "3.13"
zhipuai==2.0.1 ; python_version >= "3.10" and python_version < "3.13"
zipp==3.21.0 ; python_version >= "3.10" and python_version < "3.13"
zlib-state==0.1.9 ; python_version >= "3.10" and python_version < "3.13"
zstandard==0.23.0 ; python_version >= "3.10" and python_version < "3.13"
dbutils==3.1.0 ; python_version >= "3.10" and python_version < "3.13"
mssapi-mt==1.5.2 ; python_version >= "3.10" and python_version < "3.13"
mt-squirrel-proxy==1.0.5 ; python_version >= "3.10" and python_version < "3.13"
zebraproxyclient==0.0.9 ; python_version >= "3.10" and python_version < "3.13"
sqlalchemy>=2.0.27,<3.0.0 ; python_version >= "3.10" and python_version < "3.13"

# Ragas评估框架
ragas==0.1.9
datasets==2.14.6