#
#  Copyright 2025 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
import logging


import os
import sys

# 添加项目根目录到导入路径，解决直接运行时的导入问题
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    # 获取当前文件所在目录的上两级，即项目根目录
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
    sys.path.insert(0, project_root)

import time
from io import BytesIO

# 导入KMS相关模块

from pycat import Cat
from kms_sdk.kms import Kms
from kms_sdk.utils.exceptions import KmsResultNullException
from mssapi.s3.connection import S3Connection
from mssapi.s3.key import Key
from mssapi.exception import MssapiServerError, MssapiClientError
from mssapi.direct_connect import register_direct_conn
from rag.utils import singleton
from api.utils.config_loader import get_mss_s3_config

@singleton
class RAGFlowMTS3(object):
    # 固定bucket数量
    BUCKET_NAME = "rag-storage"
    
    # KMS密钥名称
    KMS_SECRET_KEY_NAME = "s3plus_service_secret_key"
    KMS_ACCESS_KEY_NAME = "s3plus_service_access_key"
    
    def __init__(self):
        self.conn = None
        self.buckets = {}  # 缓存已初始化的bucket对象
        
        # 使用新的配置加载机制获取S3配置
        self.mss_config = get_mss_s3_config()
        
        # 从配置中获取连接参数
        self.appkey = self.mss_config.get('appkey', None)
        self.access_key = self.mss_config.get('access_key', None)
        self.secret_key = self.mss_config.get('secret_key', None)
        
        # 如果没有配置密钥，尝试从KMS获取
        if not self.appkey:
            self._fetch_keys_from_kms()
            
        self.endpoint = self.mss_config.get('endpoint', None)
        self.use_https = self.mss_config.get('use_https', False)
        self.use_direct_conn = self.mss_config.get('use_direct_conn', False)
        
        # 记录配置信息
        logging.info(f"MSS S3配置: endpoint={self.endpoint}, use_https={self.use_https}, use_direct_conn={self.use_direct_conn}")
        
        self.__open__()

    def _fetch_keys_from_kms(self):
        """从KMS获取access_key和secret_key"""
        try:
            # 初始化Cat
            try:
                Cat.init_cat(self.appkey)
            except Exception as e:
                logging.warning(f"Cat初始化失败: {str(e)}")
                pass

            # 获取secret_key
            try:
                self.secret_key = Kms.get_by_name(self.appkey, self.KMS_SECRET_KEY_NAME)
            except KmsResultNullException as e:
                logging.error(f"从KMS获取secret_key失败: {e.code} - {e.msg}")
            except Exception as e:
                logging.exception(f"从KMS获取secret_key时发生错误: {str(e)}")
                
            # 获取access_key
            try:
                self.access_key = Kms.get_by_name(self.appkey, self.KMS_ACCESS_KEY_NAME)
            except KmsResultNullException as e:
                logging.error(f"从KMS获取access_key失败: {e.code} - {e.msg}")
            except Exception as e:
                logging.exception(f"从KMS获取access_key时发生错误: {str(e)}")
        except Exception as e:
            logging.exception(f"从KMS获取密钥时发生错误: {str(e)}")

    def __open__(self):
        try:
            if self.conn:
                self.__close__()
        except Exception:
            pass

        try:
            # 优先使用透明密钥（KMS）方式
            if self.appkey:
                logging.info("使用透明密钥(appkey)方式连接MSS S3")
                self.conn = S3Connection(
                    kms_appkey=self.appkey,
                    is_secure=self.use_https,
                    host=self.endpoint
                )
            # 备选使用账号密钥方式
            elif self.access_key and self.secret_key:
                logging.info("使用账号密钥方式连接MSS S3")
                self.conn = S3Connection(
                    aws_access_key_id=self.access_key,
                    aws_secret_access_key=self.secret_key,
                    is_secure=self.use_https,
                    host=self.endpoint
                )
            else:
                logging.error("MSS S3连接失败: 没有提供有效的凭证")
                return
                
            # 如果配置了使用直连
            if self.use_direct_conn:
                register_direct_conn(self.conn)
                logging.info("MSS S3直连已启用")
        except Exception as e:
            logging.exception(f"连接MSS S3 ({self.endpoint})失败: {str(e)}")

    def __close__(self):
        self.buckets = {}
        del self.conn
        self.conn = None

    def _get_real_bucket(self, bucket):
        """
        将知识库ID映射到固定的bucket
        """
        # if bucket == "txtxtxtxt1":  # 保留健康检查bucket
        #     return bucket
        return self.BUCKET_NAME
    
    def _get_real_path(self, bucket, fnm):
        """
        生成真实的存储路径
        """
        if bucket == "txtxtxtxt1":  # 健康检查文件路径不变
            return fnm
        return f"{bucket}/{fnm}"
    
    def _get_bucket(self, bucket_name):
        """
        获取或创建bucket对象
        """
        real_bucket = self._get_real_bucket(bucket_name)
        
        # 如果bucket已缓存，直接返回
        if real_bucket in self.buckets:
            return self.buckets[real_bucket]
            
        try:
            # 检查bucket是否存在
            if real_bucket in self.conn:
                bucket = self.conn.get_bucket(real_bucket)
            else:
                bucket = self.conn.create_bucket(real_bucket)
                logging.info(f"Created new bucket: {real_bucket}")
                
            # 缓存bucket对象
            self.buckets[real_bucket] = bucket
            return bucket
        except (MssapiServerError, MssapiClientError) as e:
            logging.exception(f"Failed to get or create bucket {real_bucket}: {str(e)}")
            return None

    def bucket_exists(self, bucket):
        real_bucket = self._get_real_bucket(bucket)
        try:
            return real_bucket in self.conn
        except Exception:
            logging.exception(f"Error checking if bucket exists: {real_bucket}")
            return False

    def health(self):
        bucket_name, key_name, binary = "txtxtxtxt1", "txtxtxtxt1", b"_t@@@1"
        
        try:
            bucket = self._get_bucket(bucket_name)
            if not bucket:
                return False
                
            key = bucket.new_key(key_name)
            key.set_contents_from_string(binary)
            return True
        except Exception as e:
            logging.exception(f"Health check failed: {str(e)}")
            return False

    def get_properties(self, bucket, key_name):
        try:
            bucket_obj = self._get_bucket(bucket)
            if not bucket_obj:
                return {}
                
            key = bucket_obj.get_key(self._get_real_path(bucket, key_name))
            if not key:
                return {}
                
            # 构建属性字典
            props = {
                'content_type': key.content_type,
                'etag': key.etag,
                'last_modified': key.last_modified,
                'size': key.size
            }
            
            # 获取自定义元数据
            metadata = {}
            for name in key.metadata:
                metadata[name] = key.get_metadata(name)
                
            props['metadata'] = metadata
            return props
        except Exception:
            logging.exception(f"Error getting properties for {bucket}/{key_name}")
            return {}

    def list(self, bucket, dir_prefix, recursive=True):
        try:
            bucket_obj = self._get_bucket(bucket)
            if not bucket_obj:
                return []
                
            real_prefix = self._get_real_path(bucket, dir_prefix)
            
            # 如果是非递归模式，则使用delimiter参数
            delimiter = None if recursive else '/'
            
            # 获取对象列表
            objects = list(bucket_obj.list(prefix=real_prefix, delimiter=delimiter))
            
            # 转换为简化的列表结构
            result = []
            for obj in objects:
                if hasattr(obj, 'name'):  # Key对象
                    # 从完整路径中提取相对路径
                    if bucket in obj.name:
                        name = obj.name.split(f"{bucket}/", 1)[1] if f"{bucket}/" in obj.name else obj.name
                    else:
                        name = obj.name
                        
                    result.append({
                        'name': name,
                        'size': obj.size,
                        'last_modified': obj.last_modified,
                        'is_dir': False
                    })
                else:  # CommonPrefix对象（文件夹）
                    # 从前缀中提取文件夹名
                    prefix = obj.name
                    if bucket in prefix:
                        prefix = prefix.split(f"{bucket}/", 1)[1] if f"{bucket}/" in prefix else prefix
                    
                    result.append({
                        'name': prefix,
                        'is_dir': True
                    })
            
            return result
        except Exception:
            logging.exception(f"Error listing objects in {bucket}/{dir_prefix}")
            return []

    def put(self, bucket, fnm, binary):
        for _ in range(3):  # 重试3次
            try:
                bucket_obj = self._get_bucket(bucket)
                if not bucket_obj:
                    continue
                    
                real_path = self._get_real_path(bucket, fnm)
                key = bucket_obj.new_key(real_path)
                key.set_contents_from_string(binary)
                return True
            except Exception:
                logging.exception(f"Failed to put {bucket}/{fnm}")
                self.__open__()  # 重新连接
                time.sleep(1)
        return False

    def rm(self, bucket, fnm):
        try:
            bucket_obj = self._get_bucket(bucket)
            if not bucket_obj:
                return False
                
            real_path = self._get_real_path(bucket, fnm)
            bucket_obj.delete_key(real_path)
            return True
        except Exception:
            logging.exception(f"Failed to remove {bucket}/{fnm}")
            return False

    def get(self, bucket, fnm):
        for _ in range(3):  # 重试3次
            try:
                bucket_obj = self._get_bucket(bucket)
                if not bucket_obj:
                    continue
                    
                real_path = self._get_real_path(bucket, fnm)
                key = bucket_obj.get_key(real_path)
                if not key:
                    logging.warning(f"Key not found: {bucket}/{fnm}")
                    return None
                    
                data = key.get_contents_as_string()
                key.close()  # 关闭连接，避免脏数据
                return data
            except Exception:
                logging.exception(f"Failed to get {bucket}/{fnm}")
                self.__open__()  # 重新连接
                time.sleep(1)
        return None

    def obj_exist(self, bucket, fnm):
        try:
            bucket_obj = self._get_bucket(bucket)
            if not bucket_obj:
                return False
                
            real_path = self._get_real_path(bucket, fnm)
            return real_path in bucket_obj
        except Exception:
            logging.exception(f"Error checking if object exists: {bucket}/{fnm}")
            return False

    def get_presigned_url(self, bucket, fnm, expires):
        for _ in range(3):  # 重试3次
            try:
                bucket_obj = self._get_bucket(bucket)
                if not bucket_obj:
                    continue
                    
                real_path = self._get_real_path(bucket, fnm)
                key = bucket_obj.get_key(real_path)
                if not key:
                    logging.warning(f"Key not found for presigned URL: {bucket}/{fnm}")
                    return None
                    
                # 生成预签名URL
                url = key.generate_url(expires_in=expires, force_http=not self.use_https)
                return url
            except Exception:
                logging.exception(f"Failed to generate presigned URL for {bucket}/{fnm}")
                self.__open__()  # 重新连接
                time.sleep(1)
        return None

# 全局单例
MSS = RAGFlowMTS3()

def test_connection():
    """测试MSS S3连接是否正常工作"""
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger("MSS-S3-Test")

    
    # 1. 测试连接和健康检查
    logger.info("执行健康检查...")
    health_result = MSS.health()
    if health_result:
        logger.info("健康检查成功！✅")
    else:
        logger.error("健康检查失败！❌")
        return False
    
    # 2. 尝试列出所有bucket
    # try:
    #     logger.info("获取所有bucket...")
    #     buckets = list(MSS.conn)
    #     logger.info(f"找到 {len(buckets)} 个bucket:")
    #     for b in buckets:
    #         logger.info(f" - {b.name}")
    # except Exception as e:
    #     logger.error(f"列出bucket时出错: {str(e)}")
    #     return False
    
    # 3. 测试一个简单的上传和下载
    test_bucket_name = "rag-storage"
    test_key_name = f"test-file-{int(time.time())}.txt"
    test_content = f"测试文件内容 - {time.time()}".encode('utf-8')
    
    logger.info(f"测试上传到 {test_bucket_name}/{test_key_name}...")
    upload_result = MSS.put(test_bucket_name, test_key_name, test_content)
    
    if upload_result:
        logger.info("文件上传成功 ✅")
        
        # 验证文件存在
        if MSS.obj_exist(test_bucket_name, test_key_name):
            logger.info("文件存在验证成功 ✅")
            
            # 下载文件并验证内容
            logger.info("下载并验证文件内容...")
            downloaded_content = MSS.get(test_bucket_name, test_key_name)
            
            if downloaded_content == test_content:
                logger.info("文件内容验证成功 ✅")
                
                # 检查文件属性
                logger.info("检查文件属性...")
                props = MSS.get_properties(test_bucket_name, test_key_name)
                if props and 'size' in props:
                    logger.info(f"文件属性: 大小={props['size']}字节, 类型={props.get('content_type', '未知')}")
                    logger.info("文件属性获取成功 ✅")
                
                # 生成预签名URL
                logger.info("生成预签名URL...")
                url = MSS.get_presigned_url(test_bucket_name, test_key_name, 3600)
                if url:
                    logger.info(f"预签名URL: {url}")
                    logger.info("预签名URL生成成功 ✅")
                
                # 删除测试文件
                # logger.info("删除测试文件...")
                # if MSS.rm(test_bucket_name, test_key_name):
                #     logger.info("文件删除成功 ✅")
                # else:
                #     logger.warning("文件删除失败 ⚠️")
            else:
                logger.error("文件内容验证失败 ❌")
                return False
        else:
            logger.error("文件存在验证失败 ❌")
            return False
    else:
        logger.error("文件上传失败 ❌")
        return False
    
    logger.info("所有测试通过! MSS S3连接工作正常 ✅✅✅")
    return True

if __name__ == "__main__":
    # 直接运行此文件时执行测试
    test_connection()