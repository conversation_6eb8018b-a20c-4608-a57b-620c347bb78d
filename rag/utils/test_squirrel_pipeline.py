import os
import sys
import json
import time
import uuid
import logging

# 添加项目根目录到导入路径，解决直接运行时的导入问题
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 获取当前文件所在目录的上两级，即项目根目录
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
sys.path.insert(0, project_root)

from rag.utils.mt_squirrel_conn import MT_SQUIRREL_CONN


def test_basic_pipeline():
    """测试基本的pipeline操作"""
    logging.info("===== 开始测试基本pipeline功能 =====")
    
    try:
        # 生成测试键名前缀，避免冲突
        prefix = f"test_pipeline_{uuid.uuid4().hex[:8]}"
        key1 = f"{prefix}_key1"
        key2 = f"{prefix}_key2"
        key3 = f"{prefix}_key3"
        
        # 构建category key
        cat_key1 = MT_SQUIRREL_CONN.build_category_key(key1)
        cat_key2 = MT_SQUIRREL_CONN.build_category_key(key2)
        cat_key3 = MT_SQUIRREL_CONN.build_category_key(key3)
        
        logging.info(f"测试键: {cat_key1}, {cat_key2}, {cat_key3}")
        
        # 创建pipeline并执行多个命令
        pipeline = MT_SQUIRREL_CONN.REDIS.pipeline()
        pipeline.set(cat_key1, "value1")
        pipeline.set(cat_key2, "value2")
        pipeline.set(cat_key3, "value3")
        results = pipeline.execute()
        
        logging.info(f"Pipeline执行结果: {results}")
        
        # 验证结果
        val1 = MT_SQUIRREL_CONN.REDIS.get(cat_key1)
        val2 = MT_SQUIRREL_CONN.REDIS.get(cat_key2)
        val3 = MT_SQUIRREL_CONN.REDIS.get(cat_key3)
        
        logging.info(f"验证结果: {val1}, {val2}, {val3}")
        
        assert val1 == "value1", f"键{cat_key1}的值应为'value1'，但实际为{val1}"
        assert val2 == "value2", f"键{cat_key2}的值应为'value2'，但实际为{val2}"
        assert val3 == "value3", f"键{cat_key3}的值应为'value3'，但实际为{val3}"
        
        # 清理测试数据
        pipeline = MT_SQUIRREL_CONN.REDIS.pipeline()
        pipeline.delete(cat_key1)
        pipeline.delete(cat_key2)
        pipeline.delete(cat_key3)
        pipeline.execute()
        
        logging.info("基本pipeline功能测试通过!")
    except Exception as e:
        logging.error(f"基本pipeline功能测试失败: {str(e)}")
        raise


def test_transaction_pipeline():
    """测试transaction方法中的pipeline事务功能"""
    logging.info("===== 开始测试transaction事务功能 =====")
    
    try:
        # 生成测试键名，避免冲突
        key = f"test_transaction_{uuid.uuid4().hex[:8]}"
        value = f"transaction_value_{int(time.time())}"
        exp = 60  # 60秒过期
        
        logging.info(f"测试键: {key}, 值: {value}, 过期时间: {exp}秒")
        
        # 使用transaction方法（内部使用pipeline事务）
        result = MT_SQUIRREL_CONN.transaction(key, value, exp)
        
        logging.info(f"Transaction执行结果: {result}")
        
        # 验证结果
        cat_key = MT_SQUIRREL_CONN.build_category_key(key)
        val = MT_SQUIRREL_CONN.REDIS.get(cat_key)
        ttl = MT_SQUIRREL_CONN.REDIS.ttl(cat_key)
        
        logging.info(f"验证结果: 键存在={val == value}, 值={val}, TTL={ttl}秒")
        
        assert val == value, f"键{cat_key}的值应为{value}，但实际为{val}"
        assert 0 < ttl <= exp, f"键{cat_key}的TTL应在0到{exp}秒之间，但实际为{ttl}秒"
        
        # 清理测试数据
        MT_SQUIRREL_CONN.REDIS.delete(cat_key)
        
        logging.info("Transaction事务功能测试通过!")
    except Exception as e:
        logging.error(f"Transaction事务功能测试失败: {str(e)}")
        raise


def test_queue_pipeline():
    """测试queue_product方法中的pipeline功能"""
    logging.info("===== 开始测试队列pipeline功能 =====")
    
    try:
        # 生成测试队列名和消息
        queue_name = f"test_queue_{uuid.uuid4().hex[:8]}"
        group_name = "test_group"
        consumer_name = "test_consumer"
        message_id = str(uuid.uuid4())
        message = {
            "id": message_id,
            "content": f"测试队列消息 {int(time.time())}",
            "timestamp": time.time()
        }
        
        logging.info(f"测试队列: {queue_name}, 消息ID: {message_id}")
        
        # 使用queue_product方法（内部使用pipeline）
        result = MT_SQUIRREL_CONN.queue_product(queue_name, message)
        
        logging.info(f"Queue product执行结果: {result}")
        
        # 验证结果 - 尝试从队列中消费消息
        msg = MT_SQUIRREL_CONN.queue_consumer(queue_name, group_name, consumer_name)
        
        if msg:
            received_msg = msg.get_message()
            logging.info(f"接收到的消息: {received_msg}")
            
            assert received_msg["id"] == message_id, f"消息ID应为{message_id}，但实际为{received_msg.get('id')}"
            
            # 确认消息
            msg.ack()
        else:
            logging.error(f"未能从队列{queue_name}中消费到消息")
            assert False, f"未能从队列{queue_name}中消费到消息"
        
        # 清理不需要，Redis Stream会保留
        
        logging.info("队列pipeline功能测试通过!")
    except Exception as e:
        logging.error(f"队列pipeline功能测试失败: {str(e)}")
        raise


def test_multi_exec_pipeline():
    """测试MULTI/EXEC事务模式的pipeline"""
    logging.info("===== 开始测试MULTI/EXEC事务功能 =====")
    
    try:
        # 生成测试键名前缀，避免冲突
        prefix = f"test_multi_exec_{uuid.uuid4().hex[:8]}"
        key1 = f"{prefix}_key1"
        key2 = f"{prefix}_key2"
        
        # 构建category key
        cat_key1 = MT_SQUIRREL_CONN.build_category_key(key1)
        cat_key2 = MT_SQUIRREL_CONN.build_category_key(key2)
        
        logging.info(f"测试键: {cat_key1}, {cat_key2}")
        
        # 创建事务pipeline
        pipeline = MT_SQUIRREL_CONN.REDIS.pipeline(transaction=True)
        pipeline.set(cat_key1, "tx_value1")
        pipeline.set(cat_key2, "tx_value2")
        # 添加一些读取操作
        pipeline.get(cat_key1)
        pipeline.get(cat_key2)
        results = pipeline.execute()
        
        logging.info(f"MULTI/EXEC事务执行结果: {results}")
        
        # 验证结果
        val1 = MT_SQUIRREL_CONN.REDIS.get(cat_key1)
        val2 = MT_SQUIRREL_CONN.REDIS.get(cat_key2)
        
        logging.info(f"验证结果: {val1}, {val2}")
        
        assert val1 == "tx_value1", f"键{cat_key1}的值应为'tx_value1'，但实际为{val1}"
        assert val2 == "tx_value2", f"键{cat_key2}的值应为'tx_value2'，但实际为{val2}"
        
        # 清理测试数据
        pipeline = MT_SQUIRREL_CONN.REDIS.pipeline()
        pipeline.delete(cat_key1)
        pipeline.delete(cat_key2)
        pipeline.execute()
        
        logging.info("MULTI/EXEC事务功能测试通过!")
    except Exception as e:
        logging.error(f"MULTI/EXEC事务功能测试失败: {str(e)}")
        raise


def test_watch_pipeline():
    """测试WATCH乐观锁功能的pipeline"""
    logging.info("===== 开始测试WATCH乐观锁功能 =====")
    
    try:
        # 生成测试键名，避免冲突
        key = f"test_watch_{uuid.uuid4().hex[:8]}"
        cat_key = MT_SQUIRREL_CONN.build_category_key(key)
        
        logging.info(f"测试键: {cat_key}")
        
        # 设置初始值
        MT_SQUIRREL_CONN.REDIS.set(cat_key, "initial")
        
        # 开始WATCH监视
        pipeline = MT_SQUIRREL_CONN.REDIS.pipeline(transaction=True)
        
        # 在Squirrel SDK中测试WATCH功能，这里可能会失败，因为Squirrel SDK的限制
        try:
            pipeline.watch(cat_key)
            
            # 在事务外部修改键值
            MT_SQUIRREL_CONN.REDIS.set(cat_key, "modified")
            
            # 开始事务
            pipeline.multi()
            pipeline.set(cat_key, "final")
            results = pipeline.execute()
            
            logging.info(f"WATCH功能执行结果: {results}")
            
            # 检查结果（如果WATCH工作正常，应该是空结果，表示事务被取消）
            val = MT_SQUIRREL_CONN.REDIS.get(cat_key)
            logging.info(f"键值为: {val}")
            
            # 注意：如果Squirrel SDK不支持WATCH，这里将不会失败
            if val == "final":
                logging.warning("注意：WATCH功能似乎没有生效，可能是Squirrel SDK限制")
        except Exception as e:
            logging.warning(f"WATCH操作异常: {str(e)}")
            logging.warning("这可能是因为Squirrel SDK不完全支持WATCH功能")
        
        # 清理测试数据
        MT_SQUIRREL_CONN.REDIS.delete(cat_key)
        
        logging.info("WATCH乐观锁功能测试完成!")
    except Exception as e:
        logging.error(f"WATCH乐观锁功能测试失败: {str(e)}")
        raise


def run_all_tests():
    """运行所有pipeline测试"""
    try:
        # 先检查连接是否成功
        if not MT_SQUIRREL_CONN.is_alive():
            logging.error("无法连接到Squirrel Redis，测试终止")
            return False
            
        # 健康检查
        try:
            if not MT_SQUIRREL_CONN.health():
                logging.error("Squirrel Redis服务健康检查失败，测试终止")
                return False
        except Exception as e:
            logging.exception(f"健康检查异常: {str(e)}")
            return False
            
        logging.info("开始执行pipeline功能测试...")
        
        # 执行各项测试
        test_basic_pipeline()
        test_transaction_pipeline()
        test_queue_pipeline()
        test_multi_exec_pipeline()
        test_watch_pipeline()
        
        logging.info("所有pipeline功能测试通过!")
        return True
    except Exception as e:
        logging.exception(f"Pipeline功能测试过程中发生异常: {str(e)}")
        return False


if __name__ == "__main__":
    run_all_tests() 