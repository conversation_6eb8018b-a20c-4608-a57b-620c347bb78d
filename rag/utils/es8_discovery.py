#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ES8集群节点发现模块
提供从OpenAPI获取ES集群节点信息的功能
"""

import requests
import logging
import json
import base64
from typing import List, Dict, Optional, Union
import os

# 配置日志
logger = logging.getLogger('ragflow.es8_discovery')

class ES8Discovery:
    """
    ES8集群节点发现类
    用于从OpenAPI获取ES集群节点信息
    """
    
    OPENAPI_BASE_URL = "http://openapi.eagle.test.sankuai.com/openapi"
    
    @staticmethod
    def generate_token(app_key, access_key):
        """
        生成认证Token
        
        参数:
            app_key (str): 应用Key
            access_key (str): 访问Key
            
        返回:
            str: 生成的Base64编码Token
        """
        auth_str = f"{app_key}:{access_key}"
        token = base64.b64encode(auth_str.encode()).decode()
        return token
    
    def __init__(self, base_url: Optional[str] = None, token: Optional[str] = None,
                app_key: Optional[str] = None, access_key: Optional[str] = None):
        """
        初始化ES8Discovery
        
        参数:
            base_url (str, optional): OpenAPI基础URL，默认为OPENAPI_BASE_URL
            token (str, optional): 认证Token，如果提供则直接使用
            app_key (str, optional): 应用Key，用于鉴权
            access_key (str, optional): 访问Key，用于鉴权
        """
        self.base_url = base_url if base_url else self.OPENAPI_BASE_URL
        
        # 优先使用传入的token
        if token:
            self.token = token
        else:
            # 如果没有token，尝试从app_key和access_key生成
            self.app_key = app_key
            self.access_key = access_key
            
            # 验证鉴权信息
            if not self.app_key or not self.access_key:
                raise ValueError("必须提供token或(app_key和access_key)")
            
            # 生成token
            self.token = self.generate_token(self.app_key, self.access_key)
        
        logger.info(f"初始化ES8Discovery，OpenAPI基础URL: {self.base_url}")
    
    def get_cluster_nodes(self, cluster_name: str, timeout: int = 10) -> List[Dict[str, Union[str, int]]]:
        """
        获取集群节点信息
        
        参数:
            cluster_name (str): 集群名称
            timeout (int, optional): 请求超时时间（秒），默认为10秒
            
        返回:
            list: 节点信息列表，每个节点包含ip、name、httpPort和tcpPort信息
            
        异常:
            requests.exceptions.RequestException: 请求异常
            ValueError: 响应解析异常
        """
        url = f"{self.base_url}/clusters/{cluster_name}/nodes"
        logger.info(f"获取集群[{cluster_name}]节点信息，请求URL: {url}")
        
        # 准备请求头
        headers = {}
        if self.token:
            headers["Authorization"] = f"Basic {self.token}"
            logger.debug("使用Token认证访问OpenAPI")
        
        try:
            response = requests.get(url, headers=headers, timeout=timeout)
            response.raise_for_status()  # 如果响应状态码不是200，则抛出异常
            
            data = response.json()
            
            if data.get("code") != 0:
                error_msg = f"获取集群节点信息失败: {data.get('message', '未知错误')}"
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            nodes = data.get("data", [])
            logger.info(f"成功获取集群[{cluster_name}]节点信息，共{len(nodes)}个节点")
            
            return nodes
        except requests.exceptions.RequestException as e:
            logger.error(f"请求OpenAPI获取集群节点信息时发生错误: {str(e)}")
            raise
        except ValueError as e:
            logger.error(f"解析OpenAPI响应时发生错误: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"获取集群节点信息时发生未知错误: {str(e)}")
            raise
    
    def get_node_ips(self, cluster_name: str, timeout: int = 10) -> List[str]:
        """
        获取集群节点IP列表
        
        参数:
            cluster_name (str): 集群名称
            timeout (int, optional): 请求超时时间（秒），默认为10秒
            
        返回:
            list: 节点IP列表
        """
        nodes = self.get_cluster_nodes(cluster_name, timeout)
        return [node["ip"] for node in nodes if "ip" in node]
    
    def get_node_info_map(self, cluster_name: str, timeout: int = 10) -> Dict[str, Dict[str, Union[str, int]]]:
        """
        获取节点信息映射，以IP为键
        
        参数:
            cluster_name (str): 集群名称
            timeout (int, optional): 请求超时时间（秒），默认为10秒
            
        返回:
            dict: 节点信息映射，键为IP，值为节点信息
        """
        nodes = self.get_cluster_nodes(cluster_name, timeout)
        return {node["ip"]: node for node in nodes if "ip" in node}


# 使用示例
if __name__ == "__main__":
    try:
        # 从环境变量读取配置
        app_key = os.getenv('ES8_APP_KEY')
        access_key = os.getenv('ES8_ACCESS_KEY')
        cluster_name = os.getenv('ES8_CLUSTER_NAME', 'shangou_sgrag_default')
        
        # 创建discovery实例
        discovery = ES8Discovery(app_key=app_key, access_key=access_key)
        
        # 获取集群节点信息
        try:
            nodes = discovery.get_cluster_nodes(cluster_name)
            
            # 打印节点信息
            print(f"集群[{cluster_name}]节点信息:")
            for node in nodes:
                print(f"节点IP: {node['ip']}, 主机名: {node['name']}, HTTP端口: {node['httpPort']}, TCP端口: {node['tcpPort']}")
            
            # 获取节点IP列表
            node_ips = discovery.get_node_ips(cluster_name)
            print(f"节点IP列表: {node_ips}")
        except Exception as e:
            print(f"获取集群节点信息失败: {str(e)}")
        
    except Exception as e:
        logger.error(f"示例执行失败: {str(e)}") 