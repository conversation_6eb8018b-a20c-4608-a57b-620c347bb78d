#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import os
import re
import tiktoken
from api.utils.file_utils import get_project_base_directory

def singleton(cls, *args, **kw):
    instances = {}

    def _singleton():
        key = str(cls) + str(os.getpid())
        if key not in instances:
            instances[key] = cls(*args, **kw)
        return instances[key]

    return _singleton


def rmSpace(txt):
    txt = re.sub(r"([^a-z0-9.,\)>]) +([^ ])", r"\1\2", txt, flags=re.IGNORECASE)
    return re.sub(r"([^ ]) +([^a-z0-9.,\(<])", r"\1\2", txt, flags=re.IGNORECASE)


def findMaxDt(fnm):
    m = "1970-01-01 00:00:00"
    try:
        with open(fnm, "r") as f:
            while True:
                line = f.readline()
                if not line:
                    break
                line = line.strip("\n")
                if line == 'nan':
                    continue
                if line > m:
                    m = line
    except Exception:
        pass
    return m

  
def findMaxTm(fnm):
    m = 0
    try:
        with open(fnm, "r") as f:
            while True:
                line = f.readline()
                if not line:
                    break
                line = line.strip("\n")
                if line == 'nan':
                    continue
                if int(line) > m:
                    m = int(line)
    except Exception:
        pass
    return m


# 在导入tiktoken之前设置环境变量
tiktoken_cache_dir = get_project_base_directory()
tiktoken_cache_dir = os.path.join(tiktoken_cache_dir, ".tiktoken")
os.environ["TIKTOKEN_CACHE_DIR"] = tiktoken_cache_dir
os.environ["TIKTOKEN_SKIP_BPE_DOWNLOAD"] = "1"

print(f"设置后TIKTOKEN_CACHE_DIR: {os.environ.get('TIKTOKEN_CACHE_DIR')}")
print(f"- TIKTOKEN_SKIP_BPE_DOWNLOAD = {os.environ.get('TIKTOKEN_SKIP_BPE_DOWNLOAD')}")
print(f"缓存文件是否存在: {os.path.exists(os.path.join(tiktoken_cache_dir, 'cl100k_base.tiktoken'))}")

# 尝试加载本地tiktoken文件
try:
    # 导入所需模块
    import tiktoken
    from tiktoken.load import load_tiktoken_bpe
    
    cl100k_file = os.path.join(tiktoken_cache_dir, 'cl100k_base.tiktoken')
    if os.path.exists(cl100k_file):
        print(f"找到本地tiktoken文件: {cl100k_file}")
        
        # 确保文件权限正确
        try:
            import stat
            os.chmod(cl100k_file, stat.S_IRUSR | stat.S_IWUSR | stat.S_IRGRP | stat.S_IROTH)
            print(f"已修改tiktoken文件权限: {cl100k_file}")
        except Exception as e:
            print(f"修改文件权限失败 (可能不影响使用): {e}")
            
        # 直接使用文件拦截方法 (不尝试标准API)
        try:
            # 定义一个自定义的文件加载函数来覆盖默认的
            def read_local_file(path):
                if path.endswith('cl100k_base.tiktoken'):
                    print(f"拦截请求，使用本地文件: {cl100k_file}")
                    with open(cl100k_file, 'rb') as f:
                        return f.read()
                else:
                    # 默认行为 - 但实际不应该走到这里
                    print(f"警告: 尝试加载非cl100k_base.tiktoken文件: {path}")
                    raise ValueError(f"不支持的文件请求: {path}")
            
            # 修改tiktoken的文件加载函数
            import tiktoken.load
            original_read_file = tiktoken.load.read_file
            tiktoken.load.read_file = read_local_file
            
            # 尝试加载编码器
            encoder = tiktoken.get_encoding("cl100k_base")
            
            # 恢复原始加载函数
            tiktoken.load.read_file = original_read_file
            
            print("✅ 使用文件拦截方法成功加载cl100k_base编码器")
        except Exception as e:
            raise Exception(f"使用文件拦截方法加载失败: {e}")
    else:
        raise FileNotFoundError(f"未找到tiktoken文件: {cl100k_file}")
        
except Exception as e:
    print(f"⚠️ 加载本地tiktoken文件失败: {e}")
    print("⚠️ 切换到SimpleEncoder")
    
    # 备选方案：使用SimpleEncoder
    class SimpleEncoder:
        def encode(self, text):
            return [ord(c) for c in text]
        def decode(self, tokens):
            # 从token ID转回文本
            return "".join(chr(t) if t < 0x110000 else "_" for t in tokens)
    
    encoder = SimpleEncoder()
    print("✅ 已切换到SimpleEncoder作为备选方案")


def num_tokens_from_string(string: str) -> int:
    """Returns the number of tokens in a text string."""
    try:
        return len(encoder.encode(string))
    except Exception:
        return 0


def truncate(string: str, max_len: int) -> str:
    """Returns truncated text if the length of text exceed max_len."""
    return encoder.decode(encoder.encode(string)[:max_len])
