#
#  Copyright 2025 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import logging
import time
from minio import Minio
from minio.error import S3Error
from io import BytesIO
from rag import settings
from rag.utils import singleton


@singleton
class RAGFlowMinio(object):
    # 固定bucket数量
    BUCKET_COUNT = 10
    BUCKET_PREFIX = "rag-storage-"
    
    def __init__(self):
        self.conn = None
        self.__open__()

    def __open__(self):
        try:
            if self.conn:
                self.__close__()
        except Exception:
            pass

        try:
            self.conn = Minio(settings.MINIO["host"],
                              access_key=settings.MINIO["user"],
                              secret_key=settings.MINIO["password"],
                              secure=False
                              )
        except Exception:
            logging.exception(
                "Fail to connect %s " % settings.MINIO["host"])

    def __close__(self):
        del self.conn
        self.conn = None

    def _get_real_bucket(self, bucket):
        """
        将知识库ID映射到固定的bucket
        """
        if bucket == "txtxtxtxt1":  # 保留健康检查bucket
            return bucket
            
        # 使用简单的哈希取模算法
        import hashlib
        hash_value = int(hashlib.md5(str(bucket).encode()).hexdigest(), 16)
        bucket_index = hash_value % self.BUCKET_COUNT
        return f"{self.BUCKET_PREFIX}{bucket_index}"
    
    def _get_real_path(self, bucket, fnm):
        """
        生成真实的存储路径
        """
        if bucket == "txtxtxtxt1":  # 健康检查文件路径不变
            return fnm
        return f"{bucket}/{fnm}"
    
    def health(self):
        bucket, fnm, binary = "txtxtxtxt1", "txtxtxtxt1", b"_t@@@1"
        if not self.conn.bucket_exists(bucket):
            self.conn.make_bucket(bucket)
        r = self.conn.put_object(bucket, fnm,
                                 BytesIO(binary),
                                 len(binary)
                                 )
        return r

    def put(self, bucket, fnm, binary):
        real_bucket = self._get_real_bucket(bucket)
        real_path = self._get_real_path(bucket, fnm)
        
        for _ in range(3):
            try:
                if not self.conn.bucket_exists(real_bucket):
                    self.conn.make_bucket(real_bucket)

                r = self.conn.put_object(real_bucket, real_path,
                                       BytesIO(binary),
                                       len(binary)
                                       )
                return r
            except Exception:
                logging.exception(f"Fail to put {real_bucket}/{real_path}:")
                self.__open__()
                time.sleep(1)

    def rm(self, bucket, fnm):
        real_bucket = self._get_real_bucket(bucket)
        real_path = self._get_real_path(bucket, fnm)
        
        try:
            self.conn.remove_object(real_bucket, real_path)
        except Exception:
            logging.exception(f"Fail to remove {real_bucket}/{real_path}:")

    def get(self, bucket, filename):
        logging.info(f"[Minio连接] 获取文件: bucket={bucket}, filename={filename}")
        real_bucket = self._get_real_bucket(bucket)
        real_path = self._get_real_path(bucket, filename)
        
        for _ in range(1):
            try:
                r = self.conn.get_object(real_bucket, real_path)
                logging.info(f"[Minio连接] 获取文件成功: bucket={real_bucket}, filename={real_path}")
                return r.read()
            except Exception:
                logging.info(f"[Minio连接] 获取文件失败: bucket={real_bucket}, filename={real_path}")
                logging.exception(f"Fail to get {real_bucket}/{real_path}")
                self.__open__()
                time.sleep(1)
        return

    def obj_exist(self, bucket, filename):
        real_bucket = self._get_real_bucket(bucket)
        real_path = self._get_real_path(bucket, filename)
        
        try:
            if not self.conn.bucket_exists(real_bucket):
                return False
            if self.conn.stat_object(real_bucket, real_path):
                return True
            else:
                return False
        except S3Error as e:
            if e.code in ["NoSuchKey", "NoSuchBucket", "ResourceNotFound"]:
                return False
        except Exception:
            logging.exception(f"obj_exist {real_bucket}/{real_path} got exception")
            return False

    def get_presigned_url(self, bucket, fnm, expires):
        real_bucket = self._get_real_bucket(bucket)
        real_path = self._get_real_path(bucket, fnm)
        
        for _ in range(10):
            try:
                return self.conn.get_presigned_url("GET", real_bucket, real_path, expires)
            except Exception:
                logging.exception(f"Fail to get_presigned {real_bucket}/{real_path}:")
                self.__open__()
                time.sleep(1)
        return


MINIO = RAGFlowMinio()

if __name__ == "__main__":
    conn = RAGFlowMinio()
    fnm = "/opt/home/<USER>/docgpt/upload/13/11-408.jpg"
    from PIL import Image

    img = Image.open(fnm)
    buff = BytesIO()
    img.save(buff, format='JPEG')
    print(conn.put("test", "11-408.jpg", buff.getvalue()))
    bts = conn.get("test", "11-408.jpg")
    img = Image.open(BytesIO(bts))
    img.save("test.jpg")
