#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ES8 Python客户端封装模块
提供与ES8集群的连接和基本操作
"""

from elasticsearch import Elasticsearch
import base64
import logging
import ssl
from urllib3.exceptions import InsecureRequestWarning
import urllib3
from typing import List, Dict, Optional, Union
from rag.utils.es8_discovery import ES8Discovery
import os

# 禁用不安全请求的警告
urllib3.disable_warnings(InsecureRequestWarning)

# 配置日志
logger = logging.getLogger('ragflow.es8_client')

class ES8Client:
    """
    ES8 Python客户端封装类
    提供与ES8集群的连接和基本操作
    """
    
    @staticmethod
    def generate_token(app_key, access_key):
        """
        生成认证Token
        
        参数:
            app_key (str): 应用Key
            access_key (str): 访问Key
            
        返回:
            str: 生成的Base64编码Token
        """
        auth_str = f"{app_key}:{access_key}"
        token = base64.b64encode(auth_str.encode()).decode()
        return token
    
    def __init__(self, nodes=None, port=8080, verify_certs=False, use_ssl=False, 
                 cluster_name=None, discovery_url=None, discovery_timeout=10,
                 app_key=None, access_key=None):
        """
        初始化ES8客户端
        
        参数:
            nodes (list): ES集群节点列表，例如 ['127.0.0.1', '127.0.0.2']
            port (int): ES集群对外访问端口，默认8080
            verify_certs (bool): 是否验证SSL证书，默认False
            use_ssl (bool): 是否使用SSL连接，默认False
            cluster_name (str): 集群名称，如果提供则通过OpenAPI获取节点列表
            discovery_url (str): OpenAPI基础URL，用于获取节点列表
            discovery_timeout (int): 获取节点列表的超时时间（秒），默认10秒
            app_key (str): 应用Key，用于鉴权
            access_key (str): 访问Key，用于鉴权
        """
        # 验证鉴权信息
        self.app_key = app_key
        self.access_key = access_key
        
        if not self.app_key or not self.access_key:
            raise ValueError("必须提供app_key和access_key")
        
        # 生成token
        self.token = self.generate_token(self.app_key, self.access_key)
        
        # 先获取节点信息，再建立连接
        if cluster_name:
            logger.info(f"通过OpenAPI获取集群[{cluster_name}]节点列表")
            try:
                # 使用ES8Discovery获取节点列表
                discovery = ES8Discovery(
                    base_url=discovery_url,
                    token=self.token  # 使用同一个token
                )
                nodes = discovery.get_node_ips(cluster_name, discovery_timeout)
                logger.info(f"成功获取集群节点列表: {nodes}")
            except Exception as e:
                logger.error(f"通过OpenAPI获取集群节点列表失败: {str(e)}")
                raise ValueError(f"获取集群节点列表失败，请手动提供节点列表或检查集群名称是否正确: {str(e)}")
        
        # 验证节点列表
        if not nodes or not isinstance(nodes, list) or len(nodes) == 0:
            raise ValueError("节点列表不能为空，请提供nodes参数或指定cluster_name")
        
        self.nodes = nodes
        self.port = port
        
        # 构建节点URL列表
        hosts = [f"http{'s' if use_ssl else ''}://{node}:{port}" for node in nodes]
        
        try:
            # 初始化Elasticsearch客户端配置
            es_config = {
                "hosts": hosts,
                "verify_certs": verify_certs,
                "ssl_show_warn": False,
                "basic_auth": self.token,
                "sniff_on_start": False,
            }
            
            # 初始化Elasticsearch客户端
            self.client = Elasticsearch(**es_config)
            
            # 检查连接是否成功
            if self.client.ping():
                logger.info("成功连接到ES集群")
                logger.info(f"已连接到ES集群：{self.client.info()['version']['number']}")
            else:
                logger.error("连接到ES集群失败")
        except Exception as e:
            logger.error(f"初始化ES客户端时发生错误: {str(e)}")
            raise
    
    def get_client(self):
        """
        获取ES客户端实例
        
        返回:
            Elasticsearch: ES客户端实例
        """
        return self.client
    
    def search(self, index, body=None, **kwargs):
        """
        执行搜索操作
        
        参数:
            index (str): 索引名称
            body (dict): 查询体
            **kwargs: 其他参数
            
        返回:
            dict: 搜索结果
        """
        try:
            # ES8 API更改: body 参数改为 query 参数
            if body:
                kwargs["body"] = body
            return self.client.search(index=index, **kwargs)
        except Exception as e:
            logger.error(f"搜索操作发生错误: {str(e)}")
            raise
    
    def index(self, index, body, id=None, **kwargs):
        """
        索引文档
        
        参数:
            index (str): 索引名称
            body (dict): 文档内容
            id (str): 文档ID，可选
            **kwargs: 其他参数
            
        返回:
            dict: 索引操作结果
        """
        try:
            # ES8 API更改: body 参数改为 document 参数
            return self.client.index(index=index, document=body, id=id, **kwargs)
        except Exception as e:
            logger.error(f"索引操作发生错误: {str(e)}")
            raise
    
    def get(self, index, id, **kwargs):
        """
        获取文档
        
        参数:
            index (str): 索引名称
            id (str): 文档ID
            **kwargs: 其他参数
            
        返回:
            dict: 文档内容
        """
        try:
            return self.client.get(index=index, id=id, **kwargs)
        except Exception as e:
            logger.error(f"获取文档操作发生错误: {str(e)}")
            raise
    
    def update(self, index, id, body, **kwargs):
        """
        更新文档
        
        参数:
            index (str): 索引名称
            id (str): 文档ID
            body (dict): 更新内容
            **kwargs: 其他参数
            
        返回:
            dict: 更新操作结果
        """
        try:
            # ES8 API更改: body 参数改为 doc 参数
            if "doc" in body:
                kwargs["doc"] = body["doc"]
            else:
                kwargs["doc"] = body
            return self.client.update(index=index, id=id, **kwargs)
        except Exception as e:
            logger.error(f"更新文档操作发生错误: {str(e)}")
            raise
    
    def delete(self, index, id, **kwargs):
        """
        删除文档
        
        参数:
            index (str): 索引名称
            id (str): 文档ID
            **kwargs: 其他参数
            
        返回:
            dict: 删除操作结果
        """
        try:
            return self.client.delete(index=index, id=id, **kwargs)
        except Exception as e:
            logger.error(f"删除文档操作发生错误: {str(e)}")
            raise
    
    def bulk(self, body, **kwargs):
        """
        批量操作
        
        参数:
            body (dict/str): 批量操作请求体
            **kwargs: 其他参数
            
        返回:
            dict: 批量操作结果
        """
        try:
            # ES8 API更改: body 参数改为 operations 参数
            kwargs["operations"] = body
            return self.client.bulk(**kwargs)
        except Exception as e:
            logger.error(f"批量操作发生错误: {str(e)}")
            raise
    
    def close(self):
        """
        关闭ES客户端连接
        """
        if hasattr(self, 'client') and self.client:
            self.client.close()
            logger.info("ES客户端连接已关闭")

# 使用示例
if __name__ == "__main__":
    try:
        # 从环境变量读取配置
        cluster_name = os.getenv('ES8_CLUSTER_NAME', 'shangou_sgrag_default')
        app_key = os.getenv('ES8_APP_KEY')
        access_key = os.getenv('ES8_ACCESS_KEY')
        
        # 方式1: 使用环境变量中的app_key和access_key
        es_client = ES8Client(
            cluster_name=cluster_name,
            app_key=app_key,
            access_key=access_key,
        )
        
        # 方式2: 手动提供app_key和access_key
        # es_client = ES8Client(
        #     cluster_name=cluster_name,
        #     app_key=app_key,
        #     access_key=access_key,
        #     port=8080
        # )
        
        # 测试连接
        es = es_client.get_client()
        print(f"集群信息: {es.info()}")
        
        # 完成后关闭连接
        es_client.close()
        
    except Exception as e:
        logger.error(f"ES8客户端示例执行失败: {str(e)}")