#
#  Copyright 2025 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#

import logging
import json
import time
import uuid

import valkey as redis
from rag import settings
from rag.utils import singleton


class RedisMsg:
    def __init__(self, consumer, queue_name, group_name, msg_id, message):
        self.__consumer = consumer
        self.__queue_name = queue_name
        self.__group_name = group_name
        self.__msg_id = msg_id
        self.__message = json.loads(message["message"])

    def ack(self):
        try:
            self.__consumer.xack(self.__queue_name, self.__group_name, self.__msg_id)
            return True
        except Exception as e:
            logging.warning("[EXCEPTION]ack" + str(self.__queue_name) + "||" + str(e))
        return False

    def get_message(self):
        return self.__message

    def get_msg_id(self):
        return self.__msg_id


@singleton
class RedisDB:
    def __init__(self):
        self.REDIS = None
        self.config = settings.REDIS
        self.__open__()

    def __open__(self):
        try:
            self.REDIS = redis.StrictRedis(
                host=self.config["host"].split(":")[0],
                port=int(self.config.get("host", ":6379").split(":")[1]),
                db=int(self.config.get("db", 1)),
                password=self.config.get("password"),
                decode_responses=True,
            )
        except Exception:
            logging.warning("Redis can't be connected.")
        return self.REDIS

    def health(self):
        self.REDIS.ping()
        a, b = "xx", "yy"
        self.REDIS.set(a, b, 3)

        if self.REDIS.get(a) == b:
            return True

    def is_alive(self):
        return self.REDIS is not None

    def exist(self, k):
        if not self.REDIS:
            return
        try:
            return self.REDIS.exists(k)
        except Exception as e:
            logging.warning("RedisDB.exist " + str(k) + " got exception: " + str(e))
            self.__open__()

    def get(self, k):
        if not self.REDIS:
            return
        try:
            return self.REDIS.get(k)
        except Exception as e:
            logging.warning("RedisDB.get " + str(k) + " got exception: " + str(e))
            self.__open__()

    def set_obj(self, k, obj, exp=3600):
        try:
            self.REDIS.set(k, json.dumps(obj, ensure_ascii=False), exp)
            return True
        except Exception as e:
            logging.warning("RedisDB.set_obj " + str(k) + " got exception: " + str(e))
            self.__open__()
        return False

    def set(self, k, v, exp=3600):
        try:
            self.REDIS.set(k, v, exp)
            return True
        except Exception as e:
            logging.warning("RedisDB.set " + str(k) + " got exception: " + str(e))
            self.__open__()
        return False

    def sadd(self, key: str, member: str):
        try:
            self.REDIS.sadd(key, member)
            return True
        except Exception as e:
            logging.warning("RedisDB.sadd " + str(key) + " got exception: " + str(e))
            self.__open__()
        return False

    def srem(self, key: str, member: str):
        try:
            self.REDIS.srem(key, member)
            return True
        except Exception as e:
            logging.warning("RedisDB.srem " + str(key) + " got exception: " + str(e))
            self.__open__()
        return False

    def smembers(self, key: str):
        try:
            res = self.REDIS.smembers(key)
            return res
        except Exception as e:
            logging.warning(
                "RedisDB.smembers " + str(key) + " got exception: " + str(e)
            )
            self.__open__()
        return None

    def zadd(self, key: str, member: str, score: float):
        try:
            self.REDIS.zadd(key, {member: score})
            return True
        except Exception as e:
            logging.warning("RedisDB.zadd " + str(key) + " got exception: " + str(e))
            self.__open__()
        return False

    def zcount(self, key: str, min: float, max: float):
        try:
            res = self.REDIS.zcount(key, min, max)
            return res
        except Exception as e:
            logging.warning("RedisDB.zcount " + str(key) + " got exception: " + str(e))
            self.__open__()
        return 0

    def zpopmin(self, key: str, count: int):
        try:
            res = self.REDIS.zpopmin(key, count)
            return res
        except Exception as e:
            logging.warning("RedisDB.zpopmin " + str(key) + " got exception: " + str(e))
            self.__open__()
        return None

    def zrangebyscore(self, key: str, min: float, max: float):
        try:
            res = self.REDIS.zrangebyscore(key, min, max)
            return res
        except Exception as e:
            logging.warning(
                "RedisDB.zrangebyscore " + str(key) + " got exception: " + str(e)
            )
            self.__open__()
        return None

    def transaction(self, key, value, exp=3600):
        try:
            pipeline = self.REDIS.pipeline(transaction=True)
            pipeline.set(key, value, exp, nx=True)
            pipeline.execute()
            return True
        except Exception as e:
            logging.warning(
                "RedisDB.transaction " + str(key) + " got exception: " + str(e)
            )
            self.__open__()
        return False

    def queue_product(self, queue, message, exp=settings.SVR_QUEUE_RETENTION) -> bool:
        logging.info(f"[Redis队列] 开始发送消息到队列: queue={queue}, exp={exp}")
        
        for attempt in range(3):
            try:
                payload = {"message": json.dumps(message)}
                pipeline = self.REDIS.pipeline()
                pipeline.xadd(queue, payload)
                # pipeline.expire(queue, exp)
                result = pipeline.execute()
                
                msg_id = result[0] if result and result[0] else "unknown"
                logging.info(f"[Redis队列] 成功发送消息到队列: queue={queue}, msg_id={msg_id}, message_id={message.get('id', 'unknown')}")
                return True
            except Exception as e:
                logging.exception(
                    f"[Redis队列] 发送消息到队列失败(尝试{attempt+1}/3): queue={queue}, message_id={message.get('id', 'unknown')}, error={str(e)}"
                )
                
                if attempt == 2:  # 最后一次尝试
                    logging.error(f"[Redis队列] 发送消息到队列失败，已重试3次: queue={queue}, message_id={message.get('id', 'unknown')}")
        
        return False

    def queue_consumer(self, queue_name, group_name, consumer_name, msg_id=b">") -> RedisMsg:
        """https://redis.io/docs/latest/commands/xreadgroup/"""
        # logging.info(f"[Redis队列] 开始从队列消费消息: queue={queue_name}, group={group_name}, consumer={consumer_name}")
        
        try:
            # 检查消费组是否存在
            try:
                group_info = self.REDIS.xinfo_groups(queue_name)
                group_exists = any(e["name"] == group_name for e in group_info)
                logging.debug(f"[Redis队列] 检查消费组: queue={queue_name}, group={group_name}, exists={group_exists}")
                
                if not group_exists:
                    logging.info(f"[Redis队列] 创建消费组: queue={queue_name}, group={group_name}")
                    self.REDIS.xgroup_create(queue_name, group_name, id="0", mkstream=True)
            except Exception as e:
                logging.warning(f"[Redis队列] 检查或创建消费组异常: queue={queue_name}, group={group_name}, error={str(e)}")
                if "NOGROUP" not in str(e) and "key" not in str(e).lower():  # 忽略预期的异常
                    raise
            
            # 从队列读取消息
            args = {
                "groupname": group_name,
                "consumername": consumer_name,
                "count": 1,
                "block": 5,
                "streams": {queue_name: msg_id},
            }
            
            logging.debug(f"[Redis队列] 尝试读取消息: queue={queue_name}, args={args}")
            messages = self.REDIS.xreadgroup(**args)
            
            if not messages:
                logging.debug(f"[Redis队列] 队列中没有新消息: queue={queue_name}")
                return None
                
            stream, element_list = messages[0]
            if not element_list:
                logging.debug(f"[Redis队列] 队列中没有新元素: queue={queue_name}")
                return None
                
            msg_id, payload = element_list[0]
            res = RedisMsg(self.REDIS, queue_name, group_name, msg_id, payload)
            logging.info(f"[Redis队列] 成功从队列消费消息: queue={queue_name}, msg_id={msg_id}")
            return res
            
        except Exception as e:
            if "key" in str(e):
                logging.debug(f"[Redis队列] 队列不存在: queue={queue_name}, error={str(e)}")
                pass
            else:
                logging.exception(
                    f"[Redis队列] 从队列消费消息异常: queue={queue_name}, group={group_name}, error={str(e)}"
                )
        return None

    def get_unacked_iterator(self, queue_name, group_name, consumer_name):
        try:
            group_info = self.REDIS.xinfo_groups(queue_name)
            if not any(e["name"] == group_name for e in group_info):
                return
            current_min = 0
            while True:
                payload = self.queue_consumer(queue_name, group_name, consumer_name, current_min)
                if not payload:
                    return
                current_min = payload.get_msg_id()
                logging.info(f"RedisDB.get_unacked_iterator {consumer_name} msg_id {current_min}")
                yield payload
        except Exception as e:
            if "key" in str(e):
                return
            logging.exception(
                "RedisDB.get_unacked_iterator " + consumer_name + " got exception: "
            )
            self.__open__()

    def queue_info(self, queue, group_name) -> dict | None:
        try:
            groups = self.REDIS.xinfo_groups(queue)
            for group in groups:
                if group["name"] == group_name:
                    return group
        except Exception as e:
            logging.warning(
                "RedisDB.queue_info " + str(queue) + " got exception: " + str(e)
            )
        return None

from rag.utils.squirrel_conn import MTSquirrelDB
# 获取配置,默认使用Redis
USE_SQUIRREL = getattr(settings, 'USE_SQUIRREL', True)

# 根据配置决定使用哪个类
DB = MTSquirrelDB if USE_SQUIRREL else RedisDB

# 保持原有的变量名,但根据配置初始化不同的实现
REDIS_CONN = DB()


class RedisDistributedLock:
    def __init__(self, lock_key, timeout=10):
        self.lock_key = lock_key
        self.lock_value = str(uuid.uuid4())
        self.timeout = timeout

    @staticmethod
    def clean_lock(lock_key):
        REDIS_CONN.REDIS.delete(lock_key)

    def acquire_lock(self):
        end_time = time.time() + self.timeout
        while time.time() < end_time:
            if REDIS_CONN.REDIS.setnx(self.lock_key, self.lock_value):
                return True
            time.sleep(1)
        return False

    def release_lock(self):
        if REDIS_CONN.REDIS.get(self.lock_key) == self.lock_value:
            REDIS_CONN.REDIS.delete(self.lock_key)

    def __enter__(self):
        self.acquire_lock()

    def __exit__(self, exception_type, exception_value, exception_traceback):
        self.release_lock()