# rag/utils/chunk_processor.py

import copy
import logging
import re


def ensure_max_char_limit(parse_id, chunks, max_chars) -> []:
    if not max_chars or max_chars <= 0:
        return chunks

    if parse_id == 'table':
        # 表格不需要分割，直接通过池化获取长文本向量
        result = chunks
    else:
        result = common_max_char_limit(chunks, max_chars)

    return result


def common_max_char_limit(chunks, max_chars) -> []:
    """
    确保所有chunks的content_with_weight不超过指定的字符限制

    Args:
        chunks: 原始chunks列表
        max_chars: 最大字符数限制

    Returns:
        list: 处理后的chunks列表
    """
    if not max_chars or max_chars <= 0:
        return chunks
        
    result = []
    split_count = 0
    
    for chunk in chunks:
        content = chunk.get("content_with_weight", "")
        parts = content_split(content, max_chars)
        if len(parts) > 1:
            split_count += 1

        # 创建新chunks
        for part in parts:
            new_chunk = copy.deepcopy(chunk)
            new_chunk["content_with_weight"] = part
            result.append(new_chunk)

    if split_count > 0:
        logging.warning(f"Split {split_count} chunks due to character limit ({max_chars})")

    return result


def content_split(content, max_chars):
    if len(content) <= max_chars:
        return [content]

    # 记录分割事件
    logging.info(f"Splitting chunk exceeding limit: {len(content)}/{max_chars} chars")

    # 尝试在自然分隔符处分割
    parts = []
    current_part = ""

    # 分割优先级：段落 > 句子 > 强制分割
    for paragraph in content.split("\n"):
        # 如果段落本身就超长
        if len(paragraph) > max_chars:
            # 先处理当前积累的内容
            if current_part:
                parts.append(current_part)
                current_part = ""

            # 尝试按句子分割段落
            sentences = re.split(r'([.!?。；！？])', paragraph)
            for i in range(0, len(sentences), 2):
                sentence = sentences[i]
                delimiter = sentences[i + 1] if i + 1 < len(sentences) else ""
                if len(current_part) + len(sentence) + len(delimiter) > max_chars:
                    if current_part:
                        parts.append(current_part)
                    # 如果单个句子还是太长，强制分割
                    if len(sentence) > max_chars:
                        for j in range(0, len(sentence), max_chars):
                            parts.append(sentence[j:j + max_chars])
                    else:
                        current_part = sentence + delimiter
                else:
                    current_part += sentence + delimiter
        # 正常长度段落处理
        elif len(current_part) + len(paragraph) + 1 > max_chars:
            parts.append(current_part)
            current_part = paragraph
        else:
            if current_part:
                current_part += "\n" + paragraph
            else:
                current_part = paragraph

    # 处理剩余内容
    if current_part:
        parts.append(current_part)

    return parts
