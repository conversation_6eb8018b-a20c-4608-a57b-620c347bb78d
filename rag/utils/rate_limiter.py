import asyncio
import logging
import time
from typing import Optional, Dict, Any

from rag.utils.redis_conn import REDIS_CONN
from rag.utils.squirrel_conn import MT_SQUIRREL_CONN


class RedisRateLimiter:
    """
    基于Redis的分钟级RPM限流器
    使用Redis的有序集合(Sorted Set)实现滑动窗口限流
    每次请求时，将当前时间戳作为分数添加到有序集合中
    然后删除一分钟前的所有请求记录
    最后检查当前窗口内的请求数是否超过了限制
    """

    def __init__(self, prefix: str = "rate_limit:", expiry: int = 70):
        """
        初始化限流器
        Args:
            prefix: Redis键的前缀
            expiry: 限流记录的过期时间(秒)，默认70秒(比窗口稍长一些)
        """
        self.prefix = prefix
        self.expiry = expiry
        self._limiters: Dict[str, Dict[str, Any]] = {}

    def _get_key(self, resource_id: str) -> str:
        """
        获取资源的Redis键
        Args:
            resource_id: 资源ID，如模型名称
        Returns:
            Redis键
        """
        return f"{self.prefix}{resource_id}"

    def register_limiter(self, resource_id: str, rpm: int) -> None:
        """
        注册一个资源的限流器
        Args:
            resource_id: 资源ID，如模型名称
            rpm: 每分钟最大请求数
        """
        self._limiters[resource_id] = {
            "rpm": rpm,
            "window": 60  # 窗口大小，单位秒
        }
        logging.info(f"已注册限流器: resource_id={resource_id}, rpm={rpm}")

    def get_limiter_info(self, resource_id: str) -> Optional[Dict[str, Any]]:
        """
        获取资源的限流器信息
        Args:
            resource_id: 资源ID，如模型名称
        Returns:
            限流器信息，如果不存在则返回None
        """
        return self._limiters.get(resource_id)

    def acquire(self, resource_id: str, count: int = 1) -> bool:
        """
        尝试获取资源的使用权限
        Args:
            resource_id: 资源ID，如模型名称
            count: 请求数量，默认为1
        Returns:
            是否获取成功
        """
        limiter_info = self.get_limiter_info(resource_id)
        if not limiter_info:
            logging.warning(f"未找到资源的限流器: resource_id={resource_id}")
            return True  # 如果没有注册限流器，默认允许

        rpm = limiter_info["rpm"]
        window = limiter_info["window"]

        # 当前时间戳(秒)
        now = time.time()
        # 一分钟前的时间戳
        min_time = now - window

        key = REDIS_CONN.build_category_key(self._get_key(resource_id))

        try:
            # 使用Redis管道执行原子操作
            pipeline = REDIS_CONN.pipeline()

            # 1. 添加当前请求记录
            for i in range(count):
                # 使用微小的时间差来区分同一时刻的多个请求
                score = now + (i * 0.001)
                member = f"{score}:{i}"
                pipeline.zadd(key, member, score)

            # 2. 删除窗口外的请求记录
            pipeline.zremrangebyscore(key, 0, min_time)

            # 3. 获取当前窗口内的请求数
            pipeline.zcard(key)

            # 4. 设置过期时间
            pipeline.expire(key, self.expiry)

            # 执行管道
            results = pipeline.execute()

            # 获取当前窗口内的请求数
            current_count = results[2]

            # 检查是否超过限制
            if current_count > rpm:
                logging.warning(f"资源请求超过限制: resource_id={resource_id}, current={current_count}, limit={rpm}")
                return False

            remaining = rpm - current_count
            logging.info(f"资源请求成功: resource_id={resource_id}, remaining={remaining}")
            return True

        except Exception as e:
            logging.exception(f"限流器操作异常: resource_id={resource_id}, error={str(e)}")
            return True  # 出错时默认允许，避免阻塞业务

    def wait_if_needed(self, resource_id: str, count: int = 1) -> None:
        """
        如果超过限制，等待直到可以获取资源

        实现阻塞等待功能，当请求超过限制时，会计算需要等待的时间，然后进行睡眠
        Args:
            resource_id: 资源ID，如模型名称
            count: 请求数量，默认为1
        """
        limiter_info = self.get_limiter_info(resource_id)
        if not limiter_info:
            logging.warning(f"未找到资源的限流器: resource_id={resource_id}")
            return  # 如果没有注册限流器，直接返回

        rpm = limiter_info["rpm"]
        window = limiter_info["window"]
        key = self._get_key(resource_id)

        # 首先尝试获取资源
        if self.acquire(resource_id, count):
            return  # 如果成功获取资源，直接返回

        # 如果获取失败，计算需要等待的时间
        try:
            # 获取当前窗口内的请求记录
            now = time.time()
            min_time = now - window

            # 获取所有在窗口内的请求记录，按时间排序
            records = REDIS_CONN.zrangebyscore(key, min_time, '+inf')

            if not records:
                # 如果没有记录，可能是因为刚刚被清理，直接返回
                return

            # 计算需要等待多久才能使窗口内的请求数量低于限制
            # 需要等待的请求数 = 当前请求数 + 本次请求数 - 允许的请求数
            current_count = len(records)
            requests_to_wait = current_count + count - rpm

            if requests_to_wait <= 0:
                # 理论上不应该出现这种情况，因为前面的acquire已经失败了
                # 但为了健壮性，还是加上这个检查
                return

            # 找出最早的N个请求，等待它们过期
            earliest_requests = records[:requests_to_wait]
            if not earliest_requests:
                # 如果没有足够的请求记录，等待一小段时间再重试
                time.sleep(0.1)
                return self.wait_if_needed(resource_id, count)

            # 计算最晚的那个"最早请求"的过期时间
            _, oldest_score = earliest_requests[-1]
            wait_until = oldest_score + window
            wait_time = wait_until - now

            # 确保等待时间是合理的
            if wait_time <= 0:
                # 如果等待时间已经过了，可能是因为时间不同步，直接重试
                return self.wait_if_needed(resource_id, count)

            if wait_time > window:
                # 如果等待时间超过了窗口大小，可能是计算错误，限制最大等待时间
                wait_time = 1.0  # 等待1秒再重试

            logging.info(f"资源请求超过限制，等待 {wait_time:.2f} 秒: resource_id={resource_id}, count={count}")
            time.sleep(wait_time)

            # 等待后再次尝试获取资源
            return self.wait_if_needed(resource_id, count)

        except Exception as e:
            logging.exception(f"计算等待时间异常: resource_id={resource_id}, error={str(e)}")
            # 出错时等待一小段时间再重试
            time.sleep(0.5)
            return self.wait_if_needed(resource_id, count)

    def get_current_usage(self, resource_id: str) -> Dict[str, Any]:
        """
        获取资源的当前使用情况
        Args:
            resource_id: 资源ID，如模型名称
        Returns:
            使用情况，包括当前请求数、限制等
        """
        limiter_info = self.get_limiter_info(resource_id)
        if not limiter_info:
            return {"resource_id": resource_id, "registered": False}

        rpm = limiter_info["rpm"]
        window = limiter_info["window"]

        # 当前时间戳(秒)
        now = time.time()
        # 一分钟前的时间戳
        min_time = now - window

        key = self._get_key(resource_id)

        try:
            # 删除窗口外的请求记录
            REDIS_CONN.REDIS.zremrangebyscore(key, 0, min_time)

            # 获取当前窗口内的请求数
            current_count = REDIS_CONN.REDIS.zcard(key)

            return {
                "resource_id": resource_id,
                "registered": True,
                "rpm": rpm,
                "window": window,
                "current_count": current_count,
                "usage_percent": (current_count / rpm) * 100 if rpm > 0 else 0
            }

        except Exception as e:
            logging.exception(f"获取使用情况异常: resource_id={resource_id}, error={str(e)}")
            return {
                "resource_id": resource_id,
                "registered": True,
                "rpm": rpm,
                "window": window,
                "current_count": 0,
                "error": str(e)
            }


# 创建全局实例
REDIS_RATE_LIMITER = RedisRateLimiter()

if __name__ == '__main__':
    redis_conn = MT_SQUIRREL_CONN
    print(redis_conn.build_category_key("rag_rate_limit"))
