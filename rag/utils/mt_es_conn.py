import logging
import json
import time
import os
import copy
import re
from elasticsearch import NotFoundError
from elasticsearch_dsl import UpdateByQuery, Q, Search, Index
from rag import settings
from rag.settings import TAG_FLD, PAGERANK_FLD
from rag.utils import singleton
from api.utils.file_utils import get_project_base_directory
from api.utils.config_loader import get_es_config
from rag.utils.doc_store_conn import DocStoreConnection, MatchExpr, OrderByExpr, MatchTextExpr, MatchDenseExpr, FusionExpr
from rag.nlp import is_english, rag_tokenizer
from rag.utils.es8_client import ES8Client

ATTEMPT_TIME = 2

logger = logging.getLogger('ragflow.mt_es_conn')


@singleton
class MTESConnection(DocStoreConnection):
    def __init__(self):
        self.info = {}
        
        # 使用新的配置加载机制获取ES配置
        es_config = get_es_config()
        
        # 从配置读取参数
        cluster_name = es_config.get("cluster_name")
        app_key = es_config.get("app_key")
        access_key = es_config.get("access_key")
        port = int(es_config.get("port", 8080))
        use_ssl = es_config.get("use_ssl", False)
        verify_certs = es_config.get("verify_certs", False)
        discovery_url = es_config.get("discovery_url")
        
        # 记录配置和连接信息
        logger.info(f"使用美团ES8集群: {cluster_name}")
        logger.info(f"配置详情: discovery_url={discovery_url}, port={port}, use_ssl={use_ssl}")
        
        # 初始化ES8Client
        for _ in range(ATTEMPT_TIME):
            try:
                self.es8_client = ES8Client(
                    cluster_name=cluster_name,
                    app_key=app_key,
                    access_key=access_key,
                    port=port,
                    use_ssl=use_ssl,
                    verify_certs=verify_certs,
                    discovery_url=discovery_url
                )
                self.es = self.es8_client.get_client()
                
                if self.es and self.es.ping():
                    self.info = self.es.info()
                    break
            except Exception as e:
                logger.warning(f"{str(e)}. 等待ES8集群就绪...")
                time.sleep(5)
        
        if not self.es or not self.es.ping():
            msg = f"ES8集群不可用，请检查配置和网络。"
            logger.error(msg)
            raise Exception(msg)
        
        # 检查ES版本
        v = self.info.get("version", {"number": "8.11.3"})
        v = v["number"].split(".")[0]
        if int(v) < 8:
            msg = f"Elasticsearch版本必须大于等于8，当前版本：{v}"
            logger.error(msg)
            raise Exception(msg)
        
        # 加载映射文件
        fp_mapping = os.path.join(get_project_base_directory(), "conf", "mapping.json")
        if not os.path.exists(fp_mapping):
            msg = f"找不到Elasticsearch映射文件：{fp_mapping}"
            logger.error(msg)
            raise Exception(msg)
        
        self.mapping = json.load(open(fp_mapping, "r"))
        logger.info(f"成功连接到ES8集群：{self.info.get('cluster_name')}")
    
    def dbType(self) -> str:
        return "elasticsearch"
    
    def health(self) -> dict:
        health_dict = dict(self.es.cluster.health())
        health_dict["type"] = "elasticsearch"
        return health_dict
    
    # 以下是实现DocStoreConnection接口的方法
    def createIdx(self, indexName: str, knowledgebaseId: str, vectorSize: int):
        if self.indexExist(indexName, knowledgebaseId):
            return True
        try:
            from elasticsearch.client import IndicesClient
            return IndicesClient(self.es).create(index=indexName,
                                            settings=self.mapping["settings"],
                                            mappings=self.mapping["mappings"])
        except Exception:
            logger.exception("MTESConnection.createIndex error %s" % (indexName))

    def deleteIdx(self, indexName: str, knowledgebaseId: str):
        if len(knowledgebaseId) > 0:
            # The index need to be alive after any kb deletion since all kb under this tenant are in one index.
            return
        try:
            self.es.indices.delete(index=indexName, allow_no_indices=True)
        except NotFoundError:
            pass
        except Exception:
            logger.exception("MTESConnection.deleteIdx error %s" % (indexName))

    def indexExist(self, indexName: str, knowledgebaseId: str = None) -> bool:
        s = Index(indexName, self.es)
        for i in range(ATTEMPT_TIME):
            try:
                return s.exists()
            except Exception as e:
                logger.exception("MTESConnection.indexExist got exception")
                if str(e).find("Timeout") > 0 or str(e).find("Conflict") > 0:
                    continue
                break
        return False

    def insert(self, documents: list[dict], indexName: str, knowledgebaseId: str = None) -> list[str]:
        # Refers to https://www.elastic.co/guide/en/elasticsearch/reference/current/docs-bulk.html
        operations = []
        for d in documents:
            assert "_id" not in d
            assert "id" in d
            d_copy = copy.deepcopy(d)
            meta_id = d_copy.pop("id", "")
            operations.append(
                {"index": {"_index": indexName, "_id": meta_id}})
            operations.append(d_copy)

        res = []
        for _ in range(ATTEMPT_TIME):
            try:
                res = []
                r = self.es.bulk(index=(indexName), operations=operations,
                                refresh=False, timeout="60s")
                if str(r["errors"]).lower() == "false":
                    return res

                for item in r["items"]:
                    for action in ["create", "delete", "index", "update"]:
                        if action in item and "error" in item[action]:
                            res.append(str(item[action]["_id"]) + ":" + str(item[action]["error"]))
                return res
            except Exception as e:
                res.append(str(e))
                logger.warning("MTESConnection.insert got exception: " + str(e))
                res = []
                time.sleep(3)
        return res

    def delete(self, condition: dict, indexName: str, knowledgebaseId: str) -> int:
        qry = None
        assert "_id" not in condition
        if "id" in condition:
            chunk_ids = condition["id"]
            if not isinstance(chunk_ids, list):
                chunk_ids = [chunk_ids]
            qry = Q("ids", values=chunk_ids)
        else:
            qry = Q("bool")
            for k, v in condition.items():
                if k == "exists":
                    qry.filter.append(Q("exists", field=v))

                elif k == "must_not":
                    if isinstance(v, dict):
                        for kk, vv in v.items():
                            if kk == "exists":
                                qry.must_not.append(Q("exists", field=vv))

                elif isinstance(v, list):
                    qry.must.append(Q("terms", **{k: v}))
                elif isinstance(v, str) or isinstance(v, int):
                    qry.must.append(Q("term", **{k: v}))
                else:
                    raise Exception("Condition value must be int, str or list.")
        logger.debug("MTESConnection.delete query: " + json.dumps(qry.to_dict()))
        for _ in range(ATTEMPT_TIME):
            try:
                res = self.es.delete_by_query(
                    index=indexName,
                    body=Search().query(qry).to_dict(),
                    refresh=True)
                return res["deleted"]
            except Exception as e:
                logger.warning("MTESConnection.delete got exception: " + str(e))
                if re.search(r"(timeout|connection)", str(e).lower()):
                    time.sleep(3)
                    continue
                if re.search(r"(not_found)", str(e), re.IGNORECASE):
                    return 0
        return 0

    def update(self, condition: dict, newValue: dict, indexName: str, knowledgebaseId: str) -> bool:
        doc = copy.deepcopy(newValue)
        doc.pop("id", None)
        if "id" in condition and isinstance(condition["id"], str):
            # update specific single document
            chunkId = condition["id"]
            for i in range(ATTEMPT_TIME):
                try:
                    self.es.update(index=indexName, id=chunkId, doc=doc)
                    return True
                except Exception as e:
                    logger.exception(
                        f"MTESConnection.update(index={indexName}, id={id}, doc={json.dumps(condition, ensure_ascii=False)}) got exception")
                    if re.search(r"(timeout|connection)", str(e).lower()):
                        continue
                    break
            return False

        # update unspecific maybe-multiple documents
        bqry = Q("bool")
        for k, v in condition.items():
            if not isinstance(k, str) or not v:
                continue
            if k == "exists":
                bqry.filter.append(Q("exists", field=v))
                continue
            if isinstance(v, list):
                bqry.filter.append(Q("terms", **{k: v}))
            elif isinstance(v, str) or isinstance(v, int):
                bqry.filter.append(Q("term", **{k: v}))
            else:
                raise Exception(
                    f"Condition `{str(k)}={str(v)}` value type is {str(type(v))}, expected to be int, str or list.")
        scripts = []
        params = {}
        for k, v in newValue.items():
            if k == "remove":
                if isinstance(v, str):
                    scripts.append(f"ctx._source.remove('{v}');")
                if isinstance(v, dict):
                    for kk, vv in v.items():
                        scripts.append(f"int i=ctx._source.{kk}.indexOf(params.p_{kk});ctx._source.{kk}.remove(i);")
                        params[f"p_{kk}"] = vv
                continue
            if k == "add":
                if isinstance(v, dict):
                    for kk, vv in v.items():
                        scripts.append(f"ctx._source.{kk}.add(params.pp_{kk});")
                        params[f"pp_{kk}"] = vv.strip()
                continue
            if (not isinstance(k, str) or not v) and k != "available_int":
                continue
            if isinstance(v, str):
                v = re.sub(r"(['\n\r]|\\.)", " ", v)
                params[f"pp_{k}"] = v
                scripts.append(f"ctx._source.{k}=params.pp_{k};")
            elif isinstance(v, int) or isinstance(v, float):
                scripts.append(f"ctx._source.{k}={v};")
            elif isinstance(v, list):
                scripts.append(f"ctx._source.{k}=params.pp_{k};")
                params[f"pp_{k}"] = json.dumps(v, ensure_ascii=False)
            else:
                raise Exception(
                    f"newValue `{str(k)}={str(v)}` value type is {str(type(v))}, expected to be int, str.")
        ubq = UpdateByQuery(
            index=indexName).using(
            self.es).query(bqry)
        ubq = ubq.script(source="".join(scripts), params=params)
        ubq = ubq.params(refresh=True)
        ubq = ubq.params(slices=5)
        ubq = ubq.params(conflicts="proceed")

        for _ in range(ATTEMPT_TIME):
            try:
                _ = ubq.execute()
                return True
            except Exception as e:
                logger.error("MTESConnection.update got exception: " + str(e) + "\n".join(scripts))
                if re.search(r"(timeout|connection|conflict)", str(e).lower()):
                    continue
                break
        return False

    def _buildQueryBody(self, condition=None, matchExprs=None, knowledgebaseIds=None, rank_feature=None) -> Q:
        bqry = Q()
        op = "must"
        if matchExprs:
            for qe in matchExprs:
                if isinstance(qe, MatchDenseExpr):
                    bqry = bqry & self._buildDenseQuery(qe, rank_feature)
                elif isinstance(qe, MatchTextExpr):
                    bqry = bqry & self._buildTextQuery(qe)
                elif isinstance(qe, FusionExpr):
                    bqry = bqry & self._buildFusionQuery(qe)
        if knowledgebaseIds and len(knowledgebaseIds) > 0:
            if len(knowledgebaseIds) == 1:
                q = Q("term", kb_id=knowledgebaseIds[0])
            else:
                q = Q()
                for kb_id in knowledgebaseIds:
                    q = q | Q("term", kb_id=kb_id)
            if op == "must":
                bqry = bqry & q
            elif op == "should":
                bqry = bqry | q
            elif op == "must_not":
                bqry = bqry & ~q
        if condition:
            for k, values in condition.items():
                if values and len(values) > 0:
                    q = Q()
                    if k.startswith("^") and k.endswith("$"):
                        k = k[1:-1]
                        for v in values:
                            if str(v) == "0":
                                # special process for filtering field not exists.
                                q = q | ~Q("exists", field=k)
                            else:
                                q = q | Q("term", **{k: v})
                        if op == "must":
                            bqry = bqry & q
                        elif op == "should":
                            bqry = bqry | q
                        elif op == "must_not":
                            bqry = bqry & ~q
                    elif k == "$or" and isinstance(values, list):
                        q = Q()
                        for and_cond in values:
                            and_q = Q()
                            for k1, v1 in and_cond.items():
                                and_q = and_q & Q("terms", **{k1: v1})
                            q = q | and_q
                        bqry = bqry & q
                    else:
                        range_op = ""
                        kk = k
                        if k.startswith(">="):
                            range_op = "gte"
                            kk = k[2:]
                        elif k.startswith(">"):
                            range_op = "gt"
                            kk = k[1:]
                        elif k.startswith("<="):
                            range_op = "lte"
                            kk = k[2:]
                        elif k.startswith("<"):
                            range_op = "lt"
                            kk = k[1:]
                        if range_op:
                            q = Q("range", **{kk: {range_op: values[0]}})
                            if op == "must":
                                bqry = bqry & q
                            elif op == "should":
                                bqry = bqry | q
                            elif op == "must_not":
                                bqry = bqry & ~q
                        else:
                            try:
                                if k == "kb_id":
                                    if len(values) == 1:
                                        q = Q("term", kb_id=values[0])
                                    else:
                                        q = Q()
                                        for kb_id in values:
                                            q = q | Q("term", kb_id=kb_id)
                                else:
                                    q = Q("terms", **{k: values})
                                
                                if op == "must":
                                    bqry = bqry & q
                                elif op == "should":
                                    bqry = bqry | q
                                elif op == "must_not":
                                    bqry = bqry & ~q
                            except:
                                logger.exception("MTESConnection._buildQueryBody terms error %s %s" % (k, str(values)))
        return bqry

    def _buildTextQuery(self, qe: MatchTextExpr) -> Q:
        q = Q()
        if hasattr(qe, 'fields') and qe.fields:
            fld = qe.fields[0] if isinstance(qe.fields, list) else qe.fields
        elif hasattr(qe, 'field'):
            fld = qe.field
        else:
            logger.warning("MatchTextExpr没有field或fields属性")
            return q
            
        val = qe.value
        if val and len(val.strip()) > 0:
            # check language of the query
            if fld == "content":
                english = is_english(val)
                fld = "content_en" if english else "content_zh"
            q = Q("match", **{fld: val})
        return q

    def _buildDenseQuery(self, qe: MatchDenseExpr, rank_feature: dict = None) -> Q:
        q = Q()
        # print("qe.value->", qe.value)
        if qe.value is not None and len(qe.value) > 0:
            if rank_feature:
                vector_fld = rank_feature.get("vector_fld", qe.field)
                raw_weight = rank_feature.get("raw_weight", 1.0)
                pr_weight = rank_feature.get("pr_weight", 1.0)
                tag_weight = rank_feature.get("tag_weight", 1.0)
                tag_list = rank_feature.get("tag_list", [])
                knn = {
                    vector_fld: {
                        "vector": qe.value,
                        "k": 10000
                    }
                }
                rank_features = {
                    PAGERANK_FLD: {
                        "saturation": {
                            "pivot": 1
                        },
                        "weight": pr_weight
                    }
                }
                if len(tag_list) > 0:
                    rank_features[TAG_FLD] = {
                        "saturation": {
                            "pivot": 1
                        },
                        "weight": tag_weight
                    }
                rrf = {
                    "rank_features": rank_features,
                    "window_size": 10000,
                    "rank_indices": {
                        "knn": {
                            "top_k": 10000,
                            "weight": raw_weight
                        }
                    }
                }
                q = Q({
                    "nested": {
                        "path": "rank",
                        "score_mode": "sum",
                        "query": {
                            "rank": {
                                "rrf": rrf,
                                "knn": knn
                            }
                        }
                    }})
            else:
                q = self._buildKnnQuery(qe)
        return q

    def _buildKnnQuery(self, qe: MatchDenseExpr) -> Q:
        q = Q()
        if qe.value is not None and len(qe.value) > 0:
            # remove when ES fixed inner_hits bug of knn/query
            q = Q({
                "knn": {
                    qe.field: {
                        "vector": qe.value,
                        "k": 10000
                    }
                }
            })
        return q

    def _buildFusionQuery(self, qe: FusionExpr) -> Q:
        q = Q()
        query = qe.query
        field = qe.field
        weight = qe.weight
        kw_field = qe.kw_field
        kw_weight = qe.kw_weight
        fts_weight = qe.fts_weight
        if query is not None and len(query.strip()) > 0:
            # build text search query
            english = is_english(query)
            fts_field = "content_en" if english else "content_zh"
            fts_q = Q()
            if fts_weight > 0:
                fts_q = Q("match", **{fts_field: {"query": query, "boost": fts_weight}})

            # build kw query
            kw_q = Q()
            if kw_field and len(kw_field) > 0 and kw_weight > 0:
                kw_q = Q("match", **{kw_field: {"query": query, "boost": kw_weight}})

            # build vector query
            vector_q = Q()
            if weight > 0 and field and len(field) > 0 and qe.vector is not None and len(qe.vector) > 0:
                vector_q = Q({
                    "script_score": {
                        "query": {"match_all": {}},
                        "script": {
                            "source": f"(cosineSimilarity(params.query_vector, '{field}') + 1.0) * params.weight",
                            "params": {"query_vector": qe.vector, "weight": weight}
                        }
                    }
                })
            # merge
            q = fts_q | kw_q | vector_q

        return q

    def search(
            self, selectFields: list[str],
            highlightFields: list[str],
            condition: dict,
            matchExprs: list[MatchExpr],
            orderBy: OrderByExpr,
            offset: int,
            limit: int,
            indexNames: str | list[str],
            knowledgebaseIds: list[str],
            aggFields: list[str] = [],
            rank_feature: dict | None = None
    ):
        """
        Search documents from index.
        https://elasticsearch-dsl.readthedocs.io/en/latest/search_dsl.html
        https://www.elastic.co/guide/en/elasticsearch/reference/current/search-search.html
        :param selectFields: Fields to return
        :param highlightFields: Field need highlight.
        :param condition: Filter condition, key is field, value is list.
        :param matchExprs: Match expressions.
        :param offset: From offset, start from 0.
        :param limit: Return count.
        :param indexNames: Search index name(s).
        :param knowledgebaseIds: Knowledge base ids.
        :param aggFields: Aggregate fields.
        :param orderBy: Order by
        :param rank_feature: Rank feature
        :return: The search result.
        """
        if isinstance(indexNames, str):
            indexNames = indexNames.split(",")
        assert isinstance(indexNames, list) and len(indexNames) > 0
        assert "_id" not in condition

        bqry = Q("bool", must=[])
        condition["kb_id"] = knowledgebaseIds
        for k, v in condition.items():
            if k == "available_int":
                if v == 0:
                    bqry.filter.append(Q("range", available_int={"lt": 1}))
                else:
                    bqry.filter.append(
                        Q("bool", must_not=Q("range", available_int={"lt": 1})))
                continue
            if not v:
                continue
            if isinstance(v, list):
                bqry.filter.append(Q("terms", **{k: v}))
            elif isinstance(v, str) or isinstance(v, int):
                bqry.filter.append(Q("term", **{k: v}))
            else:
                raise Exception(
                    f"Condition `{str(k)}={str(v)}` value type is {str(type(v))}, expected to be int, str or list.")

        s = Search(using=self.es)
        vector_similarity_weight = 0.5
        for m in matchExprs:
            if isinstance(m, FusionExpr) and m.method == "weighted_sum" and "weights" in m.fusion_params:
                assert len(matchExprs) == 3 and isinstance(matchExprs[0], MatchTextExpr) and isinstance(matchExprs[1],
                                                                                                        MatchDenseExpr) and isinstance(
                    matchExprs[2], FusionExpr)
                weights = m.fusion_params["weights"]
                vector_similarity_weight = float(weights.split(",")[1])
        for m in matchExprs:
            if isinstance(m, MatchTextExpr):
                minimum_should_match = m.extra_options.get("minimum_should_match", 0.0)
                if isinstance(minimum_should_match, float):
                    minimum_should_match = str(int(minimum_should_match * 100)) + "%"
                bqry.must.append(Q("query_string", fields=m.fields,
                                   type="best_fields", query=m.matching_text,
                                   minimum_should_match=minimum_should_match,
                                   boost=1))
                bqry.boost = 1.0 - vector_similarity_weight

            elif isinstance(m, MatchDenseExpr):
                assert (bqry is not None)
                similarity = 0.0
                if "similarity" in m.extra_options:
                    similarity = m.extra_options["similarity"]
                s = s.knn(m.vector_column_name,
                          m.topn,
                          m.topn * 2,
                          query_vector=list(m.embedding_data),
                          filter=bqry.to_dict(),
                          similarity=similarity,
                          )

        if bqry and rank_feature:
            for fld, sc in rank_feature.items():
                if fld != PAGERANK_FLD:
                    fld = f"{TAG_FLD}.{fld}"
                bqry.should.append(Q("rank_feature", field=fld, linear={}, boost=sc))

        if bqry:
            s = s.query(bqry)
        for field in highlightFields:
            s = s.highlight(field)

        if orderBy:
            orders = list()
            for field, order in orderBy.fields:
                order = "asc" if order == 0 else "desc"
                if field in ["page_num_int", "top_int"]:
                    order_info = {"order": order, "unmapped_type": "float",
                                  "mode": "avg", "numeric_type": "double"}
                elif field.endswith("_int") or field.endswith("_flt"):
                    order_info = {"order": order, "unmapped_type": "float"}
                else:
                    order_info = {"order": order, "unmapped_type": "text"}
                orders.append({field: order_info})
            s = s.sort(*orders)

        for fld in aggFields:
            s.aggs.bucket(f'aggs_{fld}', 'terms', field=fld, size=1000000)

        if limit > 0:
            s = s[offset:offset + limit]
        q = s.to_dict()
        logger.debug(f"MTESConnection.search {str(indexNames)} query: " + json.dumps(q))

        for i in range(ATTEMPT_TIME):
            try:
                res = self.es.search(index=indexNames,
                                    body=q,
                                    timeout="600s",
                                    track_total_hits=True,
                                    _source=True)
                if str(res.get("timed_out", "")).lower() == "true":
                    raise Exception("Es Timeout.")
                logger.debug(f"MTESConnection.search {str(indexNames)} res: " + str(res))
                return res
            except Exception as e:
                logger.exception(f"MTESConnection.search {str(indexNames)} query: " + str(q))
                if str(e).find("Timeout") > 0:
                    continue
                raise e
        logger.error("MTESConnection.search timeout for 3 times!")
        raise Exception("MTESConnection.search timeout.")
    
    def getTotal(self, result) -> int:
        """
        Get total hit count from search result.
        :param result: The search result.
        :return: The hit total count.
        """
        total = 0
        if result and "hits" in result and "total" in result["hits"]:
            if isinstance(result["hits"]["total"], dict) and "value" in result["hits"]["total"]:
                total = result["hits"]["total"]["value"]
            else:
                total = result["hits"]["total"]
        return total

    def get(self, chunkId: str, indexName: str, knowledgebaseIds: list[str]) -> dict | None:
        """
        获取指定ID的文档
        :param chunkId: 文档ID
        :param indexName: 索引名称
        :param knowledgebaseIds: 知识库ID列表
        :return: 文档内容，如果不存在返回None
        """
        for i in range(ATTEMPT_TIME):
            try:
                res = self.es.get(index=(indexName),
                                id=chunkId, source=True, )
                if str(res.get("timed_out", "")).lower() == "true":
                    raise Exception("Es Timeout.")
                chunk = res["_source"]
                chunk["id"] = chunkId
                return chunk
            except NotFoundError:
                return None
            except Exception as e:
                logger.exception(f"MTESConnection.get({chunkId}) got exception")
                if str(e).find("Timeout") > 0:
                    continue
                raise e
        logger.error("MTESConnection.get timeout for 3 times!")
        raise Exception("MTESConnection.get timeout.")

    def getChunkIds(self, result):
        """
        从搜索结果中获取文档ID列表
        :param result: 搜索结果
        :return: 文档ID列表
        """
        return [d["_id"] for d in result["hits"]["hits"]] if "hits" in result and "hits" in result["hits"] else []

    def __getSource(self, result):
        """
        从搜索结果中获取文档源内容列表
        :param result: 搜索结果
        :return: 文档源内容列表
        """
        rr = []
        for d in result["hits"]["hits"]:
            d["_source"]["id"] = d["_id"]
            d["_source"]["_score"] = d["_score"]
            rr.append(d["_source"])
        return rr

    def getFields(self, result, fields: list[str]) -> dict[str, dict]:
        """
        从搜索结果中获取指定字段的内容
        :param result: 搜索结果
        :param fields: 字段列表
        :return: 以文档ID为键，字段内容为值的字典
        """
        res_fields = {}
        if not fields:
            return {}
        for d in self.__getSource(result):
            m = {n: d.get(n) for n in fields if d.get(n) is not None}
            for n, v in m.items():
                if isinstance(v, list):
                    m[n] = v
                    continue
                if not isinstance(v, str):
                    m[n] = str(m[n])

            if m:
                res_fields[d["id"]] = m
        return res_fields

    def getHighlight(self, result, keywords: list[str], fieldnm: str):
        """
        从搜索结果中获取高亮内容
        :param result: 搜索结果
        :param keywords: 关键词列表
        :param fieldnm: 字段名
        :return: 以文档ID为键，高亮内容为值的字典
        """
        ans = {}
        for d in result["hits"]["hits"]:
            hlts = d.get("highlight")
            if not hlts:
                continue
            txt = "...".join([a for a in list(hlts.items())[0][1]])
            if not is_english(txt.split()):
                ans[d["_id"]] = txt
                continue

            txt = d["_source"][fieldnm]
            txt = re.sub(r"[\r\n]", " ", txt, flags=re.IGNORECASE | re.MULTILINE)
            txts = []
            for t in re.split(r"[.?!;\n]", txt):
                for w in keywords:
                    t = re.sub(r"(^|[ .?/'\"\(\)!,:;-])(%s)([ .?/'\"\(\)!,:;-])" % re.escape(w), r"\1<em>\2</em>\3", t,
                               flags=re.IGNORECASE | re.MULTILINE)
                if not re.search(r"<em>[^<>]+</em>", t, flags=re.IGNORECASE | re.MULTILINE):
                    continue
                txts.append(t)
            ans[d["_id"]] = "...".join(txts) if txts else "...".join([a for a in list(hlts.items())[0][1]])

        return ans

    def getAggregation(self, result, fieldnm: str):
        """
        从搜索结果中获取聚合结果
        :param result: 搜索结果
        :param fieldnm: 字段名
        :return: 聚合结果列表
        """
        agg_field = "aggs_" + fieldnm
        if "aggregations" not in result or agg_field not in result["aggregations"]:
            return list()
        bkts = result["aggregations"][agg_field]["buckets"]
        return [(b["key"], b["doc_count"]) for b in bkts]

    def sql(self, sql: str, fetch_size: int, format: str):
        """
        执行SQL查询
        :param sql: SQL语句
        :param fetch_size: 获取数量
        :param format: 返回格式
        :return: 查询结果
        """
        logger.debug(f"MTESConnection.sql get sql: {sql}")
        sql = re.sub(r"[ `]+", " ", sql)
        sql = sql.replace("%", "")
        replaces = []
        for r in re.finditer(r" ([a-z_]+_l?tks)( like | ?= ?)'([^']+)'", sql):
            fld, v = r.group(1), r.group(3)
            match = " MATCH({}, '{}', 'operator=OR;minimum_should_match=30%') ".format(
                fld, rag_tokenizer.fine_grained_tokenize(rag_tokenizer.tokenize(v)))
            replaces.append(
                ("{}{}'{}'".format(
                    r.group(1),
                    r.group(2),
                    r.group(3)),
                 match))

        for p, r in replaces:
            sql = sql.replace(p, r, 1)
        logger.debug(f"MTESConnection.sql to es: {sql}")

        for i in range(ATTEMPT_TIME):
            try:
                res = self.es.sql.query(body={"query": sql, "fetch_size": fetch_size}, format=format,
                                      request_timeout="2s")
                return res
            except Exception as e:
                logger.warning(f"MTESConnection.sql got exception: {str(e)}")
                continue
        logger.error("MTESConnection.sql timeout for 3 times!")
        return None
