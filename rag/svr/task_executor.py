#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.

# from beartype import BeartypeConf
# from beartype.claw import beartype_all  # <-- you didn't sign up for this
# beartype_all(conf=BeartypeConf(violation_type=UserWarning))    # <-- emit warnings from all code

# 禁用pycat错误打印
import os
os.environ['PYCAT_ENABLED'] = 'false'

import random
import sys
import atexit

from api.utils.log_utils import initRootLogger, get_project_base_directory
from graphrag.general.index import WithCommunity, WithResolution, Dealer
from graphrag.light.graph_extractor import GraphExtractor as LightKGExt
from graphrag.general.graph_extractor import GraphExtractor as GeneralKGExt
from graphrag.utils import get_llm_cache, set_llm_cache, get_tags_from_cache, set_tags_to_cache
from rag.prompts import keyword_extraction, question_proposal, content_tagging

CONSUMER_NO = "0" if len(sys.argv) < 2 else sys.argv[1]
CONSUMER_NAME = "task_executor_" + CONSUMER_NO
initRootLogger(CONSUMER_NAME)

import logging
import os
from datetime import datetime
import json
import xxhash
import copy
import re
from functools import partial
from io import BytesIO
from multiprocessing.context import TimeoutError
from timeit import default_timer as timer
import tracemalloc
import resource
import signal
import trio

import numpy as np
from peewee import DoesNotExist

from api.db import LLMType, ParserType, TaskStatus
from api.db.services.document_service import DocumentService
from api.db.services.llm_service import LLMBundle
from api.db.services.task_service import TaskService
from api.db.services.file2document_service import File2DocumentService
from api import settings
from api.versions import get_ragflow_version
from api.db.db_models import close_connection
from rag.app import laws, paper, presentation, manual, qa, table, book, resume, picture, naive, one, audio, \
    email, tag
from rag.nlp import search, rag_tokenizer
from rag.raptor import RecursiveAbstractiveProcessing4TreeOrganizedRetrieval as Raptor
from rag.settings import DOC_MAXIMUM_SIZE, SVR_QUEUE_NAME, print_rag_settings, TAG_FLD, PAGERANK_FLD
from rag.utils import num_tokens_from_string
from rag.utils.redis_conn import REDIS_CONN
from rag.utils.storage_factory import STORAGE_IMPL
from graphrag.utils import chat_limiter
from rag.utils.chunk_processor import ensure_max_char_limit, content_split
from rag.utils.rate_limiter import REDIS_RATE_LIMITER

BATCH_SIZE = 64

FACTORY = {
    "general": naive,
    ParserType.NAIVE.value: naive,
    ParserType.PAPER.value: paper,
    ParserType.BOOK.value: book,
    ParserType.PRESENTATION.value: presentation,
    ParserType.MANUAL.value: manual,
    ParserType.LAWS.value: laws,
    ParserType.QA.value: qa,
    ParserType.TABLE.value: table,
    ParserType.RESUME.value: resume,
    ParserType.PICTURE.value: picture,
    ParserType.ONE.value: one,
    ParserType.AUDIO.value: audio,
    ParserType.EMAIL.value: email,
    ParserType.KG.value: naive,
    ParserType.TAG.value: tag
}

UNACKED_ITERATOR = None
CONSUMER_NAME = "task_consumer_" + CONSUMER_NO
BOOT_AT = datetime.now().astimezone().isoformat(timespec="milliseconds")
PENDING_TASKS = 0
LAG_TASKS = 0
DONE_TASKS = 0
FAILED_TASKS = 0

CURRENT_TASKS = {}

MAX_CONCURRENT_TASKS = int(os.environ.get('MAX_CONCURRENT_TASKS', "5"))
MAX_CONCURRENT_CHUNK_BUILDERS = int(os.environ.get('MAX_CONCURRENT_CHUNK_BUILDERS', "1"))
task_limiter = trio.CapacityLimiter(MAX_CONCURRENT_TASKS)
chunk_limiter = trio.CapacityLimiter(MAX_CONCURRENT_CHUNK_BUILDERS)
# 模型rpm限流器
model_rpm_limiter = {}

# 控制是否继续运行的全局标志
SHOULD_EXIT = False
# 添加进程标识文件路径
PID_FILE = f"/tmp/task_executor_{CONSUMER_NO}.pid"


def get_model_limiter(model: LLMBundle, batch_size=1) -> trio.CapacityLimiter:
    """
    根据模型获取对应的限流器

    注意：此函数仍然返回trio.CapacityLimiter以保持兼容性，
    但实际的限流逻辑已经迁移到Redis限流器中
    """
    llm_name = getattr(model, "llm_name", "")
    rpm_value = getattr(model, "rpm", 100)

    # 处理rpm为None的情况
    if rpm_value is None:
        rpm_value = 100
        logging.warning(f"[任务执行器] 模型 {llm_name} 的rpm为None，使用默认值100")

    try:
        rpm = int(rpm_value / batch_size)
    except (TypeError, ValueError) as e:
        logging.warning(f"[任务执行器] 模型 {llm_name} 的rpm计算失败: rpm_value={rpm_value}, batch_size={batch_size}, error={e}")
        rpm = 100  # 使用默认值

    # 确保rpm至少为1
    rpm = max(1, rpm)

    # 注册到Redis限流器
    REDIS_RATE_LIMITER.register_limiter(llm_name, rpm)

    # 为了兼容性，仍然返回一个CapacityLimiter
    if llm_name not in model_rpm_limiter:
        model_rpm_limiter[llm_name] = trio.CapacityLimiter(rpm)
    return model_rpm_limiter[llm_name]


# SIGUSR1 handler: start tracemalloc and take snapshot
def start_tracemalloc_and_snapshot(signum, frame):
    if not tracemalloc.is_tracing():
        logging.info("start tracemalloc")
        tracemalloc.start()
    else:
        logging.info("tracemalloc is already running")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    snapshot_file = f"snapshot_{timestamp}.trace"
    snapshot_file = os.path.abspath(os.path.join(get_project_base_directory(), "logs", f"{os.getpid()}_snapshot_{timestamp}.trace"))

    snapshot = tracemalloc.take_snapshot()
    snapshot.dump(snapshot_file)
    current, peak = tracemalloc.get_traced_memory()
    max_rss = resource.getrusage(resource.RUSAGE_SELF).ru_maxrss
    logging.info(f"taken snapshot {snapshot_file}. max RSS={max_rss / 1000:.2f} MB, current memory usage: {current / 10**6:.2f} MB, Peak memory usage: {peak / 10**6:.2f} MB")

# SIGUSR2 handler: stop tracemalloc
def stop_tracemalloc(signum, frame):
    if tracemalloc.is_tracing():
        logging.info("stop tracemalloc")
        tracemalloc.stop()
    else:
        logging.info("tracemalloc not running")

# 新增: 处理程序退出信号(SIGINT, SIGTERM)
def handle_exit_signal(signum, frame):
    global SHOULD_EXIT
    sig_name = "SIGINT" if signum == signal.SIGINT else "SIGTERM"
    logging.info(f"接收到 {sig_name} 信号，正在准备优雅退出...")
    SHOULD_EXIT = True

# 新增: 清理函数，在退出前执行
def cleanup():
    logging.info(f"正在执行清理工作...")
    try:
        # 从TASKEXE集合中移除当前消费者
        REDIS_CONN.srem("TASKEXE", CONSUMER_NAME)
        logging.info(f"已从TASKEXE集合中移除 {CONSUMER_NAME}")

        # 删除PID文件
        if os.path.exists(PID_FILE):
            os.unlink(PID_FILE)
            logging.info(f"已删除PID文件: {PID_FILE}")

        # 打印当前任务状态
        if CURRENT_TASKS:
            logging.warning(f"退出时仍有 {len(CURRENT_TASKS)} 个未完成的任务")
            for task_id, task in CURRENT_TASKS.items():
                logging.warning(f"未完成任务: {task_id}")
    except Exception as e:
        logging.exception(f"清理过程中发生错误: {str(e)}")

# 新增: 创建PID文件并检查是否有已运行的实例
def create_pid_file():
    if os.path.exists(PID_FILE):
        try:
            with open(PID_FILE, 'r') as f:
                old_pid = int(f.read().strip())
            # 尝试向已有的PID发送空信号，检查进程是否存在
            os.kill(old_pid, 0)
            logging.error(f"发现已有task_executor进程(PID: {old_pid})正在运行！如果确定没有运行，请手动删除 {PID_FILE}")
            return False
        except (OSError, ValueError):
            # 进程不存在或PID文件内容无效，可以继续
            logging.warning(f"发现过时的PID文件，将被覆盖")

    # 创建新的PID文件
    try:
        with open(PID_FILE, 'w') as f:
            f.write(str(os.getpid()))
        logging.info(f"已创建PID文件: {PID_FILE}")
        return True
    except Exception as e:
        logging.exception(f"创建PID文件失败: {str(e)}")
        return False

class TaskCanceledException(Exception):
    def __init__(self, msg):
        self.msg = msg


def set_progress(task_id, from_page=0, to_page=-1, prog=None, msg="Processing..."):
    logging.warning(f"[任务执行器] 设置进度: task_id={task_id}, from_page={from_page}, to_page={to_page}, prog={prog}, msg={msg}")
    if prog is not None and prog < 0:
        msg = "[ERROR]" + msg

    cancel = TaskService.do_cancel(task_id)

    if cancel:
        msg += " [Canceled]"
        prog = -1

    if to_page > 0:
        if msg:
            if from_page < to_page:
                msg = f"Page({from_page + 1}~{to_page + 1}): " + msg
    if msg:
        msg = datetime.now().strftime("%H:%M:%S") + " " + msg
    d = {"progress_msg": msg}
    if prog is not None:
        d["progress"] = prog

    logging.warning(f"set_progress({task_id}), progress: {prog}, progress_msg: {msg}")

    TaskService.update_progress(task_id, d)

    close_connection()
    if cancel:
        raise TaskCanceledException(msg)

async def collect():
    global CONSUMER_NAME, DONE_TASKS, FAILED_TASKS
    global UNACKED_ITERATOR, SHOULD_EXIT

    # 检查是否应该退出
    if SHOULD_EXIT:
        logging.info(f"[任务执行器] 由于收到退出信号，返回(None, None)")
        return None, None

    # logging.info(f"[任务执行器] 开始从队列收集任务: consumer={CONSUMER_NAME}")
    try:
        if not UNACKED_ITERATOR:
            # logging.info(f"[任务执行器] 获取未确认消息迭代器: queue={SVR_QUEUE_NAME}, group=rag_flow_svr_task_broker")
            UNACKED_ITERATOR = REDIS_CONN.get_unacked_iterator(SVR_QUEUE_NAME, "rag_flow_svr_task_broker", CONSUMER_NAME)

        try:
            redis_msg = next(UNACKED_ITERATOR)
        except StopIteration:
            redis_msg = REDIS_CONN.queue_consumer(SVR_QUEUE_NAME, "rag_flow_svr_task_broker", CONSUMER_NAME)

        if not redis_msg:
            await trio.sleep(1)
            logging.debug(f"[任务执行器] 队列中无消息，返回(None, None)")
            return None, None
    except Exception as e:
        logging.exception(f"[任务执行器] 从队列收集任务异常: error={str(e)}")
        logging.info(f"[任务执行器] 由于异常，返回(None, None)")
        return None, None

    msg = redis_msg.get_message()
    if not msg:
        logging.error(f"[任务执行器] 收到空消息: msg_id={redis_msg.get_msg_id()}")
        redis_msg.ack()
        logging.info(f"[任务执行器] 由于空消息，返回(None, None)")
        return None, None

    # logging.info(f"[任务执行器] 收到任务消息: task_id={msg.get('id')}, msg_id={redis_msg.get_msg_id()}")

    canceled = False
    try:
        logging.warning(f"[任务执行器] 获取任务详情: task_id={msg['id']}")
        task = TaskService.get_task(msg["id"])

        if task:
            logging.warning(f"[任务执行器] 检查任务状态: task_id={msg['id']}")
            _, doc = DocumentService.get_by_id(task["doc_id"])
            canceled = doc.run == TaskStatus.CANCEL.value or doc.progress < 0
            if canceled:
                logging.warning(f"[任务执行器] 任务已被取消: task_id={msg['id']}, doc_id={task['doc_id']}")
    except Exception as e:
        logging.exception(f"[任务执行器] 获取任务详情异常: task_id={msg.get('id')}, error={str(e)}")
        task = None

    if not task or canceled:
        state = "is unknown" if not task else "has been cancelled"
        FAILED_TASKS += 1
        logging.warning(f"[任务执行器] 任务无法处理: task_id={msg['id']}, state={state}")
        redis_msg.ack()
        logging.info(f"[任务执行器] 任务不存在或已取消，返回(None, None)")
        return None, None

    task["task_type"] = msg.get("task_type", "")
    logging.info(f"[任务执行器] 成功收集任务: task_id={msg['id']}, task_type={task['task_type']}")
    return redis_msg, task


async def get_storage_binary(bucket, name):
    return await trio.to_thread.run_sync(lambda: STORAGE_IMPL.get(bucket, name))


async def build_chunks(task, progress_callback, max_chars):
    logging.warning(f"[任务执行器] 开始分块: task_id={task['id']}")
    if task["size"] > DOC_MAXIMUM_SIZE:
        set_progress(task["id"], prog=-1, msg="File size exceeds( <= %dMb )" %
                                              (int(DOC_MAXIMUM_SIZE / 1024 / 1024)))
        return []

    parser_id = task["parser_id"].lower()
    chunker = FACTORY[parser_id]
    try:
        st = timer()
        # logging.warning(f"[任务执行器] 获取存储地址: doc_id={task['doc_id']}")
        bucket, name = File2DocumentService.get_storage_address(doc_id=task["doc_id"])
        # logging.warning(f"[任务执行器] 获取存储地址: bucket={bucket}, name={name}")
        binary = await get_storage_binary(bucket, name)
        # logging.info("From minio({}) {}/{}".format(timer() - st, task["location"], task["name"]))
    except TimeoutError:
        progress_callback(-1, "Internal server error: Fetch file from s3 timeout. Could you try it again.")
        logging.exception(
            "S3 {}/{} got timeout: Fetch file from s3 timeout.".format(task["location"], task["name"]))
        raise
    except Exception as e:
        if re.search("(No such file|not found)", str(e)):
            progress_callback(-1, "Can not find file <%s> from s3. Could you try it again?" % task["name"])
        else:
            progress_callback(-1, "Get file from s3: %s" % str(e).replace("'", ""))
        logging.exception("Chunking {}/{} got exception".format(task["location"], task["name"]))
        raise

    try:
        async with chunk_limiter:
            logging.warning(f"[任务执行器] 开始分块: task_id={task['id']}")
            cks = await trio.to_thread.run_sync(lambda: chunker.chunk(task["name"], binary=binary, from_page=task["from_page"],
                                to_page=task["to_page"], lang=task["language"], callback=progress_callback,
                                kb_id=task["kb_id"], parser_config=task["parser_config"], tenant_id=task["tenant_id"]))
            # 根据embedding模型配置应用字符限制

            if max_chars:
                cks = ensure_max_char_limit(parser_id, cks, max_chars)

        logging.warning("Chunking({}) {}/{} done".format(timer() - st, task["location"], task["name"]))

        # 添加日志，记录每个chunk的content_with_weight大小
        logging.info(f"=== Generated {len(cks)} chunks for document {task['name']} ===")
        for i, ck in enumerate(cks):
            if "content_with_weight" in ck:
                content = ck["content_with_weight"]
                token_count = num_tokens_from_string(content)
                logging.info(f"Chunk {i}: tokens={token_count}, chars={len(content)}")
                if token_count > 1000:  # 接近miffy-002模型的限制
                    logging.warning(f"Chunk {i} may exceed embedding model limit (1024): tokens={token_count}")

    except TaskCanceledException:
        raise
    except Exception as e:
        progress_callback(-1, "Internal server error while chunking: %s" % str(e).replace("'", ""))
        logging.exception("Chunking {}/{} got exception".format(task["location"], task["name"]))
        raise

    docs = []
    doc = {
        "doc_id": task["doc_id"],
        "kb_id": str(task["kb_id"])
    }
    if task["pagerank"]:
        doc[PAGERANK_FLD] = int(task["pagerank"])
    el = 0
    for ck in cks:
        d = copy.deepcopy(doc)
        d.update(ck)
        d["id"] = xxhash.xxh64((ck["content_with_weight"] + str(d["doc_id"])).encode("utf-8")).hexdigest()
        d["create_time"] = str(datetime.now()).replace("T", " ")[:19]
        d["create_timestamp_flt"] = datetime.now().timestamp()
        if not d.get("image"):
            _ = d.pop("image", None)
            d["img_id"] = ""
            docs.append(d)
            continue

        try:
            output_buffer = BytesIO()
            if isinstance(d["image"], bytes):
                output_buffer = BytesIO(d["image"])
            else:
                d["image"].save(output_buffer, format='JPEG')

            st = timer()
            await trio.to_thread.run_sync(lambda: STORAGE_IMPL.put(task["kb_id"], d["id"], output_buffer.getvalue()))
            el += timer() - st
        except Exception:
            logging.exception(
                "Saving image of chunk {}/{}/{} got exception".format(task["location"], task["name"], d["id"]))
            raise

        d["img_id"] = "{}-{}".format(task["kb_id"], d["id"])
        del d["image"]
        docs.append(d)
    logging.info("MINIO PUT({}):{}".format(task["name"], el))

    if task["parser_config"].get("auto_keywords", 0):
        st = timer()
        progress_callback(msg="Start to generate keywords for every chunk ...")
        chat_mdl = LLMBundle(task["tenant_id"], LLMType.CHAT, llm_name=task["llm_id"], lang=task["language"])

        async def doc_keyword_extraction(chat_mdl, d, topn):
            cached = get_llm_cache(chat_mdl.llm_name, d["content_with_weight"], "keywords", {"topn": topn})
            if not cached:
                async with chat_limiter:
                    cached = await trio.to_thread.run_sync(lambda: keyword_extraction(chat_mdl, d["content_with_weight"], topn))
                set_llm_cache(chat_mdl.llm_name, d["content_with_weight"], cached, "keywords", {"topn": topn})
            if cached:
                d["important_kwd"] = cached.split(",")
                d["important_tks"] = rag_tokenizer.tokenize(" ".join(d["important_kwd"]))
            return
        async with trio.open_nursery() as nursery:
            for d in docs:
                nursery.start_soon(lambda: doc_keyword_extraction(chat_mdl, d, task["parser_config"]["auto_keywords"]))
        progress_callback(msg="Keywords generation {} chunks completed in {:.2f}s".format(len(docs), timer() - st))

    if task["parser_config"].get("auto_questions", 0):
        st = timer()
        progress_callback(msg="Start to generate questions for every chunk ...")
        chat_mdl = LLMBundle(task["tenant_id"], LLMType.CHAT, llm_name=task["llm_id"], lang=task["language"])

        async def doc_question_proposal(chat_mdl, d, topn):
            cached = get_llm_cache(chat_mdl.llm_name, d["content_with_weight"], "question", {"topn": topn})
            if not cached:
                async with chat_limiter:
                    cached = await trio.to_thread.run_sync(lambda: question_proposal(chat_mdl, d["content_with_weight"], topn))
                set_llm_cache(chat_mdl.llm_name, d["content_with_weight"], cached, "question", {"topn": topn})
            if cached:
                d["question_kwd"] = cached.split("\n")
                d["question_tks"] = rag_tokenizer.tokenize("\n".join(d["question_kwd"]))
        async with trio.open_nursery() as nursery:
            for d in docs:
                nursery.start_soon(lambda: doc_question_proposal(chat_mdl, d, task["parser_config"]["auto_questions"]))
        progress_callback(msg="Question generation {} chunks completed in {:.2f}s".format(len(docs), timer() - st))

    if task["kb_parser_config"].get("tag_kb_ids", []):
        progress_callback(msg="Start to tag for every chunk ...")
        kb_ids = task["kb_parser_config"]["tag_kb_ids"]
        tenant_id = task["tenant_id"]
        topn_tags = task["kb_parser_config"].get("topn_tags", 3)
        S = 1000
        st = timer()
        examples = []
        all_tags = get_tags_from_cache(kb_ids)
        if not all_tags:
            all_tags = settings.retrievaler.all_tags_in_portion(tenant_id, kb_ids, S)
            set_tags_to_cache(kb_ids, all_tags)
        else:
            all_tags = json.loads(all_tags)

        chat_mdl = LLMBundle(task["tenant_id"], LLMType.CHAT, llm_name=task["llm_id"], lang=task["language"])

        docs_to_tag = []
        for d in docs:
            if settings.retrievaler.tag_content(tenant_id, kb_ids, d, all_tags, topn_tags=topn_tags, S=S):
                examples.append({"content": d["content_with_weight"], TAG_FLD: d[TAG_FLD]})
            else:
                docs_to_tag.append(d)

        async def doc_content_tagging(chat_mdl, d, topn_tags):
            cached = get_llm_cache(chat_mdl.llm_name, d["content_with_weight"], all_tags, {"topn": topn_tags})
            if not cached:
                picked_examples = random.choices(examples, k=2) if len(examples)>2 else examples
                async with chat_limiter:
                    cached = await trio.to_thread.run_sync(lambda: content_tagging(chat_mdl, d["content_with_weight"], all_tags, picked_examples, topn=topn_tags))
                if cached:
                    cached = json.dumps(cached)
            if cached:
                set_llm_cache(chat_mdl.llm_name, d["content_with_weight"], cached, all_tags, {"topn": topn_tags})
                d[TAG_FLD] = json.loads(cached)
        async with trio.open_nursery() as nursery:
            for d in docs_to_tag:
                nursery.start_soon(lambda: doc_content_tagging(chat_mdl, d, topn_tags))
        progress_callback(msg="Tagging {} chunks completed in {:.2f}s".format(len(docs), timer() - st))

    return docs


def init_kb(row, vector_size: int):
    idxnm = search.index_name(row["tenant_id"])
    return settings.docStoreConn.createIdx(idxnm, row.get("kb_id", ""), vector_size)


async def embedding(docs, mdl, parser_config=None, callback=None):
    if parser_config is None:
        parser_config = {}

    # 从 LLMBundle 实例中获取 batch_size
    batch_size = mdl.batch_size
    # logging.info(f"[任务执行器] embedding batch_size: {batch_size}")
    tts, cnts = [], []
    for d in docs:
        tts.append(d.get("docnm_kwd", "Title"))
        c = "\n".join(d.get("question_kwd", []))
        if not c:
            c = d["content_with_weight"]
        c = re.sub(r"</?(table|td|caption|tr|th)( [^<>]{0,12})?>", " ", c)
        if not c:
            c = "None"
        cnts.append(c)

    tk_count = 0
    # 获取模型名称用于限流
    model_name = getattr(mdl, "llm_name", "")
    limiter = get_model_limiter(mdl)
    if len(tts) == len(cnts):
        # 使用Redis限流器
        REDIS_RATE_LIMITER.wait_if_needed(model_name, 1)
        vts, c = await trio.to_thread.run_sync(lambda: mdl.encode(tts[0: 1]), limiter=limiter)
        tts = np.concatenate([vts for _ in range(len(tts))], axis=0)
        tk_count += c

    # 添加日志，记录每批要处理的内容长度
    logging.info(f"=== Embedding {len(cnts)} chunks in batches of {batch_size} ===")
    for i, content in enumerate(cnts):
        token_count = num_tokens_from_string(content)
        logging.info(f"Content {i} for embedding: tokens={token_count}, chars={len(content)}")
        if token_count > 1000:  # 接近miffy-002模型的限制
            logging.warning(f"Content {i} exceeds embedding model limit (1024): tokens={token_count}, preview: {content[:100]}...")

    cnts_ = np.array([])
    for i in range(0, len(cnts), batch_size):
        batch = cnts[i: i + batch_size]
        # 添加日志记录当前批次
        logging.info(f"Processing batch {i//batch_size+1}/{(len(cnts)+batch_size-1)//batch_size}: items {i} to {min(i+batch_size-1, len(cnts)-1)}")

        try:
            # 使用Redis限流器
            vts, c = await trio.to_thread.run_sync(lambda: long_embedding(cnts[i: i + batch_size], mdl, batch_size), limiter=limiter)
            if len(cnts_) == 0:
                cnts_ = vts
            else:
                cnts_ = np.concatenate((cnts_, vts), axis=0)
            tk_count += c
            callback(prog=0.7 + 0.2 * (i + 1) / len(cnts), msg="")
        except Exception as e:
            # 添加错误日志，记录失败的批次详情
            logging.error(f"Error processing batch {i//batch_size+1}: {str(e)}")
            for j, content in enumerate(batch):
                token_count = num_tokens_from_string(content)
                logging.error(f"Item {i+j}: tokens={token_count}, chars={len(content)}, preview: {content[:50]}...")
            raise
    cnts = cnts_

    title_w = float(parser_config.get("filename_embd_weight", 0.1))
    vects = (title_w * tts + (1 - title_w) *
             cnts) if len(tts) == len(cnts) else cnts

    assert len(vects) == len(docs)
    vector_size = 0
    for i, d in enumerate(docs):
        v = vects[i].tolist()
        vector_size = len(v)
        d["q_%d_vec" % len(v)] = v
    return tk_count, vector_size


def long_embedding(contents: list, embedding_model, batch_size):
    used_tokens = 0
    vectors = np.array([])
    for content in contents:
        # 1. 文本分段
        segments = content_split(content, embedding_model.max_chars)

        # 2. 批量处理segments获取embeddings
        all_embeddings = []
        for i in range(0, len(segments), batch_size):
            # 使用Redis限流器
            REDIS_RATE_LIMITER.wait_if_needed(embedding_model.llm_name, batch_size)
            embeddings, c = embedding_model.encode(segments[i: i+batch_size])
            all_embeddings.extend(embeddings)
            used_tokens += c

        # 3. 对所有embeddings进行平均池化
        final_embedding = np.array([np.mean(all_embeddings, axis=0)])
        if len(vectors) == 0:
            vectors = final_embedding
        else:
            vectors = np.concatenate((vectors, final_embedding), axis=0)

    return vectors, used_tokens


async def run_raptor(row, chat_mdl, embd_mdl, vector_size, callback=None):
    chunks = []
    vctr_nm = "q_%d_vec"%vector_size
    for d in settings.retrievaler.chunk_list(row["doc_id"], row["tenant_id"], [str(row["kb_id"])],
                                             fields=["content_with_weight", vctr_nm]):
        chunks.append((d["content_with_weight"], np.array(d[vctr_nm])))

    raptor = Raptor(
        row["parser_config"]["raptor"].get("max_cluster", 64),
        chat_mdl,
        embd_mdl,
        row["parser_config"]["raptor"]["prompt"],
        row["parser_config"]["raptor"]["max_token"],
        row["parser_config"]["raptor"]["threshold"]
    )
    original_length = len(chunks)
    chunks = await raptor(chunks, row["parser_config"]["raptor"]["random_seed"], callback)
    doc = {
        "doc_id": row["doc_id"],
        "kb_id": [str(row["kb_id"])],
        "docnm_kwd": row["name"],
        "title_tks": rag_tokenizer.tokenize(row["name"])
    }
    if row["pagerank"]:
        doc[PAGERANK_FLD] = int(row["pagerank"])
    res = []
    tk_count = 0
    for content, vctr in chunks[original_length:]:
        d = copy.deepcopy(doc)
        d["id"] = xxhash.xxh64((content + str(d["doc_id"])).encode("utf-8")).hexdigest()
        d["create_time"] = str(datetime.now()).replace("T", " ")[:19]
        d["create_timestamp_flt"] = datetime.now().timestamp()
        d[vctr_nm] = vctr.tolist()
        d["content_with_weight"] = content
        d["content_ltks"] = rag_tokenizer.tokenize(content)
        d["content_sm_ltks"] = rag_tokenizer.fine_grained_tokenize(d["content_ltks"])
        res.append(d)
        tk_count += num_tokens_from_string(content)
    return res, tk_count


async def run_graphrag(row, chat_model, language, embedding_model, callback=None):
    chunks = []
    for d in settings.retrievaler.chunk_list(row["doc_id"], row["tenant_id"], [str(row["kb_id"])],
                                             fields=["content_with_weight", "doc_id"]):
        chunks.append((d["doc_id"], d["content_with_weight"]))

    dealer = Dealer(LightKGExt if row["parser_config"]["graphrag"]["method"] != 'general' else GeneralKGExt,
                    row["tenant_id"],
                    str(row["kb_id"]),
                    chat_model,
                    chunks=chunks,
                    language=language,
                    entity_types=row["parser_config"]["graphrag"]["entity_types"],
                    embed_bdl=embedding_model,
                    callback=callback)
    await dealer()


async def do_handle_task(task):
    task_id = task["id"]
    task_from_page = task["from_page"]
    task_to_page = task["to_page"]
    task_tenant_id = task["tenant_id"]
    task_embedding_id = task["embd_id"]
    task_language = task["language"]
    task_llm_id = task["llm_id"]
    task_dataset_id = task["kb_id"]
    task_doc_id = task["doc_id"]
    task_document_name = task["name"]
    task_parser_config = task["parser_config"]
    task_start_ts = timer()

    # prepare the progress callback function
    progress_callback = partial(set_progress, task_id, task_from_page, task_to_page)
    logging.warning(f"[任务执行器] progress_callback({task_id}), task_from_page={task_from_page}, task_to_page={task_to_page}")
    # FIXME: workaround, Infinity doesn't support table parsing method, this check is to notify user
    lower_case_doc_engine = settings.DOC_ENGINE.lower()
    if lower_case_doc_engine == 'infinity' and task['parser_id'].lower() == 'table':
        error_message = "Table parsing method is not supported by Infinity, please use other parsing methods or use Elasticsearch as the document engine."
        progress_callback(-1, msg=error_message)
        raise Exception(error_message)

    task_canceled = TaskService.do_cancel(task_id)
    if task_canceled:
        progress_callback(-1, msg="Task has been canceled.")
        return

    try:
        # bind embedding model
        embedding_model = LLMBundle(task_tenant_id, LLMType.EMBEDDING, llm_name=task_embedding_id, lang=task_language)
        vts, _ = embedding_model.encode(["ok"])
        vector_size = len(vts[0])
    except Exception as e:
        error_message = f'Fail to bind embedding model: {str(e)}'
        progress_callback(-1, msg=error_message)
        logging.exception(error_message)
        raise

    init_kb(task, vector_size)

    # Either using RAPTOR or Standard chunking methods
    if task.get("task_type", "") == "raptor":
        # bind LLM for raptor
        chat_model = LLMBundle(task_tenant_id, LLMType.CHAT, llm_name=task_llm_id, lang=task_language)
        # run RAPTOR
        chunks, token_count = await run_raptor(task, chat_model, embedding_model, vector_size, progress_callback)
    # Either using graphrag or Standard chunking methods
    elif task.get("task_type", "") == "graphrag":
        start_ts = timer()
        chat_model = LLMBundle(task_tenant_id, LLMType.CHAT, llm_name=task_llm_id, lang=task_language)
        await run_graphrag(task, chat_model, task_language, embedding_model, progress_callback)
        progress_callback(prog=1.0, msg="Knowledge Graph is done ({:.2f}s)".format(timer() - start_ts))
        return
    elif task.get("task_type", "") == "graph_resolution":
        start_ts = timer()
        chat_model = LLMBundle(task_tenant_id, LLMType.CHAT, llm_name=task_llm_id, lang=task_language)
        with_res = WithResolution(
            task["tenant_id"], str(task["kb_id"]),chat_model, embedding_model,
            progress_callback
        )
        await with_res()
        progress_callback(prog=1.0, msg="Knowledge Graph resolution is done ({:.2f}s)".format(timer() - start_ts))
        return
    elif task.get("task_type", "") == "graph_community":
        start_ts = timer()
        chat_model = LLMBundle(task_tenant_id, LLMType.CHAT, llm_name=task_llm_id, lang=task_language)
        with_comm = WithCommunity(
            task["tenant_id"], str(task["kb_id"]), chat_model, embedding_model,
            progress_callback
        )
        await with_comm()
        progress_callback(prog=1.0, msg="GraphRAG community reports generation is done ({:.2f}s)".format(timer() - start_ts))
        return
    else:
        # Standard chunking methods
        start_ts = timer()
        chunks = await build_chunks(task, progress_callback, embedding_model.max_chars)
        logging.info("Build document {}: {:.2f}s".format(task_document_name, timer() - start_ts))
        if chunks is None:
            return
        if not chunks:
            progress_callback(1., msg=f"No chunk built from {task_document_name}")
            return
        # TODO: exception handler
        ## set_progress(task["did"], -1, "ERROR: ")
        progress_callback(msg="Generate {} chunks".format(len(chunks)))
        start_ts = timer()
        try:
            token_count, vector_size = await embedding(chunks, embedding_model, task_parser_config, progress_callback)
        except Exception as e:
            error_message = "Generate embedding error:{}".format(str(e))
            progress_callback(-1, error_message)
            logging.exception(error_message)
            token_count = 0
            raise
        progress_message = "Embedding chunks ({:.2f}s)".format(timer() - start_ts)
        logging.info(progress_message)
        progress_callback(msg=progress_message)

    chunk_count = len(set([chunk["id"] for chunk in chunks]))
    start_ts = timer()
    doc_store_result = ""
    es_bulk_size = 4
    for b in range(0, len(chunks), es_bulk_size):
        doc_store_result = await trio.to_thread.run_sync(lambda: settings.docStoreConn.insert(chunks[b:b + es_bulk_size], search.index_name(task_tenant_id), task_dataset_id))
        if b % 128 == 0:
            progress_callback(prog=0.8 + 0.1 * (b + 1) / len(chunks), msg="")
        if doc_store_result:
            error_message = f"Insert chunk error: {doc_store_result}, please check log file and Elasticsearch/Infinity status!"
            progress_callback(-1, msg=error_message)
            raise Exception(error_message)
        chunk_ids = [chunk["id"] for chunk in chunks[:b + es_bulk_size]]
        chunk_ids_str = " ".join(chunk_ids)
        try:
            TaskService.update_chunk_ids(task["id"], chunk_ids_str)
        except DoesNotExist:
            logging.warning(f"do_handle_task update_chunk_ids failed since task {task['id']} is unknown.")
            doc_store_result = await trio.to_thread.run_sync(lambda: settings.docStoreConn.delete({"id": chunk_ids}, search.index_name(task_tenant_id), task_dataset_id))
            return
    logging.info("Indexing doc({}), page({}-{}), chunks({}), elapsed: {:.2f}".format(task_document_name, task_from_page,
                                                                                     task_to_page, len(chunks),
                                                                                     timer() - start_ts))

    DocumentService.increment_chunk_num(task_doc_id, task_dataset_id, token_count, chunk_count, 0)

    time_cost = timer() - start_ts
    task_time_cost = timer() - task_start_ts
    progress_callback(prog=1.0, msg="Indexing done ({:.2f}s). Task done ({:.2f}s)".format(time_cost, task_time_cost))
    logging.info(
        "Chunk doc({}), page({}-{}), chunks({}), token({}), elapsed:{:.2f}".format(task_document_name, task_from_page,
                                                                                   task_to_page, len(chunks),
                                                                                   token_count, task_time_cost))


async def handle_task():
    global DONE_TASKS, FAILED_TASKS
    try:
        redis_msg, task = await collect()
        logging.debug(f"[任务执行器] collect()返回: redis_msg类型={type(redis_msg)}, task类型={type(task)}")
        if not task:
            logging.debug("[任务执行器] 没有任务需要处理，跳过")
            return
        try:
            logging.info(f"handle_task begin for task {json.dumps(task)}")
            CURRENT_TASKS[task["id"]] = copy.deepcopy(task)
            await do_handle_task(task)
            DONE_TASKS += 1
            CURRENT_TASKS.pop(task["id"], None)
            logging.info(f"handle_task done for task {json.dumps(task)}")
        except Exception as e:
            FAILED_TASKS += 1
            CURRENT_TASKS.pop(task["id"], None)
            try:
                set_progress(task["id"], prog=-1, msg=f"[Exception]: {e}")
            except Exception:
                pass
            logging.exception(f"handle_task got exception for task {json.dumps(task)}")
        redis_msg.ack()
    except Exception as e:
        logging.exception(f"[任务执行器] handle_task顶层异常: {str(e)}")


async def report_status():
    global CONSUMER_NAME, BOOT_AT, PENDING_TASKS, LAG_TASKS, DONE_TASKS, FAILED_TASKS, SHOULD_EXIT
    REDIS_CONN.sadd("TASKEXE", CONSUMER_NAME)
    while not SHOULD_EXIT:
        try:
            now = datetime.now()
            group_info = REDIS_CONN.queue_info(SVR_QUEUE_NAME, "rag_flow_svr_task_broker")
            if group_info is not None:
                PENDING_TASKS = int(group_info.get("pending", 0))
                LAG_TASKS = int(group_info.get("lag", 0))

            current = copy.deepcopy(CURRENT_TASKS)
            heartbeat = json.dumps({
                "name": CONSUMER_NAME,
                "now": now.astimezone().isoformat(timespec="milliseconds"),
                "boot_at": BOOT_AT,
                "pending": PENDING_TASKS,
                "lag": LAG_TASKS,
                "done": DONE_TASKS,
                "failed": FAILED_TASKS,
                "current": current,
            })
            REDIS_CONN.zadd(CONSUMER_NAME, heartbeat, now.timestamp())
            logging.info(f"{CONSUMER_NAME} reported heartbeat: {heartbeat}")

            expired = REDIS_CONN.zcount(CONSUMER_NAME, 0, now.timestamp() - 60 * 30)
            if expired > 0:
                REDIS_CONN.zpopmin(CONSUMER_NAME, expired)
        except Exception:
            logging.exception("report_status got exception")
        await trio.sleep(30)

    logging.info("心跳报告线程正在退出...")


async def main():
    global SHOULD_EXIT

    # 添加 debugpy 监听（仅开发环境）
    if os.environ.get("ENABLE_DEBUGPY") == "1":
        try:
            import debugpy
            # 从 CONSUMER_NO 中提取数字部分，处理类似 '0_b0407902' 的情况
            consumer_num = int(CONSUMER_NO.split('_')[0]) if '_' in CONSUMER_NO else int(CONSUMER_NO)
            # 使用不同端口避免与 ragflow_server 冲突
            debug_port = 5678 + consumer_num + 1  # 从 5679 开始
            debugpy.listen(("0.0.0.0", debug_port))
            logging.info(f"✅ Task Executor {CONSUMER_NO} 已启动调试监听，端口 {debug_port}，可随时 attach")
        except ImportError:
            logging.warning("⚠️ debugpy 未安装，跳过调试器监听。如需调试请安装: pip install debugpy")
        except Exception as e:
            logging.warning(f"⚠️ debugpy 启动失败: {e}")

    logging.info(r"""
  ______           __      ______                     __
 /_  __/___ ______/ /__   / ____/  _____  _______  __/ /_____  _____
  / / / __ `/ ___/ //_/  / __/ | |/_/ _ \/ ___/ / / / __/ __ \/ ___/
 / / / /_/ (__  ) ,<    / /____>  </  __/ /__/ /_/ / /_/ /_/ / /
/_/  \__,_/____/_/|_|  /_____/_/|_|\___/\___/\__,_/\__/\____/_/
    """)
    logging.info(f'TaskExecutor: RAGFlow version: {get_ragflow_version()}')
    settings.init_settings()
    print_rag_settings()

    # 注册信号处理器
    signal.signal(signal.SIGUSR1, start_tracemalloc_and_snapshot)
    signal.signal(signal.SIGUSR2, stop_tracemalloc)
    signal.signal(signal.SIGINT, handle_exit_signal)
    signal.signal(signal.SIGTERM, handle_exit_signal)

    # 注册退出时的清理函数
    atexit.register(cleanup)

    # 检查是否有已运行的实例
    if not create_pid_file():
        logging.error("由于已有实例正在运行，将退出...")
        sys.exit(1)

    TRACE_MALLOC_ENABLED = int(os.environ.get('TRACE_MALLOC_ENABLED', "0"))
    if TRACE_MALLOC_ENABLED:
        start_tracemalloc_and_snapshot(None, None)

    try:
        async with trio.open_nursery() as nursery:
            nursery.start_soon(report_status)

            while not SHOULD_EXIT:
                async with task_limiter:
                    # 如果收到退出信号，停止接收新任务
                    if SHOULD_EXIT:
                        break
                    nursery.start_soon(handle_task)
                    # 避免CPU满负荷
                    await trio.sleep(0.1)

            # 收到退出信号后，取消所有正在运行的任务
            logging.info("收到退出信号，正在停止接收新任务...")
            nursery.cancel_scope.cancel()
    except KeyboardInterrupt:
        # 这是一个额外的保障，以防信号处理器没有被调用
        logging.info("接收到键盘中断，正在退出...")
        SHOULD_EXIT = True

    logging.info("Task executor 主循环已退出，正在完成清理工作...")

if __name__ == "__main__":
    try:
        trio.run(main)
    except KeyboardInterrupt:
        logging.info("程序被键盘中断，正在退出...")
    except Exception as e:
        logging.exception(f"程序异常终止: {str(e)}")
    finally:
        # 确保清理工作被执行
        cleanup()
        logging.info("程序已完全退出")
