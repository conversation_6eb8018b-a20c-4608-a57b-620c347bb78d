"""
评估任务运行器
用于执行评估任务和管理评估流程
"""

import json
import logging
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

from api.db.db_models import EvaluationTask, EvaluationResult, EvaluationReport
from api.utils import get_uuid
from rag.evaluation.ragas_evaluator import RagasEvaluator, EvaluationConfig
from rag.evaluation.dataset_manager import DatasetManager


class EvaluationRunner:
    """评估任务运行器"""

    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id
        self.dataset_manager = DatasetManager(tenant_id)
        self._running_tasks = {}  # 存储正在运行的任务
        self._task_lock = threading.Lock()

    def create_task(self, name: str, dataset_id: str, kb_id: str, created_by: str,
                   metrics: List[str], llm_id: Optional[str] = None,
                   retrieval_config: Optional[Dict[str, Any]] = None,
                   llm_config: Optional[Dict[str, Any]] = None) -> str:
        """创建评估任务"""
        task_id = get_uuid()

        # 验证数据集存在
        dataset = self.dataset_manager.get_dataset(dataset_id)
        if not dataset:
            raise ValueError(f"数据集 {dataset_id} 不存在")

        # 获取知识库配置
        from api.db.services.knowledgebase_service import KnowledgebaseService
        success, kb = KnowledgebaseService.get_by_id(kb_id)
        if not success or not kb:
            raise ValueError(f"知识库 {kb_id} 不存在")

        # 自动配置模型
        if not llm_config:
            llm_config = {}

        # 从知识库获取embedding模型
        if not llm_config.get("embedding_id"):
            llm_config["embedding_id"] = kb.embd_id

        # 使用用户指定的LLM模型，如果没有指定则使用知识库默认的
        if llm_id:
            llm_config["llm_id"] = llm_id
        elif not llm_config.get("llm_id"):
            # 如果知识库有默认LLM，使用它，否则需要用户指定
            if hasattr(kb, 'llm_id') and kb.llm_id:
                llm_config["llm_id"] = kb.llm_id
            else:
                raise ValueError("请指定用于评估的LLM模型")

        # 设置默认检索配置
        if not retrieval_config:
            retrieval_config = {
                "similarity_threshold": 0.2,
                "vector_similarity_weight": 0.3,
                "top_n": 5,
                "top_k": 100
            }

        # 获取样本数量
        total_samples = self.dataset_manager.get_sample_count(dataset_id)

        # 创建任务记录
        task = EvaluationTask.create(
            id=task_id,
            name=name,
            dataset_id=dataset_id,
            kb_id=kb_id,
            tenant_id=self.tenant_id,
            created_by=created_by,
            metrics=json.dumps(metrics),
            retrieval_config=json.dumps(retrieval_config or {}),
            llm_config=json.dumps(llm_config or {}),
            status="pending",
            total_samples=total_samples,
            processed_samples=0,
            progress=0
        )

        logging.info(f"创建评估任务: {task_id}, 名称: {name}, 数据集: {dataset_id}")
        return task_id

    def get_task(self, task_id: str) -> Optional[EvaluationTask]:
        """获取任务信息"""
        try:
            return EvaluationTask.get(
                EvaluationTask.id == task_id,
                EvaluationTask.tenant_id == self.tenant_id
            )
        except EvaluationTask.DoesNotExist:
            return None

    def list_tasks(self, kb_id: Optional[str] = None, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """列出评估任务"""
        query = EvaluationTask.select().where(
            EvaluationTask.tenant_id == self.tenant_id
        )

        if kb_id:
            query = query.where(EvaluationTask.kb_id == kb_id)

        if status:
            query = query.where(EvaluationTask.status == status)

        tasks = []
        for task in query.order_by(EvaluationTask.create_time.desc()):
            tasks.append({
                "id": task.id,
                "name": task.name,
                "dataset_id": task.dataset_id,
                "kb_id": task.kb_id,
                "status": task.status,
                "progress": task.progress,
                "total_samples": task.total_samples,
                "processed_samples": task.processed_samples,
                "metrics": json.loads(task.metrics),
                "created_by": task.created_by,
                "create_time": task.create_time,
                "started_at": task.started_at,
                "completed_at": task.completed_at
            })

        return tasks

    def start_task(self, task_id: str, max_workers: int = 3) -> bool:
        """启动评估任务"""
        with self._task_lock:
            if task_id in self._running_tasks:
                logging.warning(f"任务 {task_id} 已在运行中")
                return False

        task = self.get_task(task_id)
        if not task:
            logging.error(f"任务 {task_id} 不存在")
            return False

        if task.status != "pending":
            logging.error(f"任务 {task_id} 状态不是pending，无法启动")
            return False

        # 更新任务状态
        task.status = "running"
        task.started_at = datetime.now()
        task.save()

        # 在后台线程中运行任务
        thread = threading.Thread(
            target=self._run_task_background,
            args=(task_id, max_workers),
            daemon=True
        )

        with self._task_lock:
            self._running_tasks[task_id] = thread

        thread.start()
        logging.info(f"启动评估任务: {task_id}")
        return True

    def stop_task(self, task_id: str) -> bool:
        """停止评估任务"""
        with self._task_lock:
            if task_id not in self._running_tasks:
                logging.warning(f"任务 {task_id} 未在运行中")
                return False

            # 标记任务为已取消
            task = self.get_task(task_id)
            if task:
                task.status = "cancelled"
                task.completed_at = datetime.now()
                task.save()

            # 从运行任务列表中移除
            del self._running_tasks[task_id]

        logging.info(f"停止评估任务: {task_id}")
        return True

    def _run_task_background(self, task_id: str, max_workers: int):
        """在后台运行评估任务"""
        try:
            self._execute_evaluation_task(task_id, max_workers)
        except Exception as e:
            logging.error(f"执行评估任务失败: {e}")
            # 更新任务状态为失败
            task = self.get_task(task_id)
            if task:
                task.status = "failed"
                task.completed_at = datetime.now()
                task.save()
        finally:
            # 从运行任务列表中移除
            with self._task_lock:
                if task_id in self._running_tasks:
                    del self._running_tasks[task_id]

    def _execute_evaluation_task(self, task_id: str, max_workers: int):
        """执行评估任务的核心逻辑"""
        task = self.get_task(task_id)
        if not task:
            raise ValueError(f"任务 {task_id} 不存在")

        # 解析配置
        metrics = json.loads(task.metrics)
        retrieval_config = json.loads(task.retrieval_config or "{}")
        llm_config = json.loads(task.llm_config or "{}")

        # 创建评估配置
        eval_config = EvaluationConfig(
            metrics=metrics,
            similarity_threshold=retrieval_config.get("similarity_threshold", 0.2),
            vector_similarity_weight=retrieval_config.get("vector_similarity_weight", 0.3),
            top_n=retrieval_config.get("top_n", 8),
            top_k=retrieval_config.get("top_k", 1024),
            rerank_id=retrieval_config.get("rerank_id"),
            llm_id=llm_config.get("llm_id"),
            embedding_id=llm_config.get("embedding_id")
        )

        # 获取评估样本
        samples = self.dataset_manager.get_samples(task.dataset_id)
        if not samples:
            raise ValueError(f"数据集 {task.dataset_id} 中没有样本")

        # 创建评估器
        evaluator = RagasEvaluator(self.tenant_id, task.kb_id)

        # 验证模型配置
        try:
            evaluator._get_models(eval_config)
            logging.info(f"任务 {task_id} 模型配置验证通过")
        except Exception as e:
            error_msg = f"模型配置验证失败: {e}"
            logging.error(error_msg)
            # 更新任务状态为失败
            task.status = "failed"
            task.completed_at = datetime.now()
            task.save()
            raise ValueError(error_msg)

        # 并行执行评估
        results = []
        processed_count = 0

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有评估任务
            future_to_sample = {
                executor.submit(evaluator.evaluate_sample, sample, eval_config): i
                for i, sample in enumerate(samples)
            }

            # 处理完成的任务
            for future in as_completed(future_to_sample):
                # 检查任务是否被取消
                current_task = self.get_task(task_id)
                if current_task and current_task.status == "cancelled":
                    logging.info(f"任务 {task_id} 被取消，停止执行")
                    return

                try:
                    result = future.result()
                    results.append(result)

                    # 保存单个结果到数据库
                    self._save_evaluation_result(task_id, result)

                    processed_count += 1

                    # 更新进度
                    progress = int((processed_count / len(samples)) * 100)
                    task.processed_samples = processed_count
                    task.progress = progress
                    task.save()

                    logging.info(f"任务 {task_id} 进度: {processed_count}/{len(samples)} ({progress}%)")

                except Exception as e:
                    logging.error(f"评估样本失败: {e}")
                    # 保存失败的结果
                    failed_result = EvaluationResult(
                        sample_id=f"failed_{processed_count}",
                        question="",
                        generated_answer="",
                        retrieved_contexts=[],
                        metrics={},
                        execution_time=0,
                        error_message=str(e)
                    )
                    self._save_evaluation_result(task_id, failed_result)
                    processed_count += 1

                    # 更新进度
                    progress = int((processed_count / len(samples)) * 100)
                    task.processed_samples = processed_count
                    task.progress = progress
                    task.save()

        # 生成评估报告
        report = evaluator.generate_report(results)
        self._save_evaluation_report(task_id, report)

        # 更新任务状态为完成
        task.status = "completed"
        task.completed_at = datetime.now()
        task.progress = 100
        task.save()

        logging.info(f"评估任务 {task_id} 完成")

    def _save_evaluation_result(self, task_id: str, result):
        """保存单个评估结果"""
        try:
            EvaluationResult.create(
                id=get_uuid(),
                task_id=task_id,
                sample_id=result.sample_id,
                retrieved_contexts=json.dumps(result.retrieved_contexts, ensure_ascii=False),
                generated_answer=result.generated_answer,
                faithfulness=result.metrics.get("faithfulness"),
                answer_relevancy=result.metrics.get("answer_relevancy"),
                context_precision=result.metrics.get("context_precision"),
                context_recall=result.metrics.get("context_recall"),
                context_relevancy=result.metrics.get("context_relevancy"),
                answer_correctness=result.metrics.get("answer_correctness"),
                answer_similarity=result.metrics.get("answer_similarity"),
                execution_time=result.execution_time,
                error_message=result.error_message
            )
        except Exception as e:
            logging.error(f"保存评估结果失败: {e}")

    def _save_evaluation_report(self, task_id: str, report: Dict[str, Any]):
        """保存评估报告"""
        try:
            summary = report.get("summary", {})

            EvaluationReport.create(
                id=get_uuid(),
                task_id=task_id,
                avg_faithfulness=summary.get("avg_faithfulness"),
                avg_answer_relevancy=summary.get("avg_answer_relevancy"),
                avg_context_precision=summary.get("avg_context_precision"),
                avg_context_recall=summary.get("avg_context_recall"),
                avg_context_relevancy=summary.get("avg_context_relevancy"),
                avg_answer_correctness=summary.get("avg_answer_correctness"),
                avg_answer_similarity=summary.get("avg_answer_similarity"),
                total_samples=summary.get("total_samples", 0),
                successful_samples=summary.get("successful_samples", 0),
                failed_samples=summary.get("failed_samples", 0),
                avg_execution_time=summary.get("avg_execution_time"),
                detailed_report=json.dumps(report, ensure_ascii=False)
            )
        except Exception as e:
            logging.error(f"保存评估报告失败: {e}")

    def get_task_results(self, task_id: str, limit: Optional[int] = None, offset: int = 0) -> List[Dict[str, Any]]:
        """获取任务的评估结果"""
        query = EvaluationResult.select().where(
            EvaluationResult.task_id == task_id
        ).offset(offset)

        if limit:
            query = query.limit(limit)

        results = []
        for result in query:
            results.append({
                "id": result.id,
                "sample_id": result.sample_id,
                "retrieved_contexts": json.loads(result.retrieved_contexts or "[]"),
                "generated_answer": result.generated_answer,
                "metrics": {
                    "faithfulness": result.faithfulness,
                    "answer_relevancy": result.answer_relevancy,
                    "context_precision": result.context_precision,
                    "context_recall": result.context_recall,
                    "context_relevancy": result.context_relevancy,
                    "answer_correctness": result.answer_correctness,
                    "answer_similarity": result.answer_similarity
                },
                "execution_time": result.execution_time,
                "error_message": result.error_message,
                "create_time": result.create_time
            })

        return results

    def get_task_report(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务的评估报告"""
        try:
            report = EvaluationReport.get(EvaluationReport.task_id == task_id)
            return {
                "id": report.id,
                "task_id": report.task_id,
                "summary": {
                    "avg_faithfulness": report.avg_faithfulness,
                    "avg_answer_relevancy": report.avg_answer_relevancy,
                    "avg_context_precision": report.avg_context_precision,
                    "avg_context_recall": report.avg_context_recall,
                    "avg_context_relevancy": report.avg_context_relevancy,
                    "avg_answer_correctness": report.avg_answer_correctness,
                    "avg_answer_similarity": report.avg_answer_similarity,
                    "total_samples": report.total_samples,
                    "successful_samples": report.successful_samples,
                    "failed_samples": report.failed_samples,
                    "avg_execution_time": report.avg_execution_time
                },
                "detailed_report": json.loads(report.detailed_report or "{}"),
                "create_time": report.create_time
            }
        except EvaluationReport.DoesNotExist:
            return None

    def delete_task(self, task_id: str) -> bool:
        """删除评估任务及其相关数据"""
        try:
            # 停止正在运行的任务
            if task_id in self._running_tasks:
                self.stop_task(task_id)

            # 删除评估结果
            EvaluationResult.delete().where(EvaluationResult.task_id == task_id).execute()

            # 删除评估报告
            EvaluationReport.delete().where(EvaluationReport.task_id == task_id).execute()

            # 删除任务
            task = self.get_task(task_id)
            if task:
                task.delete_instance()

            logging.info(f"删除评估任务: {task_id}")
            return True
        except Exception as e:
            logging.error(f"删除任务失败: {e}")
            return False