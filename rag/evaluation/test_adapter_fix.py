#!/usr/bin/env python3
"""
测试Ragas适配器接口修复
验证接口类型匹配问题的修复
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_llm_adapter_prompt_handling():
    """测试LLM适配器的prompt处理"""
    logger.info("测试LLM适配器的prompt处理...")
    
    try:
        from rag.evaluation.ragas_adapter import RAGFlowLLMAdapter
        
        # 模拟LLMBundle
        class MockLLMBundle:
            def chat(self, system, history, gen_conf):
                return {"content": f"响应: {system[:50]}..."}
        
        adapter = RAGFlowLLMAdapter(MockLLMBundle())
        
        # 模拟LangChain的StringPromptValue
        class MockStringPromptValue:
            def __init__(self, text):
                self.text = text
        
        # 测试不同类型的prompt输入
        test_cases = [
            # 字符串列表
            ["测试提示1", "测试提示2"],
            # 单个字符串
            "单个测试提示",
            # StringPromptValue对象
            MockStringPromptValue("StringPromptValue测试"),
            # StringPromptValue列表
            [MockStringPromptValue("提示1"), MockStringPromptValue("提示2")],
        ]
        
        for i, prompts in enumerate(test_cases):
            logger.info(f"测试用例 {i+1}: {type(prompts)} - {prompts}")
            try:
                results = adapter.generate(prompts)
                logger.info(f"✅ 结果: {results}")
            except Exception as e:
                logger.error(f"❌ 失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ LLM适配器测试失败: {e}")
        return False


def test_embedding_adapter_batch_processing():
    """测试Embedding适配器的批量处理"""
    logger.info("测试Embedding适配器的批量处理...")
    
    try:
        from rag.evaluation.ragas_adapter import RAGFlowEmbeddingAdapter
        
        # 模拟LLMBundle
        class MockEmbeddingBundle:
            def encode_queries(self, text):
                # 模拟返回embedding向量
                if isinstance(text, str):
                    return [0.1, 0.2, 0.3], None
                else:
                    raise TypeError(f"Expected str, got {type(text)}")
        
        adapter = RAGFlowEmbeddingAdapter(MockEmbeddingBundle())
        
        # 测试单个文本嵌入
        try:
            result = adapter.embed_query("测试文本")
            logger.info(f"✅ 单个嵌入结果: {result}")
        except Exception as e:
            logger.error(f"❌ 单个嵌入失败: {e}")
            return False
        
        # 测试批量文本嵌入
        try:
            texts = ["文本1", "文本2", "文本3"]
            results = adapter.embed_documents(texts)
            logger.info(f"✅ 批量嵌入结果: {len(results)} 个向量")
            for i, result in enumerate(results):
                logger.info(f"  向量 {i+1}: {result}")
        except Exception as e:
            logger.error(f"❌ 批量嵌入失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Embedding适配器测试失败: {e}")
        return False


def test_interface_compatibility():
    """测试接口兼容性"""
    logger.info("测试接口兼容性...")
    
    try:
        # 检查Ragas基类
        from rag.evaluation.ragas_adapter import BaseRagasLLM, BaseRagasEmbeddings
        
        logger.info(f"✅ BaseRagasLLM: {BaseRagasLLM}")
        logger.info(f"✅ BaseRagasEmbeddings: {BaseRagasEmbeddings}")
        
        # 检查我们的适配器是否正确继承
        from rag.evaluation.ragas_adapter import RAGFlowLLMAdapter, RAGFlowEmbeddingAdapter
        
        logger.info(f"✅ RAGFlowLLMAdapter 继承自: {RAGFlowLLMAdapter.__bases__}")
        logger.info(f"✅ RAGFlowEmbeddingAdapter 继承自: {RAGFlowEmbeddingAdapter.__bases__}")
        
        # 检查必需的方法
        llm_methods = ['generate', 'agenerate', 'generate_text']
        embedding_methods = ['embed_query', 'embed_documents', 'aembed_query', 'aembed_documents']
        
        for method in llm_methods:
            if hasattr(RAGFlowLLMAdapter, method):
                logger.info(f"✅ RAGFlowLLMAdapter.{method} 存在")
            else:
                logger.error(f"❌ RAGFlowLLMAdapter.{method} 缺失")
                return False
        
        for method in embedding_methods:
            if hasattr(RAGFlowEmbeddingAdapter, method):
                logger.info(f"✅ RAGFlowEmbeddingAdapter.{method} 存在")
            else:
                logger.error(f"❌ RAGFlowEmbeddingAdapter.{method} 缺失")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 接口兼容性测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    logger.info("测试错误处理...")
    
    try:
        from rag.evaluation.ragas_adapter import RAGFlowLLMAdapter, RAGFlowEmbeddingAdapter
        
        # 模拟会出错的LLMBundle
        class ErrorLLMBundle:
            def chat(self, system, history, gen_conf):
                raise Exception("模拟LLM错误")
            
            def encode_queries(self, text):
                raise Exception("模拟Embedding错误")
        
        error_bundle = ErrorLLMBundle()
        
        # 测试LLM错误处理
        llm_adapter = RAGFlowLLMAdapter(error_bundle)
        result = llm_adapter.generate_text("测试")
        logger.info(f"✅ LLM错误处理: {result}")
        
        # 测试Embedding错误处理
        emb_adapter = RAGFlowEmbeddingAdapter(error_bundle)
        result = emb_adapter.embed_query("测试")
        logger.info(f"✅ Embedding错误处理: {len(result)} 维向量")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 错误处理测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始Ragas适配器接口修复测试")
    
    tests = [
        ("LLM适配器prompt处理", test_llm_adapter_prompt_handling),
        ("Embedding适配器批量处理", test_embedding_adapter_batch_processing),
        ("接口兼容性", test_interface_compatibility),
        ("错误处理", test_error_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 测试: {test_name}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 Ragas适配器接口修复验证成功！")
        logger.info("主要修复点:")
        logger.info("1. ✅ 修复了generate()方法的prompt类型处理")
        logger.info("2. ✅ 修复了embed_documents()的批量处理")
        logger.info("3. ✅ 支持StringPromptValue对象")
        logger.info("4. ✅ 改进了错误处理")
        return True
    else:
        logger.error("⚠️  部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
