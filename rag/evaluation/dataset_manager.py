"""
评估数据集管理器
用于管理评估数据集和样本的创建、导入、导出等操作
"""

import json
import csv
import logging
from typing import List, Dict, Any, Optional, Union
from io import StringIO
import pandas as pd

from api.db.db_models import EvaluationDataset, EvaluationSample
from api.utils import get_uuid
from rag.evaluation.ragas_evaluator import EvaluationSample as RagasEvaluationSample


class DatasetManager:
    """评估数据集管理器"""
    
    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id
    
    def create_dataset(self, name: str, kb_id: str, created_by: str, 
                      description: Optional[str] = None, dataset_type: str = "qa_pairs") -> str:
        """创建评估数据集"""
        dataset_id = get_uuid()
        
        dataset = EvaluationDataset.create(
            id=dataset_id,
            name=name,
            description=description,
            kb_id=kb_id,
            tenant_id=self.tenant_id,
            created_by=created_by,
            dataset_type=dataset_type,
            status="active",
            total_samples=0
        )
        
        logging.info(f"创建评估数据集: {dataset_id}, 名称: {name}")
        return dataset_id
    
    def get_dataset(self, dataset_id: str) -> Optional[EvaluationDataset]:
        """获取数据集信息"""
        try:
            return EvaluationDataset.get(
                EvaluationDataset.id == dataset_id,
                EvaluationDataset.tenant_id == self.tenant_id
            )
        except EvaluationDataset.DoesNotExist:
            return None
    
    def list_datasets(self, kb_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """列出数据集"""
        query = EvaluationDataset.select().where(
            EvaluationDataset.tenant_id == self.tenant_id,
            EvaluationDataset.status == "active"
        )
        
        if kb_id:
            query = query.where(EvaluationDataset.kb_id == kb_id)
        
        datasets = []
        for dataset in query:
            datasets.append({
                "id": dataset.id,
                "name": dataset.name,
                "description": dataset.description,
                "kb_id": dataset.kb_id,
                "dataset_type": dataset.dataset_type,
                "total_samples": dataset.total_samples,
                "created_by": dataset.created_by,
                "create_time": dataset.create_time,
                "update_time": dataset.update_time
            })
        
        return datasets
    
    def delete_dataset(self, dataset_id: str) -> bool:
        """删除数据集（软删除）"""
        try:
            dataset = self.get_dataset(dataset_id)
            if not dataset:
                return False
            
            # 软删除数据集
            dataset.status = "archived"
            dataset.save()
            
            logging.info(f"删除评估数据集: {dataset_id}")
            return True
        except Exception as e:
            logging.error(f"删除数据集失败: {e}")
            return False
    
    def add_sample(self, dataset_id: str, question: str, ground_truth: Optional[str] = None,
                   contexts: Optional[List[str]] = None, metadata: Optional[Dict[str, Any]] = None) -> str:
        """添加评估样本"""
        dataset = self.get_dataset(dataset_id)
        if not dataset:
            raise ValueError(f"数据集 {dataset_id} 不存在")
        
        sample_id = get_uuid()
        
        # 序列化contexts和metadata
        contexts_json = json.dumps(contexts, ensure_ascii=False) if contexts else None
        metadata_json = json.dumps(metadata, ensure_ascii=False) if metadata else None
        
        EvaluationSample.create(
            id=sample_id,
            dataset_id=dataset_id,
            question=question,
            ground_truth=ground_truth,
            contexts=contexts_json,
            metadata=metadata_json
        )
        
        # 更新数据集样本数量
        dataset.total_samples = EvaluationSample.select().where(
            EvaluationSample.dataset_id == dataset_id
        ).count()
        dataset.save()
        
        logging.info(f"添加评估样本: {sample_id} 到数据集: {dataset_id}")
        return sample_id
    
    def get_samples(self, dataset_id: str, limit: Optional[int] = None, offset: int = 0) -> List[RagasEvaluationSample]:
        """获取数据集中的样本"""
        query = EvaluationSample.select().where(
            EvaluationSample.dataset_id == dataset_id
        ).offset(offset)
        
        if limit:
            query = query.limit(limit)
        
        samples = []
        for sample in query:
            # 反序列化contexts和metadata
            contexts = json.loads(sample.contexts) if sample.contexts else None
            metadata = json.loads(sample.metadata) if sample.metadata else None
            
            samples.append(RagasEvaluationSample(
                question=sample.question,
                ground_truth=sample.ground_truth,
                contexts=contexts,
                metadata=metadata
            ))
        
        return samples
    
    def import_from_csv(self, dataset_id: str, csv_content: str, 
                       question_col: str = "question", 
                       ground_truth_col: str = "ground_truth",
                       contexts_col: Optional[str] = None) -> int:
        """从CSV导入样本"""
        dataset = self.get_dataset(dataset_id)
        if not dataset:
            raise ValueError(f"数据集 {dataset_id} 不存在")
        
        # 解析CSV
        csv_file = StringIO(csv_content)
        df = pd.read_csv(csv_file)
        
        imported_count = 0
        for _, row in df.iterrows():
            try:
                question = str(row[question_col]).strip()
                if not question or question == 'nan':
                    continue
                
                ground_truth = None
                if ground_truth_col in row and pd.notna(row[ground_truth_col]):
                    ground_truth = str(row[ground_truth_col]).strip()
                
                contexts = None
                if contexts_col and contexts_col in row and pd.notna(row[contexts_col]):
                    # 假设contexts是用分隔符分隔的字符串
                    contexts_str = str(row[contexts_col]).strip()
                    contexts = [ctx.strip() for ctx in contexts_str.split('\n') if ctx.strip()]
                
                self.add_sample(
                    dataset_id=dataset_id,
                    question=question,
                    ground_truth=ground_truth,
                    contexts=contexts
                )
                imported_count += 1
                
            except Exception as e:
                logging.warning(f"导入样本失败: {e}, 行数据: {row.to_dict()}")
                continue
        
        logging.info(f"从CSV导入 {imported_count} 个样本到数据集 {dataset_id}")
        return imported_count
    
    def import_from_json(self, dataset_id: str, json_content: str) -> int:
        """从JSON导入样本"""
        dataset = self.get_dataset(dataset_id)
        if not dataset:
            raise ValueError(f"数据集 {dataset_id} 不存在")
        
        try:
            data = json.loads(json_content)
            if not isinstance(data, list):
                raise ValueError("JSON数据应该是一个数组")
            
            imported_count = 0
            for item in data:
                if not isinstance(item, dict) or "question" not in item:
                    continue
                
                question = item["question"].strip()
                if not question:
                    continue
                
                ground_truth = item.get("ground_truth")
                contexts = item.get("contexts")
                metadata = item.get("metadata")
                
                self.add_sample(
                    dataset_id=dataset_id,
                    question=question,
                    ground_truth=ground_truth,
                    contexts=contexts,
                    metadata=metadata
                )
                imported_count += 1
            
            logging.info(f"从JSON导入 {imported_count} 个样本到数据集 {dataset_id}")
            return imported_count
            
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON格式错误: {e}")
    
    def export_to_csv(self, dataset_id: str) -> str:
        """导出数据集为CSV"""
        samples = EvaluationSample.select().where(
            EvaluationSample.dataset_id == dataset_id
        )
        
        output = StringIO()
        writer = csv.writer(output)
        
        # 写入表头
        writer.writerow(["question", "ground_truth", "contexts", "metadata"])
        
        # 写入数据
        for sample in samples:
            contexts_str = ""
            if sample.contexts:
                try:
                    contexts = json.loads(sample.contexts)
                    contexts_str = "\n".join(contexts) if contexts else ""
                except:
                    contexts_str = sample.contexts
            
            metadata_str = sample.metadata or ""
            
            writer.writerow([
                sample.question,
                sample.ground_truth or "",
                contexts_str,
                metadata_str
            ])
        
        return output.getvalue()
    
    def export_to_json(self, dataset_id: str) -> str:
        """导出数据集为JSON"""
        samples = EvaluationSample.select().where(
            EvaluationSample.dataset_id == dataset_id
        )
        
        data = []
        for sample in samples:
            item = {
                "question": sample.question,
                "ground_truth": sample.ground_truth
            }
            
            if sample.contexts:
                try:
                    item["contexts"] = json.loads(sample.contexts)
                except:
                    item["contexts"] = [sample.contexts]
            
            if sample.metadata:
                try:
                    item["metadata"] = json.loads(sample.metadata)
                except:
                    item["metadata"] = {"raw": sample.metadata}
            
            data.append(item)
        
        return json.dumps(data, ensure_ascii=False, indent=2)
    
    def get_sample_count(self, dataset_id: str) -> int:
        """获取数据集样本数量"""
        return EvaluationSample.select().where(
            EvaluationSample.dataset_id == dataset_id
        ).count()
    
    def delete_sample(self, sample_id: str) -> bool:
        """删除样本"""
        try:
            sample = EvaluationSample.get(EvaluationSample.id == sample_id)
            dataset_id = sample.dataset_id
            sample.delete_instance()
            
            # 更新数据集样本数量
            dataset = self.get_dataset(dataset_id)
            if dataset:
                dataset.total_samples = self.get_sample_count(dataset_id)
                dataset.save()
            
            logging.info(f"删除评估样本: {sample_id}")
            return True
        except EvaluationSample.DoesNotExist:
            return False
        except Exception as e:
            logging.error(f"删除样本失败: {e}")
            return False 