#!/usr/bin/env python3
"""
测试异步方法修复
验证Ragas适配器的异步方法实现
"""

import sys
import os
import logging
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_llm_adapter_async_methods():
    """测试LLM适配器的异步方法"""
    logger.info("测试LLM适配器的异步方法...")
    
    try:
        from rag.evaluation.ragas_adapter import RAGFlowLLMAdapter
        
        # 模拟LLMBundle
        class MockLLMBundle:
            def chat(self, system, history, gen_conf):
                return {"content": f"异步响应: {system[:30]}..."}
        
        adapter = RAGFlowLLMAdapter(MockLLMBundle())
        
        # 测试异步单个文本生成
        logger.info("测试agenerate_text方法...")
        result = await adapter.agenerate_text("测试异步提示")
        logger.info(f"✅ agenerate_text结果: {result}")
        
        # 测试异步批量生成
        logger.info("测试agenerate方法...")
        
        # 模拟StringPromptValue
        class MockPromptValue:
            def __init__(self, text):
                self.text = text
        
        # 测试不同类型的prompts
        test_cases = [
            ["提示1", "提示2"],  # 字符串列表
            "单个提示",  # 单个字符串
            MockPromptValue("PromptValue提示"),  # 单个PromptValue
        ]
        
        for i, prompts in enumerate(test_cases):
            logger.info(f"测试用例 {i+1}: {type(prompts)}")
            results = await adapter.agenerate(prompts)
            logger.info(f"✅ agenerate结果: {results}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ LLM适配器异步方法测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


async def test_embedding_adapter_async_methods():
    """测试Embedding适配器的异步方法"""
    logger.info("测试Embedding适配器的异步方法...")
    
    try:
        from rag.evaluation.ragas_adapter import RAGFlowEmbeddingAdapter
        import numpy as np
        
        # 模拟LLMBundle
        class MockEmbeddingBundle:
            def encode_queries(self, text):
                # 返回numpy数组模拟真实情况
                return np.array([0.1, 0.2, 0.3]), None
        
        adapter = RAGFlowEmbeddingAdapter(MockEmbeddingBundle())
        
        # 测试异步单个查询嵌入
        logger.info("测试aembed_query方法...")
        result = await adapter.aembed_query("测试文本")
        logger.info(f"✅ aembed_query结果: {type(result)} - {result}")
        
        # 验证结果类型
        if isinstance(result, list) and all(isinstance(x, float) for x in result):
            logger.info("  ✓ 类型正确: List[float]")
        else:
            logger.error(f"  ❌ 类型错误: {[type(x) for x in result]}")
            return False
        
        # 测试异步批量文档嵌入
        logger.info("测试aembed_documents方法...")
        texts = ["文本1", "文本2", "文本3"]
        results = await adapter.aembed_documents(texts)
        logger.info(f"✅ aembed_documents结果: {len(results)} 个向量")
        
        # 验证结果类型
        if (isinstance(results, list) and 
            all(isinstance(vec, list) for vec in results) and
            all(all(isinstance(x, float) for x in vec) for vec in results)):
            logger.info("  ✓ 类型正确: List[List[float]]")
        else:
            logger.error("  ❌ 类型错误")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Embedding适配器异步方法测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


async def test_concurrent_execution():
    """测试并发执行"""
    logger.info("测试并发执行...")
    
    try:
        from rag.evaluation.ragas_adapter import RAGFlowLLMAdapter, RAGFlowEmbeddingAdapter
        import numpy as np
        
        # 模拟LLMBundle
        class MockLLMBundle:
            def chat(self, system, history, gen_conf):
                import time
                time.sleep(0.1)  # 模拟网络延迟
                return {"content": f"并发响应: {system[:20]}..."}
        
        class MockEmbeddingBundle:
            def encode_queries(self, text):
                import time
                time.sleep(0.1)  # 模拟网络延迟
                return np.array([0.1, 0.2, 0.3]), None
        
        llm_adapter = RAGFlowLLMAdapter(MockLLMBundle())
        emb_adapter = RAGFlowEmbeddingAdapter(MockEmbeddingBundle())
        
        # 并发执行多个任务
        tasks = [
            llm_adapter.agenerate_text("提示1"),
            llm_adapter.agenerate_text("提示2"),
            emb_adapter.aembed_query("文本1"),
            emb_adapter.aembed_query("文本2"),
        ]
        
        start_time = asyncio.get_event_loop().time()
        results = await asyncio.gather(*tasks)
        end_time = asyncio.get_event_loop().time()
        
        logger.info(f"✅ 并发执行完成，耗时: {end_time - start_time:.2f}秒")
        logger.info(f"✅ 结果数量: {len(results)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 并发执行测试失败: {e}")
        return False


async def test_error_handling():
    """测试异步错误处理"""
    logger.info("测试异步错误处理...")
    
    try:
        from rag.evaluation.ragas_adapter import RAGFlowLLMAdapter, RAGFlowEmbeddingAdapter
        
        # 模拟会出错的LLMBundle
        class ErrorLLMBundle:
            def chat(self, system, history, gen_conf):
                raise Exception("模拟LLM错误")
            
            def encode_queries(self, text):
                raise Exception("模拟Embedding错误")
        
        error_bundle = ErrorLLMBundle()
        
        # 测试LLM异步错误处理
        llm_adapter = RAGFlowLLMAdapter(error_bundle)
        try:
            result = await llm_adapter.agenerate_text("测试")
            logger.info(f"✅ LLM异步错误处理: {result}")
        except Exception as e:
            logger.info(f"✅ LLM异步错误被正确捕获: {e}")
        
        # 测试Embedding异步错误处理
        emb_adapter = RAGFlowEmbeddingAdapter(error_bundle)
        try:
            result = await emb_adapter.aembed_query("测试")
            logger.info(f"✅ Embedding异步错误处理: {len(result)} 维向量")
        except Exception as e:
            logger.info(f"✅ Embedding异步错误被正确捕获: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 异步错误处理测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    logger.info("🚀 开始异步方法修复测试")
    
    tests = [
        ("LLM适配器异步方法", test_llm_adapter_async_methods),
        ("Embedding适配器异步方法", test_embedding_adapter_async_methods),
        ("并发执行", test_concurrent_execution),
        ("异步错误处理", test_error_handling),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 测试: {test_name}")
        try:
            if await test_func():
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 异步方法修复验证成功！")
        logger.info("主要修复点:")
        logger.info("1. ✅ 添加了缺失的agenerate_text方法")
        logger.info("2. ✅ 修复了异步方法的实现")
        logger.info("3. ✅ 确保异步方法返回正确的类型")
        logger.info("4. ✅ 改进了错误处理")
        
        logger.info("\n🔧 修复内容:")
        logger.info("- agenerate_text()方法现在正确实现")
        logger.info("- 所有异步方法使用run_in_executor处理同步调用")
        logger.info("- 保持了类型安全和错误处理")
        
        return True
    else:
        logger.error("⚠️  部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
