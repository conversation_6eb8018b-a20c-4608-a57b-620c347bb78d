"""
Ragas适配器
用于将RAGFlow的LLM和Embedding模型适配到Ragas框架
"""

import logging
from typing import List, Dict, Any, Optional
import asyncio
import inspect

try:
    from ragas.llms.base import BaseRagasLLM
    from ragas.embeddings.base import BaseRagasEmbeddings
    RAGAS_AVAILABLE = True
except ImportError:
    logging.warning("Ragas not available")
    RAGAS_AVAILABLE = False
    # 创建占位符基类
    class BaseRagasLLM:
        pass
    class BaseRagasEmbeddings:
        pass

from api.db.services.llm_service import LLMBundle


class RAGFlowLLMAdapter(BaseRagasLLM):
    """RAGFlow LLM适配器"""

    def __init__(self, llm_bundle: LLMBundle):
        self.llm_bundle = llm_bundle
        self.call_count = 0  # 调用计数器

    def generate_text(self, prompt: str, **kwargs) -> str:
        """生成文本"""
        self.call_count += 1
        logging.info(f"[RAGFlowLLMAdapter] generate_text调用 #{self.call_count}: prompt长度={len(prompt)}")
        try:
            # LLMBundle.chat需要三个参数: system, history, gen_conf
            gen_conf = {
                "temperature": kwargs.get("temperature", 0.1),
                "max_tokens": kwargs.get("max_tokens", 1024),
                "top_p": kwargs.get("top_p", 0.9)
            }
            # 只有当值大于0时才添加penalty参数
            if kwargs.get("presence_penalty", 0) > 0:
                gen_conf["presence_penalty"] = kwargs.get("presence_penalty")
            if kwargs.get("frequency_penalty", 0) > 0:
                gen_conf["frequency_penalty"] = kwargs.get("frequency_penalty")

            response = self.llm_bundle.chat(prompt, [], gen_conf)

            # 处理不同类型的响应
            if isinstance(response, dict):
                return response.get("content", str(response))
            elif isinstance(response, str):
                return response
            else:
                return str(response)

        except Exception as e:
            logging.error(f"LLM生成失败: {e}")
            return "生成失败"

    def generate(self, prompts, **kwargs) -> List[str]:
        """批量生成文本"""
        self.call_count += 1
        logging.info(f"[RAGFlowLLMAdapter] generate调用 #{self.call_count}: prompts类型={type(prompts)}")
        results = []

        # 处理不同类型的prompts输入
        if isinstance(prompts, list):
            prompt_list = prompts
        else:
            # 如果是单个prompt对象，转换为列表
            prompt_list = [prompts]

        for prompt in prompt_list:
            # 处理不同类型的prompt
            if hasattr(prompt, 'text'):
                # LangChain的PromptValue对象
                prompt_text = prompt.text
            elif isinstance(prompt, str):
                prompt_text = prompt
            else:
                prompt_text = str(prompt)

            result = self.generate_text(prompt_text, **kwargs)
            results.append(result)

        return results

    async def agenerate_text(self, prompt: str, **kwargs) -> str:
        """异步生成文本"""
        self.call_count += 1
        logging.info(f"[RAGFlowLLMAdapter] agenerate_text调用 #{self.call_count}: prompt长度={len(prompt)}, kwargs={kwargs}")

        try:
            # 由于LLMBundle是同步的，我们在线程池中运行
            loop = asyncio.get_event_loop()
            logging.info(f"[RAGFlowLLMAdapter] 获取事件循环: {type(loop)}")

            logging.info(f"[RAGFlowLLMAdapter] 开始在线程池中执行generate_text")
            result = await loop.run_in_executor(None, lambda: self.generate_text(prompt, **kwargs))
            logging.info(f"[RAGFlowLLMAdapter] generate_text完成，结果长度={len(result)}")
            return result
        except Exception as e:
            logging.error(f"[RAGFlowLLMAdapter] agenerate_text失败: {e}")
            import traceback
            logging.error(f"[RAGFlowLLMAdapter] 详细错误: {traceback.format_exc()}")
            raise

    async def agenerate(self, prompts, **kwargs) -> List[str]:
        """异步批量生成文本"""
        self.call_count += 1
        logging.info(f"[RAGFlowLLMAdapter] agenerate调用 #{self.call_count}: prompts类型={type(prompts)}, 内容={prompts}")

        # 处理不同类型的prompts输入
        if isinstance(prompts, list):
            prompt_list = prompts
            logging.info(f"[RAGFlowLLMAdapter] 处理列表prompts，长度={len(prompt_list)}")
        else:
            prompt_list = [prompts]
            logging.info(f"[RAGFlowLLMAdapter] 处理单个prompt，转换为列表")

        tasks = []
        for i, prompt in enumerate(prompt_list):
            # 处理不同类型的prompt
            if hasattr(prompt, 'text'):
                prompt_text = prompt.text
                logging.info(f"[RAGFlowLLMAdapter] Prompt {i}: PromptValue对象，text={prompt_text[:50]}...")
            elif isinstance(prompt, str):
                prompt_text = prompt
                logging.info(f"[RAGFlowLLMAdapter] Prompt {i}: 字符串，内容={prompt_text[:50]}...")
            else:
                prompt_text = str(prompt)
                logging.info(f"[RAGFlowLLMAdapter] Prompt {i}: 其他类型{type(prompt)}，转换为字符串")

            logging.info(f"[RAGFlowLLMAdapter] 创建异步任务 {i}: agenerate_text")
            task = self.agenerate_text(prompt_text, **kwargs)
            logging.info(f"[RAGFlowLLMAdapter] 任务 {i} 类型: {type(task)}")

            # 确保task是协程
            if not inspect.iscoroutine(task):
                logging.error(f"[RAGFlowLLMAdapter] 警告: agenerate_text没有返回协程，而是返回了 {type(task)}")
                # 如果不是协程，创建一个简单的协程包装
                async def wrapper():
                    return task
                task = wrapper()

            tasks.append(task)

        logging.info(f"[RAGFlowLLMAdapter] 开始执行 {len(tasks)} 个异步任务")
        try:
            results = await asyncio.gather(*tasks)
            logging.info(f"[RAGFlowLLMAdapter] 异步任务完成，结果数量={len(results)}")
            return results
        except Exception as e:
            logging.error(f"[RAGFlowLLMAdapter] 异步任务执行失败: {e}")
            import traceback
            logging.error(f"[RAGFlowLLMAdapter] 详细错误: {traceback.format_exc()}")
            raise


class RAGFlowEmbeddingAdapter(BaseRagasEmbeddings):
    """RAGFlow Embedding适配器"""

    def __init__(self, embedding_bundle: LLMBundle):
        self.embedding_bundle = embedding_bundle

    def embed_query(self, text: str) -> List[float]:
        """嵌入查询文本"""
        try:
            embeddings, _ = self.embedding_bundle.encode_queries(text)

            # 处理不同类型的返回值
            if isinstance(embeddings, list) and len(embeddings) > 0:
                # 如果是嵌套列表，取第一个
                first_embedding = embeddings[0]
                # 如果第一个元素也是列表或numpy数组，继续取第一个
                while isinstance(first_embedding, list) and len(first_embedding) > 0:
                    first_embedding = first_embedding[0]
                    break  # 只取一层嵌套
                # 如果第一个元素是numpy数组，直接使用
                if hasattr(first_embedding, 'tolist'):
                    first_embedding = embeddings[0]  # 使用原始的numpy数组
                else:
                    first_embedding = embeddings  # 使用整个列表
            else:
                first_embedding = embeddings

            # 转换numpy数组为Python列表
            if hasattr(first_embedding, 'tolist'):
                return first_embedding.tolist()
            elif isinstance(first_embedding, list):
                # 确保列表中的元素都是float类型
                try:
                    return [float(x) for x in first_embedding]
                except (ValueError, TypeError):
                    # 如果转换失败，可能是嵌套的numpy数组
                    result = []
                    for item in first_embedding:
                        if hasattr(item, 'tolist'):
                            result.extend(item.tolist())
                        else:
                            result.append(float(item))
                    return result
            else:
                return [float(first_embedding)]

        except Exception as e:
            logging.error(f"Embedding生成失败: {e}")
            return [0.0] * 768  # 返回默认维度的零向量

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """嵌入文档列表"""
        try:
            # LLMBundle.encode_queries期望单个字符串，所以我们需要逐个处理
            results = []
            for text in texts:
                embeddings, _ = self.embedding_bundle.encode_queries(text)

                # 处理不同类型的返回值
                if isinstance(embeddings, list) and len(embeddings) > 0:
                    # 如果返回的是嵌套列表，取第一个
                    if isinstance(embeddings[0], list):
                        embedding_vector = embeddings[0]
                    else:
                        embedding_vector = embeddings
                else:
                    embedding_vector = embeddings

                # 转换numpy数组为Python列表
                if hasattr(embedding_vector, 'tolist'):
                    results.append(embedding_vector.tolist())
                elif isinstance(embedding_vector, list):
                    # 确保列表中的元素都是float类型
                    results.append([float(x) for x in embedding_vector])
                else:
                    results.append([float(embedding_vector)])

            return results
        except Exception as e:
            logging.error(f"批量Embedding生成失败: {e}")
            return [[0.0] * 768] * len(texts)

    async def aembed_query(self, text: str) -> List[float]:
        """异步嵌入查询文本"""
        logging.info(f"[RAGFlowEmbeddingAdapter] aembed_query调用: text长度={len(text)}")
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self.embed_query, text)
            logging.info(f"[RAGFlowEmbeddingAdapter] aembed_query完成，向量维度={len(result)}")
            return result
        except Exception as e:
            logging.error(f"[RAGFlowEmbeddingAdapter] aembed_query失败: {e}")
            raise

    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
        """异步嵌入文档列表"""
        logging.info(f"[RAGFlowEmbeddingAdapter] aembed_documents调用: texts数量={len(texts)}")
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, self.embed_documents, texts)
            logging.info(f"[RAGFlowEmbeddingAdapter] aembed_documents完成，向量数量={len(result)}")
            return result
        except Exception as e:
            logging.error(f"[RAGFlowEmbeddingAdapter] aembed_documents失败: {e}")
            raise

    def embed_text(self, text: str) -> List[float]:
        """嵌入单个文本 - Ragas可能需要的方法"""
        logging.info(f"[RAGFlowEmbeddingAdapter] embed_text调用: text长度={len(text)}")
        return self.embed_query(text)

    async def aembed_text(self, text: str) -> List[float]:
        """异步嵌入单个文本 - Ragas可能需要的方法"""
        logging.info(f"[RAGFlowEmbeddingAdapter] aembed_text调用: text长度={len(text)}")
        return await self.aembed_query(text)


def create_ragas_models(llm_bundle: LLMBundle, embedding_bundle: LLMBundle):
    """创建Ragas兼容的模型"""
    if not RAGAS_AVAILABLE:
        raise ImportError("Ragas not available")

    llm_adapter = RAGFlowLLMAdapter(llm_bundle)
    embedding_adapter = RAGFlowEmbeddingAdapter(embedding_bundle)

    return llm_adapter, embedding_adapter


def setup_ragas_models(llm_bundle: LLMBundle, embedding_bundle: LLMBundle):
    """设置Ragas模型"""
    try:
        # 直接创建适配器，不依赖全局设置
        llm_adapter, embedding_adapter = create_ragas_models(llm_bundle, embedding_bundle)

        # 尝试设置全局模型（如果Ragas支持）
        try:
            from ragas import settings as ragas_settings
            if hasattr(ragas_settings, 'llm'):
                ragas_settings.llm = llm_adapter
            if hasattr(ragas_settings, 'embeddings'):
                ragas_settings.embeddings = embedding_adapter
            logging.info("Ragas全局模型设置完成")
        except ImportError:
            logging.info("Ragas不支持全局设置，将在evaluate()中直接传递模型")
        except Exception as e:
            logging.warning(f"设置Ragas全局模型失败: {e}")

        logging.info("Ragas模型适配器创建完成")
        return llm_adapter, embedding_adapter

    except Exception as e:
        logging.error(f"创建Ragas模型适配器失败: {e}")
        raise e
