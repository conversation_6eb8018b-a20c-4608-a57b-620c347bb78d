"""
Ragas适配器
用于将RAGFlow的LLM和Embedding模型适配到Ragas框架
"""

import logging
from typing import List, Dict, Any, Optional
import asyncio

try:
    from ragas.llms.base import BaseRagasLLM
    from ragas.embeddings.base import BaseRagasEmbeddings
    RAGAS_AVAILABLE = True
except ImportError:
    logging.warning("Ragas not available")
    RAGAS_AVAILABLE = False
    # 创建占位符基类
    class BaseRagasLLM:
        pass
    class BaseRagasEmbeddings:
        pass

from api.db.services.llm_service import LLMBundle


class RAGFlowLLMAdapter(BaseRagasLLM):
    """RAGFlow LLM适配器"""
    
    def __init__(self, llm_bundle: LLMBundle):
        self.llm_bundle = llm_bundle
        
    def generate_text(self, prompt: str, **kwargs) -> str:
        """生成文本"""
        try:
            response = self.llm_bundle.chat(prompt, [])
            
            # 处理不同类型的响应
            if isinstance(response, dict):
                return response.get("content", str(response))
            elif isinstance(response, str):
                return response
            else:
                return str(response)
                
        except Exception as e:
            logging.error(f"LLM生成失败: {e}")
            return "生成失败"
    
    async def agenerate_text(self, prompt: str, **kwargs) -> str:
        """异步生成文本"""
        # 在线程池中执行同步调用
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.generate_text, prompt)
    
    def generate(self, prompts: List[str], **kwargs) -> List[str]:
        """批量生成文本"""
        return [self.generate_text(prompt, **kwargs) for prompt in prompts]
    
    async def agenerate(self, prompts: List[str], **kwargs) -> List[str]:
        """异步批量生成文本"""
        tasks = [self.agenerate_text(prompt, **kwargs) for prompt in prompts]
        return await asyncio.gather(*tasks)


class RAGFlowEmbeddingAdapter(BaseRagasEmbeddings):
    """RAGFlow Embedding适配器"""
    
    def __init__(self, embedding_bundle: LLMBundle):
        self.embedding_bundle = embedding_bundle
        
    def embed_query(self, text: str) -> List[float]:
        """嵌入查询文本"""
        try:
            embeddings, _ = self.embedding_bundle.encode_queries(text)
            if isinstance(embeddings, list) and len(embeddings) > 0:
                return embeddings[0] if isinstance(embeddings[0], list) else embeddings
            return embeddings
        except Exception as e:
            logging.error(f"Embedding生成失败: {e}")
            return [0.0] * 768  # 返回默认维度的零向量
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """嵌入文档列表"""
        try:
            embeddings, _ = self.embedding_bundle.encode_queries(texts)
            if isinstance(embeddings, list):
                return embeddings
            return [embeddings] * len(texts)
        except Exception as e:
            logging.error(f"批量Embedding生成失败: {e}")
            return [[0.0] * 768] * len(texts)
    
    async def aembed_query(self, text: str) -> List[float]:
        """异步嵌入查询文本"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.embed_query, text)
    
    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
        """异步嵌入文档列表"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.embed_documents, texts)


def create_ragas_models(llm_bundle: LLMBundle, embedding_bundle: LLMBundle):
    """创建Ragas兼容的模型"""
    if not RAGAS_AVAILABLE:
        raise ImportError("Ragas not available")
    
    llm_adapter = RAGFlowLLMAdapter(llm_bundle)
    embedding_adapter = RAGFlowEmbeddingAdapter(embedding_bundle)
    
    return llm_adapter, embedding_adapter


def setup_ragas_models(llm_bundle: LLMBundle, embedding_bundle: LLMBundle):
    """设置Ragas模型（如果需要全局配置）"""
    try:
        from ragas import settings as ragas_settings
        
        llm_adapter, embedding_adapter = create_ragas_models(llm_bundle, embedding_bundle)
        
        # 设置全局模型（如果Ragas支持）
        if hasattr(ragas_settings, 'llm'):
            ragas_settings.llm = llm_adapter
        if hasattr(ragas_settings, 'embeddings'):
            ragas_settings.embeddings = embedding_adapter
            
        logging.info("Ragas模型设置完成")
        return llm_adapter, embedding_adapter
        
    except Exception as e:
        logging.warning(f"设置Ragas全局模型失败: {e}")
        return create_ragas_models(llm_bundle, embedding_bundle)
