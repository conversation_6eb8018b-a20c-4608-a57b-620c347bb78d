#!/usr/bin/env python3
"""
测试Ragas 0.1.9的具体接口
"""

import sys
import os
import logging
import inspect

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_ragas_base_classes():
    """测试Ragas基类"""
    try:
        import ragas
        logger.info(f"Ragas版本: {ragas.__version__}")
        
        # 测试BaseRagasLLM
        try:
            from ragas.llms.base import BaseRagasLLM
            logger.info("✅ BaseRagasLLM 导入成功")
            
            # 检查构造函数签名
            init_sig = inspect.signature(BaseRagasLLM.__init__)
            logger.info(f"BaseRagasLLM.__init__ 签名: {init_sig}")
            
            # 尝试创建实例
            try:
                # 尝试不传参数
                instance = BaseRagasLLM()
                logger.info("✅ BaseRagasLLM() 无参数创建成功")
            except Exception as e:
                logger.info(f"❌ BaseRagasLLM() 无参数创建失败: {e}")
                
                # 尝试传递run_config
                try:
                    from ragas.run_config import RunConfig
                    run_config = RunConfig()
                    instance = BaseRagasLLM(run_config)
                    logger.info("✅ BaseRagasLLM(run_config) 创建成功")
                except Exception as e2:
                    logger.info(f"❌ BaseRagasLLM(run_config) 创建失败: {e2}")
                    
                    # 尝试传递空字典
                    try:
                        instance = BaseRagasLLM({})
                        logger.info("✅ BaseRagasLLM({}) 创建成功")
                    except Exception as e3:
                        logger.info(f"❌ BaseRagasLLM({{}}) 创建失败: {e3}")
            
        except ImportError as e:
            logger.error(f"❌ BaseRagasLLM 导入失败: {e}")
        
        # 测试BaseRagasEmbeddings
        try:
            from ragas.embeddings.base import BaseRagasEmbeddings
            logger.info("✅ BaseRagasEmbeddings 导入成功")
            
            # 检查构造函数签名
            init_sig = inspect.signature(BaseRagasEmbeddings.__init__)
            logger.info(f"BaseRagasEmbeddings.__init__ 签名: {init_sig}")
            
            # 尝试创建实例
            try:
                # 尝试不传参数
                instance = BaseRagasEmbeddings()
                logger.info("✅ BaseRagasEmbeddings() 无参数创建成功")
            except Exception as e:
                logger.info(f"❌ BaseRagasEmbeddings() 无参数创建失败: {e}")
                
                # 尝试传递run_config
                try:
                    from ragas.run_config import RunConfig
                    run_config = RunConfig()
                    instance = BaseRagasEmbeddings(run_config)
                    logger.info("✅ BaseRagasEmbeddings(run_config) 创建成功")
                except Exception as e2:
                    logger.info(f"❌ BaseRagasEmbeddings(run_config) 创建失败: {e2}")
            
        except ImportError as e:
            logger.error(f"❌ BaseRagasEmbeddings 导入失败: {e}")
        
        # 测试RunConfig
        try:
            from ragas.run_config import RunConfig
            logger.info("✅ RunConfig 导入成功")
            
            # 检查构造函数签名
            init_sig = inspect.signature(RunConfig.__init__)
            logger.info(f"RunConfig.__init__ 签名: {init_sig}")
            
            # 尝试创建实例
            run_config = RunConfig()
            logger.info("✅ RunConfig() 创建成功")
            logger.info(f"RunConfig 类型: {type(run_config)}")
            logger.info(f"RunConfig 属性: {dir(run_config)}")
            
        except ImportError as e:
            logger.error(f"❌ RunConfig 导入失败: {e}")
        except Exception as e:
            logger.error(f"❌ RunConfig 创建失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def test_simple_adapter():
    """测试简单的适配器"""
    try:
        from ragas.llms.base import BaseRagasLLM
        
        class SimpleAdapter(BaseRagasLLM):
            def __init__(self):
                # 尝试不同的初始化方式
                try:
                    super().__init__()
                    logger.info("✅ SimpleAdapter: super().__init__() 成功")
                except Exception as e:
                    logger.info(f"❌ SimpleAdapter: super().__init__() 失败: {e}")
                    
                    try:
                        from ragas.run_config import RunConfig
                        run_config = RunConfig()
                        super().__init__(run_config)
                        logger.info("✅ SimpleAdapter: super().__init__(run_config) 成功")
                    except Exception as e2:
                        logger.info(f"❌ SimpleAdapter: super().__init__(run_config) 失败: {e2}")
                        raise e2
        
        # 尝试创建适配器
        adapter = SimpleAdapter()
        logger.info("✅ SimpleAdapter 创建成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ SimpleAdapter 测试失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("🚀 开始测试Ragas 0.1.9接口")
    
    tests = [
        ("Ragas基类测试", test_ragas_base_classes),
        ("简单适配器测试", test_simple_adapter),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 测试: {test_name}")
        try:
            result = test_func()
            if result:
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 Ragas 0.1.9接口测试成功！")
        return True
    else:
        logger.error("⚠️  部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
