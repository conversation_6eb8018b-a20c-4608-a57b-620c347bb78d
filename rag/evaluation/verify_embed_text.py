#!/usr/bin/env python3
"""
验证embed_text方法是否正确添加
"""

import sys
import os
import logging
import inspect

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def verify_embedding_methods():
    """验证embedding方法"""
    logger.info("验证embedding方法...")
    
    try:
        from rag.evaluation.ragas_adapter import RAGFlowEmbeddingAdapter
        
        # 检查所有方法
        methods = [method for method in dir(RAGFlowEmbeddingAdapter) if not method.startswith('_')]
        logger.info("RAGFlowEmbeddingAdapter 方法:")
        for method in methods:
            method_obj = getattr(RAGFlowEmbeddingAdapter, method)
            if callable(method_obj):
                is_async = inspect.iscoroutinefunction(method_obj)
                logger.info(f"  - {method}: {'异步' if is_async else '同步'}")
        
        # 检查关键方法
        required_methods = [
            'embed_query', 'embed_documents', 'embed_text',
            'aembed_query', 'aembed_documents', 'aembed_text'
        ]
        
        logger.info("\n检查必需的方法:")
        all_present = True
        for method in required_methods:
            if hasattr(RAGFlowEmbeddingAdapter, method):
                method_obj = getattr(RAGFlowEmbeddingAdapter, method)
                is_async = inspect.iscoroutinefunction(method_obj)
                logger.info(f"  ✅ {method}: {'异步' if is_async else '同步'}")
            else:
                logger.error(f"  ❌ {method}: 缺失")
                all_present = False
        
        return all_present
        
    except Exception as e:
        logger.error(f"❌ 验证embedding方法失败: {e}")
        return False


def test_embedding_adapter():
    """测试embedding适配器"""
    logger.info("测试embedding适配器...")
    
    try:
        from rag.evaluation.ragas_adapter import RAGFlowEmbeddingAdapter
        import numpy as np
        
        # 模拟LLMBundle
        class MockEmbeddingBundle:
            def encode_queries(self, text):
                # 返回numpy数组模拟真实情况
                return np.array([0.1, 0.2, 0.3]), None
        
        adapter = RAGFlowEmbeddingAdapter(MockEmbeddingBundle())
        
        # 测试embed_text方法
        logger.info("测试embed_text方法...")
        try:
            result = adapter.embed_text("测试文本")
            logger.info(f"✅ embed_text结果: {type(result)} - {result}")
            
            # 验证结果类型
            if isinstance(result, list) and all(isinstance(x, float) for x in result):
                logger.info("  ✓ 类型正确: List[float]")
            else:
                logger.error(f"  ❌ 类型错误: {[type(x) for x in result]}")
                return False
        except Exception as e:
            logger.error(f"❌ embed_text测试失败: {e}")
            return False
        
        # 测试其他方法
        test_methods = [
            ('embed_query', "测试查询"),
            ('embed_documents', ["文档1", "文档2"]),
        ]
        
        for method_name, test_input in test_methods:
            logger.info(f"测试{method_name}方法...")
            try:
                method = getattr(adapter, method_name)
                result = method(test_input)
                logger.info(f"✅ {method_name}结果: {type(result)}")
            except Exception as e:
                logger.error(f"❌ {method_name}测试失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试embedding适配器失败: {e}")
        return False


def check_ragas_compatibility():
    """检查Ragas兼容性"""
    logger.info("检查Ragas兼容性...")
    
    try:
        # 检查Ragas是否可用
        try:
            from ragas.llms.base import BaseRagasLLM
            from ragas.embeddings.base import BaseRagasEmbeddings
            logger.info("✅ Ragas基类导入成功")
        except ImportError as e:
            logger.error(f"❌ Ragas基类导入失败: {e}")
            return False
        
        # 检查我们的适配器是否正确继承
        from rag.evaluation.ragas_adapter import RAGFlowEmbeddingAdapter
        
        if issubclass(RAGFlowEmbeddingAdapter, BaseRagasEmbeddings):
            logger.info("✅ RAGFlowEmbeddingAdapter 正确继承自 BaseRagasEmbeddings")
        else:
            logger.error("❌ RAGFlowEmbeddingAdapter 继承关系错误")
            return False
        
        # 检查基类的方法要求
        base_methods = [method for method in dir(BaseRagasEmbeddings) if not method.startswith('_')]
        logger.info(f"BaseRagasEmbeddings 方法: {base_methods}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查Ragas兼容性失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("🚀 开始验证embed_text方法修复")
    
    tests = [
        ("embedding方法验证", verify_embedding_methods),
        ("embedding适配器测试", test_embedding_adapter),
        ("Ragas兼容性检查", check_ragas_compatibility),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 {test_name}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} 通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
    
    logger.info(f"\n📊 验证结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 embed_text方法修复验证成功！")
        logger.info("\n主要修复:")
        logger.info("1. ✅ 添加了embed_text方法")
        logger.info("2. ✅ 添加了aembed_text异步方法")
        logger.info("3. ✅ 确保所有必需的embedding方法都存在")
        logger.info("4. ✅ 保持了Ragas兼容性")
        
        logger.info("\n现在Ragas应该能够正确调用所有embedding方法，不再出现:")
        logger.info("AttributeError('NoneType' object has no attribute 'embed_text')")
        
        return True
    else:
        logger.error("⚠️  验证失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
