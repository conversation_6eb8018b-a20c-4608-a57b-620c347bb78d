#!/usr/bin/env python3
"""
测试Ragas接口兼容性
"""

import sys
import os
import logging
import asyncio
import inspect

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_ragas_llm_interface():
    """测试Ragas LLM接口"""
    logger.info("测试Ragas LLM接口...")
    
    try:
        from ragas.llms.base import BaseRagasLLM
        from rag.evaluation.ragas_adapter import RAGFlowLLMAdapter
        
        # 检查基类的方法
        base_methods = [method for method in dir(BaseRagasLLM) if not method.startswith('_')]
        logger.info(f"BaseRagasLLM 方法: {base_methods}")
        
        # 检查基类的异步方法
        async_methods = []
        for method_name in base_methods:
            method = getattr(BaseRagasLLM, method_name)
            if callable(method) and inspect.iscoroutinefunction(method):
                sig = inspect.signature(method)
                async_methods.append((method_name, sig))
        
        logger.info(f"BaseRagasLLM 异步方法: {async_methods}")
        
        # 模拟LLMBundle
        class MockLLMBundle:
            def chat(self, system, history, gen_conf):
                return {"content": f"响应: {system[:30]}..."}
        
        adapter = RAGFlowLLMAdapter(MockLLMBundle())
        
        # 检查我们的适配器方法
        our_methods = [method for method in dir(adapter) if not method.startswith('_')]
        logger.info(f"RAGFlowLLMAdapter 方法: {our_methods}")
        
        # 检查我们的异步方法
        our_async_methods = []
        for method_name in our_methods:
            method = getattr(adapter, method_name)
            if callable(method) and inspect.iscoroutinefunction(method):
                sig = inspect.signature(method)
                our_async_methods.append((method_name, sig))
        
        logger.info(f"RAGFlowLLMAdapter 异步方法: {our_async_methods}")
        
        # 测试关键方法是否存在并且是协程
        key_methods = ['agenerate', 'agenerate_text']
        for method_name in key_methods:
            if hasattr(adapter, method_name):
                method = getattr(adapter, method_name)
                if inspect.iscoroutinefunction(method):
                    logger.info(f"✅ {method_name}: 是协程函数")
                    
                    # 测试调用
                    if method_name == 'agenerate_text':
                        result = await method("测试提示")
                        logger.info(f"✅ {method_name} 调用成功: {result}")
                    elif method_name == 'agenerate':
                        result = await method(["测试提示1", "测试提示2"])
                        logger.info(f"✅ {method_name} 调用成功: {result}")
                else:
                    logger.error(f"❌ {method_name}: 不是协程函数")
                    return False
            else:
                logger.error(f"❌ {method_name}: 方法不存在")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Ragas LLM接口测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


async def test_ragas_embedding_interface():
    """测试Ragas Embedding接口"""
    logger.info("测试Ragas Embedding接口...")
    
    try:
        from ragas.embeddings.base import BaseRagasEmbeddings
        from rag.evaluation.ragas_adapter import RAGFlowEmbeddingAdapter
        import numpy as np
        
        # 检查基类的方法
        base_methods = [method for method in dir(BaseRagasEmbeddings) if not method.startswith('_')]
        logger.info(f"BaseRagasEmbeddings 方法: {base_methods}")
        
        # 检查基类的异步方法
        async_methods = []
        for method_name in base_methods:
            method = getattr(BaseRagasEmbeddings, method_name)
            if callable(method) and inspect.iscoroutinefunction(method):
                sig = inspect.signature(method)
                async_methods.append((method_name, sig))
        
        logger.info(f"BaseRagasEmbeddings 异步方法: {async_methods}")
        
        # 模拟EmbeddingBundle
        class MockEmbeddingBundle:
            def encode_queries(self, text):
                return np.array([0.1, 0.2, 0.3]), None
        
        adapter = RAGFlowEmbeddingAdapter(MockEmbeddingBundle())
        
        # 检查我们的适配器方法
        our_methods = [method for method in dir(adapter) if not method.startswith('_')]
        logger.info(f"RAGFlowEmbeddingAdapter 方法: {our_methods}")
        
        # 检查我们的异步方法
        our_async_methods = []
        for method_name in our_methods:
            method = getattr(adapter, method_name)
            if callable(method) and inspect.iscoroutinefunction(method):
                sig = inspect.signature(method)
                our_async_methods.append((method_name, sig))
        
        logger.info(f"RAGFlowEmbeddingAdapter 异步方法: {our_async_methods}")
        
        # 测试关键方法
        key_methods = ['aembed_query', 'aembed_documents', 'aembed_text']
        for method_name in key_methods:
            if hasattr(adapter, method_name):
                method = getattr(adapter, method_name)
                if inspect.iscoroutinefunction(method):
                    logger.info(f"✅ {method_name}: 是协程函数")
                    
                    # 测试调用
                    if method_name == 'aembed_query' or method_name == 'aembed_text':
                        result = await method("测试文本")
                        logger.info(f"✅ {method_name} 调用成功: {len(result)} 维向量")
                    elif method_name == 'aembed_documents':
                        result = await method(["文本1", "文本2"])
                        logger.info(f"✅ {method_name} 调用成功: {len(result)} 个向量")
                else:
                    logger.error(f"❌ {method_name}: 不是协程函数")
                    return False
            else:
                logger.error(f"❌ {method_name}: 方法不存在")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Ragas Embedding接口测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def test_method_return_types():
    """测试方法返回类型"""
    logger.info("测试方法返回类型...")
    
    try:
        from rag.evaluation.ragas_adapter import RAGFlowLLMAdapter, RAGFlowEmbeddingAdapter
        import numpy as np
        
        # 模拟Bundle
        class MockLLMBundle:
            def chat(self, system, history, gen_conf):
                return {"content": f"响应: {system[:30]}..."}
        
        class MockEmbeddingBundle:
            def encode_queries(self, text):
                return np.array([0.1, 0.2, 0.3]), None
        
        llm_adapter = RAGFlowLLMAdapter(MockLLMBundle())
        emb_adapter = RAGFlowEmbeddingAdapter(MockEmbeddingBundle())
        
        # 测试同步方法返回类型
        logger.info("测试同步方法返回类型:")
        
        # LLM同步方法
        result = llm_adapter.generate(["测试1", "测试2"])
        logger.info(f"generate 返回类型: {type(result)}, 内容: {result}")
        
        result = llm_adapter.generate_text("测试")
        logger.info(f"generate_text 返回类型: {type(result)}, 内容: {result}")
        
        # Embedding同步方法
        result = emb_adapter.embed_query("测试")
        logger.info(f"embed_query 返回类型: {type(result)}, 长度: {len(result)}")
        
        result = emb_adapter.embed_documents(["测试1", "测试2"])
        logger.info(f"embed_documents 返回类型: {type(result)}, 长度: {len(result)}")
        
        # 检查异步方法是否返回协程
        logger.info("检查异步方法是否返回协程:")
        
        coro = llm_adapter.agenerate_text("测试")
        logger.info(f"agenerate_text 返回类型: {type(coro)}, 是否为协程: {inspect.iscoroutine(coro)}")
        coro.close()  # 关闭协程避免警告
        
        coro = llm_adapter.agenerate(["测试"])
        logger.info(f"agenerate 返回类型: {type(coro)}, 是否为协程: {inspect.iscoroutine(coro)}")
        coro.close()
        
        coro = emb_adapter.aembed_query("测试")
        logger.info(f"aembed_query 返回类型: {type(coro)}, 是否为协程: {inspect.iscoroutine(coro)}")
        coro.close()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 方法返回类型测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


async def main():
    """主测试函数"""
    logger.info("🚀 开始Ragas接口兼容性测试")
    
    tests = [
        ("方法返回类型测试", test_method_return_types),
        ("Ragas LLM接口测试", test_ragas_llm_interface),
        ("Ragas Embedding接口测试", test_ragas_embedding_interface),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 Ragas接口兼容性测试成功！")
        logger.info("\n关键发现:")
        logger.info("1. ✅ 所有异步方法都正确返回协程")
        logger.info("2. ✅ 方法签名与Ragas基类兼容")
        logger.info("3. ✅ 返回类型符合预期")
        
        logger.info("\n如果Ragas仍然报错，可能的原因:")
        logger.info("- Ragas内部的异步调用逻辑问题")
        logger.info("- Ragas版本兼容性问题")
        logger.info("- 需要检查Ragas的具体调用栈")
        
        return True
    else:
        logger.error("⚠️  部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
