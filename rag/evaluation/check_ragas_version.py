#!/usr/bin/env python3
"""
检查Ragas版本和接口
"""

import sys
import os
import logging
import inspect

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_ragas_version():
    """检查Ragas版本"""
    try:
        import ragas
        logger.info(f"✅ Ragas版本: {ragas.__version__}")
        return ragas.__version__
    except ImportError as e:
        logger.error(f"❌ Ragas导入失败: {e}")
        return None
    except Exception as e:
        logger.error(f"❌ 检查Ragas版本失败: {e}")
        return None


def check_base_classes():
    """检查基类接口"""
    try:
        from ragas.llms.base import BaseRagasLLM
        from ragas.embeddings.base import BaseRagasEmbeddings
        
        logger.info("✅ Ragas基类导入成功")
        
        # 检查LLM基类
        logger.info("\n📋 BaseRagasLLM 方法:")
        llm_methods = [method for method in dir(BaseRagasLLM) if not method.startswith('_')]
        for method in llm_methods:
            method_obj = getattr(BaseRagasLLM, method)
            if callable(method_obj):
                is_async = inspect.iscoroutinefunction(method_obj)
                try:
                    sig = inspect.signature(method_obj)
                    logger.info(f"  - {method}: {'异步' if is_async else '同步'} {sig}")
                except Exception:
                    logger.info(f"  - {method}: {'异步' if is_async else '同步'} (无法获取签名)")
        
        # 检查Embedding基类
        logger.info("\n📋 BaseRagasEmbeddings 方法:")
        emb_methods = [method for method in dir(BaseRagasEmbeddings) if not method.startswith('_')]
        for method in emb_methods:
            method_obj = getattr(BaseRagasEmbeddings, method)
            if callable(method_obj):
                is_async = inspect.iscoroutinefunction(method_obj)
                try:
                    sig = inspect.signature(method_obj)
                    logger.info(f"  - {method}: {'异步' if is_async else '同步'} {sig}")
                except Exception:
                    logger.info(f"  - {method}: {'异步' if is_async else '同步'} (无法获取签名)")
        
        # 检查基类构造函数
        logger.info("\n📋 基类构造函数:")
        try:
            llm_init_sig = inspect.signature(BaseRagasLLM.__init__)
            logger.info(f"BaseRagasLLM.__init__: {llm_init_sig}")
        except Exception as e:
            logger.error(f"无法获取BaseRagasLLM.__init__签名: {e}")
        
        try:
            emb_init_sig = inspect.signature(BaseRagasEmbeddings.__init__)
            logger.info(f"BaseRagasEmbeddings.__init__: {emb_init_sig}")
        except Exception as e:
            logger.error(f"无法获取BaseRagasEmbeddings.__init__签名: {e}")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Ragas基类导入失败: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 检查基类接口失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def check_evaluate_function():
    """检查evaluate函数接口"""
    try:
        from ragas import evaluate
        
        logger.info("\n📋 evaluate函数:")
        try:
            sig = inspect.signature(evaluate)
            logger.info(f"evaluate签名: {sig}")
        except Exception as e:
            logger.error(f"无法获取evaluate签名: {e}")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ evaluate函数导入失败: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 检查evaluate函数失败: {e}")
        return False


def check_metrics():
    """检查可用的评估指标"""
    try:
        # 尝试导入各种指标
        metrics_to_check = [
            'faithfulness',
            'answer_relevancy', 
            'context_recall',
            'answer_correctness',
            'context_precision',
            'semantic_similarity'
        ]
        
        logger.info("\n📋 评估指标:")
        available_metrics = []
        
        for metric_name in metrics_to_check:
            try:
                # 尝试从不同的模块导入
                try:
                    exec(f"from ragas.metrics import {metric_name}")
                    logger.info(f"  ✅ {metric_name}: 从ragas.metrics导入成功")
                    available_metrics.append(metric_name)
                except ImportError:
                    try:
                        exec(f"from ragas.metrics.{metric_name} import {metric_name}")
                        logger.info(f"  ✅ {metric_name}: 从ragas.metrics.{metric_name}导入成功")
                        available_metrics.append(metric_name)
                    except ImportError:
                        logger.warning(f"  ❌ {metric_name}: 导入失败")
            except Exception as e:
                logger.error(f"  ❌ {metric_name}: 检查失败 - {e}")
        
        logger.info(f"\n可用指标: {available_metrics}")
        return available_metrics
        
    except Exception as e:
        logger.error(f"❌ 检查评估指标失败: {e}")
        return []


def main():
    """主函数"""
    logger.info("🚀 开始检查Ragas版本和接口")
    
    # 检查版本
    version = check_ragas_version()
    if not version:
        logger.error("❌ 无法获取Ragas版本，退出检查")
        return False
    
    # 检查基类
    if not check_base_classes():
        logger.error("❌ 基类检查失败")
        return False
    
    # 检查evaluate函数
    if not check_evaluate_function():
        logger.error("❌ evaluate函数检查失败")
        return False
    
    # 检查指标
    metrics = check_metrics()
    
    logger.info(f"\n🎉 Ragas {version} 接口检查完成")
    logger.info("现在可以根据实际接口创建兼容的适配器")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
