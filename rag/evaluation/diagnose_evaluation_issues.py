#!/usr/bin/env python3
"""
RAG评估问题诊断和修复脚本
用于诊断和修复评估任务中的常见问题
"""

import sys
import os
import logging
import json
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from api.db.db_models import EvaluationTask, EvaluationResult, LLM, Knowledgebase
from api.db.services.llm_service import LLMService
from api.db import LLMType
from rag.evaluation.evaluation_runner import EvaluationRunner

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def diagnose_failed_tasks(tenant_id: str, hours: int = 24):
    """诊断最近失败的评估任务"""
    logger.info(f"诊断租户 {tenant_id} 最近 {hours} 小时内的失败任务...")

    # 查询最近的失败任务
    since_time = datetime.now() - timedelta(hours=hours)
    failed_tasks = EvaluationTask.select().where(
        (EvaluationTask.tenant_id == tenant_id) &
        (EvaluationTask.status == 'failed')
    ).order_by(EvaluationTask.create_time.desc()).limit(10)

    if not failed_tasks:
        logger.info("✅ 没有发现失败的任务")
        return []

    issues = []
    for task in failed_tasks:
        logger.info(f"\n🔍 分析任务: {task.id} - {task.name}")

        # 分析任务配置
        try:
            llm_config = json.loads(task.llm_config or "{}")
            retrieval_config = json.loads(task.retrieval_config or "{}")

            task_issues = []

            # 检查模型配置
            embedding_id = llm_config.get("embedding_id")
            llm_id = llm_config.get("llm_id")
            rerank_id = retrieval_config.get("rerank_id")

            if embedding_id:
                embedding_model = LLM.get_or_none(LLM.id == embedding_id)
                if not embedding_model:
                    task_issues.append(f"Embedding模型不存在: {embedding_id}")
                elif embedding_model.tenant_id != tenant_id:
                    task_issues.append(f"Embedding模型不属于当前租户: {embedding_id}")
                elif embedding_model.status != 1:
                    task_issues.append(f"Embedding模型未启用: {embedding_id}")

            if llm_id:
                llm_model = LLM.get_or_none(LLM.id == llm_id)
                if not llm_model:
                    task_issues.append(f"LLM模型不存在: {llm_id}")
                elif llm_model.tenant_id != tenant_id:
                    task_issues.append(f"LLM模型不属于当前租户: {llm_id}")
                elif llm_model.status != 1:
                    task_issues.append(f"LLM模型未启用: {llm_id}")

            # 检查知识库
            kb = Knowledgebase.get_or_none(
                (Knowledgebase.id == task.kb_id) &
                (Knowledgebase.tenant_id == tenant_id)
            )
            if not kb:
                task_issues.append(f"知识库不存在: {task.kb_id}")

            # 检查评估结果中的错误
            error_results = EvaluationResult.select().where(
                (EvaluationResult.task_id == task.id) &
                (EvaluationResult.error_message.is_null(False))
            )

            error_messages = set()
            for result in error_results:
                if result.error_message:
                    error_messages.add(result.error_message)

            if error_messages:
                task_issues.extend([f"运行时错误: {msg}" for msg in error_messages])

            if task_issues:
                issues.append({
                    "task_id": task.id,
                    "task_name": task.name,
                    "create_time": task.create_time,
                    "issues": task_issues
                })

                logger.error(f"❌ 发现问题:")
                for issue in task_issues:
                    logger.error(f"   - {issue}")
            else:
                logger.info(f"✅ 未发现明显问题")

        except Exception as e:
            logger.error(f"分析任务失败: {e}")

    return issues


def get_model_suggestions(tenant_id: str):
    """获取可用模型建议"""
    logger.info("获取可用模型建议...")

    suggestions = {}

    # 获取可用的Embedding模型
    embedding_models = LLM.select().where(
        (LLM.tenant_id == tenant_id) &
        (LLM.model_type == LLMType.EMBEDDING.value) &
        (LLM.status == 1)
    )

    suggestions["embedding_models"] = []
    for model in embedding_models:
        suggestions["embedding_models"].append({
            "id": model.id,
            "name": model.llm_name,
            "api_base": model.api_base
        })

    # 获取可用的Chat模型
    chat_models = LLM.select().where(
        (LLM.tenant_id == tenant_id) &
        (LLM.model_type == LLMType.CHAT.value) &
        (LLM.status == 1)
    )

    suggestions["chat_models"] = []
    for model in chat_models:
        suggestions["chat_models"].append({
            "id": model.id,
            "name": model.llm_name,
            "api_base": model.api_base
        })

    # 获取可用的Rerank模型
    rerank_models = LLM.select().where(
        (LLM.tenant_id == tenant_id) &
        (LLM.model_type == LLMType.RERANK.value) &
        (LLM.status == 1)
    )

    suggestions["rerank_models"] = []
    for model in rerank_models:
        suggestions["rerank_models"].append({
            "id": model.id,
            "name": model.llm_name,
            "api_base": model.api_base
        })

    return suggestions


def fix_task_configuration(tenant_id: str, task_id: str, new_config: dict):
    """修复任务配置"""
    logger.info(f"修复任务 {task_id} 的配置...")

    try:
        task = EvaluationTask.get(
            (EvaluationTask.id == task_id) &
            (EvaluationTask.tenant_id == tenant_id)
        )

        # 更新配置
        if "llm_config" in new_config:
            task.llm_config = json.dumps(new_config["llm_config"])

        if "retrieval_config" in new_config:
            task.retrieval_config = json.dumps(new_config["retrieval_config"])

        # 重置任务状态
        task.status = "pending"
        task.processed_samples = 0
        task.progress = 0
        task.started_at = None
        task.completed_at = None

        task.save()

        logger.info(f"✅ 任务 {task_id} 配置已修复，状态重置为pending")
        return True

    except EvaluationTask.DoesNotExist:
        logger.error(f"任务 {task_id} 不存在")
        return False
    except Exception as e:
        logger.error(f"修复任务配置失败: {e}")
        return False


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python diagnose_evaluation_issues.py <tenant_id> [hours]")
        print("示例: python diagnose_evaluation_issues.py test_tenant 24")
        sys.exit(1)

    tenant_id = sys.argv[1]
    hours = int(sys.argv[2]) if len(sys.argv) > 2 else 24

    logger.info(f"🔍 开始诊断RAG评估问题")
    logger.info(f"租户ID: {tenant_id}")
    logger.info(f"时间范围: 最近 {hours} 小时")

    # 诊断失败任务
    issues = diagnose_failed_tasks(tenant_id, hours)

    if issues:
        logger.info(f"\n📊 发现 {len(issues)} 个有问题的任务")

        # 获取模型建议
        suggestions = get_model_suggestions(tenant_id)

        logger.info("\n💡 可用模型建议:")
        for model_type, models in suggestions.items():
            if models:
                logger.info(f"  {model_type}:")
                for model in models[:3]:  # 只显示前3个
                    logger.info(f"    - {model['id']}: {model['name']}")

        logger.info("\n🔧 修复建议:")
        logger.info("1. 检查模型是否存在且属于正确的租户")
        logger.info("2. 确保模型状态为启用")
        logger.info("3. 验证API密钥和配置是否正确")
        logger.info("4. 使用上述可用模型替换有问题的模型")

    else:
        logger.info("🎉 没有发现问题，评估功能应该正常工作")

    return len(issues) == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
