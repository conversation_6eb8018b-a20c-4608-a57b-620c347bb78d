#!/usr/bin/env python3
"""
测试修复后的LLM API
验证KnowledgebaseService.get_by_id的返回值处理
"""

import sys
import os
import logging
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from api.db.services.knowledgebase_service import KnowledgebaseService
from api.db.db_models import TenantLLM, LLM, Knowledgebase
from api.db import LLMType

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_knowledgebase_service(kb_id: str):
    """测试KnowledgebaseService.get_by_id方法"""
    logger.info(f"测试KnowledgebaseService.get_by_id: {kb_id}")
    
    try:
        success, kb = KnowledgebaseService.get_by_id(kb_id)
        
        logger.info(f"返回值类型: success={type(success)}, kb={type(kb)}")
        logger.info(f"success: {success}")
        
        if success and kb:
            logger.info(f"知识库信息:")
            logger.info(f"  ID: {kb.id}")
            logger.info(f"  名称: {kb.name}")
            logger.info(f"  Embedding模型: {kb.embd_id}")
            logger.info(f"  租户ID: {kb.tenant_id}")
            
            kb_info = {
                "kb_id": kb_id,
                "embedding_model": kb.embd_id,
                "name": kb.name
            }
            logger.info(f"✅ 知识库信息获取成功: {kb_info}")
            return True, kb_info
        else:
            logger.error(f"❌ 知识库 {kb_id} 不存在或获取失败")
            return False, None
            
    except Exception as e:
        logger.error(f"❌ 测试KnowledgebaseService失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False, None


def test_tenant_llm_query(tenant_id: str):
    """测试租户LLM查询"""
    logger.info(f"测试租户LLM查询: {tenant_id}")
    
    try:
        # 查询租户的Chat类型LLM模型
        query = (TenantLLM
                .select(TenantLLM, LLM)
                .join(LLM, on=(
                    (TenantLLM.llm_factory == LLM.fid) &
                    (TenantLLM.llm_name == LLM.llm_name)
                ))
                .where(
                    (TenantLLM.tenant_id == tenant_id) &
                    (LLM.model_type == LLMType.CHAT.value) &
                    (LLM.status == 1)
                ))
        
        models = []
        for tenant_llm in query:
            model_id = f"{tenant_llm.llm_name}@{tenant_llm.llm_factory}"
            
            model_info = {
                "id": model_id,
                "llm_name": tenant_llm.llm_name,
                "llm_factory": tenant_llm.llm_factory,
                "model_type": tenant_llm.model_type,
                "api_base": tenant_llm.api_base,
                "max_tokens": tenant_llm.max_tokens
            }
            models.append(model_info)
            
            logger.info(f"找到模型: {model_info}")
        
        if models:
            logger.info(f"✅ 成功找到 {len(models)} 个Chat模型")
            return True, models
        else:
            logger.warning("⚠️  没有找到可用的Chat模型")
            return False, []
            
    except Exception as e:
        logger.error(f"❌ 租户LLM查询失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False, []


def simulate_llm_api(tenant_id: str, kb_id: str = None):
    """模拟LLM API完整流程"""
    logger.info("模拟LLM API完整流程...")
    
    try:
        # 1. 获取Chat模型
        success, models = test_tenant_llm_query(tenant_id)
        if not success:
            logger.error("获取Chat模型失败")
            return False
        
        # 2. 获取知识库信息（如果提供了kb_id）
        kb_info = None
        if kb_id:
            kb_success, kb_info = test_knowledgebase_service(kb_id)
            if not kb_success:
                logger.warning("获取知识库信息失败，但继续处理")
        
        # 3. 构造API响应
        api_response = {
            "code": 0,
            "data": {
                "llms": models,
                "kb_info": kb_info
            },
            "message": "success"
        }
        
        logger.info("✅ 模拟API响应成功:")
        logger.info(json.dumps(api_response, indent=2, ensure_ascii=False))
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 模拟API流程失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def test_all_knowledgebases(tenant_id: str):
    """测试租户下的所有知识库"""
    logger.info(f"测试租户 {tenant_id} 下的所有知识库...")
    
    try:
        # 获取租户的所有知识库
        kbs = Knowledgebase.select().where(
            Knowledgebase.tenant_id == tenant_id
        ).limit(5)  # 限制5个，避免输出太多
        
        kb_list = []
        for kb in kbs:
            kb_info = {
                "id": kb.id,
                "name": kb.name,
                "embedding_model": kb.embd_id,
                "tenant_id": kb.tenant_id
            }
            kb_list.append(kb_info)
            logger.info(f"知识库: {kb_info}")
        
        if kb_list:
            logger.info(f"✅ 找到 {len(kb_list)} 个知识库")
            return True, kb_list
        else:
            logger.warning("⚠️  没有找到知识库")
            return False, []
            
    except Exception as e:
        logger.error(f"❌ 获取知识库列表失败: {e}")
        return False, []


def main():
    """主测试函数"""
    if len(sys.argv) < 2:
        print("用法: python test_llm_api_fix.py <tenant_id> [kb_id]")
        print("示例: python test_llm_api_fix.py test_tenant test_kb")
        sys.exit(1)
    
    tenant_id = sys.argv[1]
    kb_id = sys.argv[2] if len(sys.argv) > 2 else None
    
    logger.info("🚀 开始测试修复后的LLM API")
    logger.info(f"租户ID: {tenant_id}")
    if kb_id:
        logger.info(f"知识库ID: {kb_id}")
    
    tests = [
        ("租户LLM查询", lambda: test_tenant_llm_query(tenant_id)[0]),
        ("所有知识库列表", lambda: test_all_knowledgebases(tenant_id)[0]),
    ]
    
    # 如果有知识库ID，添加知识库测试
    if kb_id:
        tests.append(("知识库服务测试", lambda: test_knowledgebase_service(kb_id)[0]))
        tests.append(("完整API流程", lambda: simulate_llm_api(tenant_id, kb_id)))
    else:
        # 如果没有指定kb_id，尝试使用第一个找到的知识库
        success, kb_list = test_all_knowledgebases(tenant_id)
        if success and kb_list:
            test_kb_id = kb_list[0]["id"]
            logger.info(f"使用第一个知识库进行测试: {test_kb_id}")
            tests.append(("知识库服务测试", lambda: test_knowledgebase_service(test_kb_id)[0]))
            tests.append(("完整API流程", lambda: simulate_llm_api(tenant_id, test_kb_id)))
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 测试: {test_name}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed >= total - 1:  # 允许一个测试失败
        logger.info("🎉 修复验证成功！LLM API应该可以正常工作")
        return True
    else:
        logger.error("⚠️  修复验证失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
