#!/usr/bin/env python3
"""
RAG评估问题修复脚本
自动检测和修复常见的RAG评估问题
"""

import sys
import os
import logging
import subprocess
import importlib

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_ragas_installation():
    """检查并安装Ragas"""
    logger.info("检查Ragas安装状态...")
    
    try:
        import ragas
        logger.info(f"✅ Ragas已安装，版本: {ragas.__version__}")
        return True
    except ImportError:
        logger.warning("❌ Ragas未安装，正在安装...")
        
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "ragas==0.1.9"])
            logger.info("✅ Ragas安装成功")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Ragas安装失败: {e}")
            return False


def check_datasets_installation():
    """检查并安装datasets"""
    logger.info("检查datasets安装状态...")
    
    try:
        import datasets
        logger.info(f"✅ datasets已安装，版本: {datasets.__version__}")
        return True
    except ImportError:
        logger.warning("❌ datasets未安装，正在安装...")
        
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "datasets==2.14.6"])
            logger.info("✅ datasets安装成功")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ datasets安装失败: {e}")
            return False


def check_database_tables():
    """检查数据库表是否存在"""
    logger.info("检查评估相关数据库表...")
    
    try:
        # 添加项目根目录到Python路径
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))
        
        from api.db.db_models import EvaluationDataset, EvaluationSample, EvaluationTask, EvaluationResult, EvaluationReport
        
        # 尝试创建表（如果不存在）
        tables = [EvaluationDataset, EvaluationSample, EvaluationTask, EvaluationResult, EvaluationReport]
        
        for table in tables:
            try:
                if not table.table_exists():
                    table.create_table()
                    logger.info(f"✅ 创建表: {table._meta.table_name}")
                else:
                    logger.info(f"✅ 表已存在: {table._meta.table_name}")
            except Exception as e:
                logger.warning(f"⚠️  表 {table._meta.table_name} 检查失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库表检查失败: {e}")
        return False


def check_llm_service():
    """检查LLM服务配置"""
    logger.info("检查LLM服务配置...")
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))
        
        from api.db.services.llm_service import LLMBundle
        from api.db import LLMType
        
        logger.info("✅ LLM服务模块导入成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ LLM服务检查失败: {e}")
        return False


def fix_import_paths():
    """修复导入路径问题"""
    logger.info("修复导入路径...")
    
    try:
        # 确保项目根目录在Python路径中
        project_root = os.path.join(os.path.dirname(__file__), '../..')
        project_root = os.path.abspath(project_root)
        
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
            logger.info(f"✅ 添加项目根目录到Python路径: {project_root}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 修复导入路径失败: {e}")
        return False


def create_sample_dataset():
    """创建示例数据集用于测试"""
    logger.info("创建示例数据集...")
    
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))
        
        from rag.evaluation.dataset_manager import DatasetManager
        from api.utils import get_uuid
        
        tenant_id = "test_tenant_" + get_uuid()[:8]
        kb_id = "test_kb_" + get_uuid()[:8]
        
        manager = DatasetManager(tenant_id)
        
        # 创建测试数据集
        dataset_id = manager.create_dataset(
            name="测试数据集",
            kb_id=kb_id,
            created_by="system",
            description="用于测试RAG评估功能的示例数据集"
        )
        
        # 添加示例样本
        sample_questions = [
            {
                "question": "什么是人工智能？",
                "ground_truth": "人工智能是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。"
            },
            {
                "question": "机器学习和深度学习有什么区别？",
                "ground_truth": "机器学习是人工智能的一个子集，而深度学习是机器学习的一个子集，使用多层神经网络来学习数据的复杂模式。"
            },
            {
                "question": "什么是自然语言处理？",
                "ground_truth": "自然语言处理（NLP）是人工智能的一个分支，专注于使计算机能够理解、解释和生成人类语言。"
            }
        ]
        
        for sample in sample_questions:
            manager.add_sample(
                dataset_id=dataset_id,
                question=sample["question"],
                ground_truth=sample["ground_truth"]
            )
        
        logger.info(f"✅ 创建示例数据集成功: {dataset_id}")
        logger.info(f"   租户ID: {tenant_id}")
        logger.info(f"   知识库ID: {kb_id}")
        logger.info(f"   样本数量: {len(sample_questions)}")
        
        return True, {"dataset_id": dataset_id, "tenant_id": tenant_id, "kb_id": kb_id}
        
    except Exception as e:
        logger.error(f"❌ 创建示例数据集失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False, {}


def main():
    """主修复函数"""
    logger.info("🔧 开始RAG评估问题修复")
    
    fixes = [
        ("修复导入路径", fix_import_paths),
        ("检查Ragas安装", check_ragas_installation),
        ("检查datasets安装", check_datasets_installation),
        ("检查LLM服务", check_llm_service),
        ("检查数据库表", check_database_tables),
    ]
    
    success_count = 0
    
    for fix_name, fix_func in fixes:
        logger.info(f"\n🔧 执行: {fix_name}")
        try:
            if fix_func():
                logger.info(f"✅ {fix_name} 成功")
                success_count += 1
            else:
                logger.error(f"❌ {fix_name} 失败")
        except Exception as e:
            logger.error(f"❌ {fix_name} 异常: {e}")
    
    # 创建示例数据集
    logger.info(f"\n🔧 执行: 创建示例数据集")
    success, dataset_info = create_sample_dataset()
    if success:
        logger.info(f"✅ 创建示例数据集 成功")
        success_count += 1
    else:
        logger.error(f"❌ 创建示例数据集 失败")
    
    total_fixes = len(fixes) + 1
    logger.info(f"\n📊 修复结果: {success_count}/{total_fixes} 成功")
    
    if success_count == total_fixes:
        logger.info("🎉 所有修复完成！RAG评估功能应该可以正常使用了")
        if dataset_info:
            logger.info(f"💡 可以使用以下信息进行测试:")
            logger.info(f"   数据集ID: {dataset_info.get('dataset_id')}")
            logger.info(f"   租户ID: {dataset_info.get('tenant_id')}")
            logger.info(f"   知识库ID: {dataset_info.get('kb_id')}")
        return True
    else:
        logger.error("⚠️  部分修复失败，可能需要手动处理")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
