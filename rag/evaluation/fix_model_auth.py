#!/usr/bin/env python3
"""
修复RAG评估中的模型授权问题
"""

import sys
import os
import logging
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from api.db.services.llm_service import LLMService
from api.db.services.knowledgebase_service import KnowledgebaseService
from api.db.db_models import LLM, Knowledgebase
from api.db import LLMType

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def check_model_authorization(tenant_id: str, model_id: str, model_type: LLMType):
    """检查模型授权"""
    try:
        # 查询模型
        model = LLM.get_or_none(LLM.id == model_id)
        if not model:
            logger.error(f"模型 {model_id} 不存在")
            return False, f"模型 {model_id} 不存在"
        
        # 检查租户权限
        if model.tenant_id != tenant_id:
            logger.error(f"模型 {model_id} 不属于租户 {tenant_id}")
            return False, f"模型 {model_id} 不属于租户 {tenant_id}"
        
        # 检查模型类型
        if model.model_type != model_type.value:
            logger.error(f"模型 {model_id} 类型不匹配，期望: {model_type.value}, 实际: {model.model_type}")
            return False, f"模型类型不匹配"
        
        # 检查模型状态
        if model.status != 1:  # 1表示启用
            logger.error(f"模型 {model_id} 未启用，状态: {model.status}")
            return False, f"模型未启用"
        
        logger.info(f"模型 {model_id} 授权检查通过")
        return True, "授权检查通过"
        
    except Exception as e:
        logger.error(f"检查模型授权失败: {e}")
        return False, str(e)


def get_available_models(tenant_id: str, model_type: LLMType):
    """获取可用的模型列表"""
    try:
        models = LLM.select().where(
            (LLM.tenant_id == tenant_id) &
            (LLM.model_type == model_type.value) &
            (LLM.status == 1)
        )
        
        model_list = []
        for model in models:
            model_list.append({
                "id": model.id,
                "llm_name": model.llm_name,
                "model_type": model.model_type,
                "api_key": model.api_key[:10] + "..." if model.api_key else "无",
                "api_base": model.api_base
            })
        
        return model_list
        
    except Exception as e:
        logger.error(f"获取可用模型失败: {e}")
        return []


def get_kb_default_models(tenant_id: str, kb_id: str):
    """获取知识库的默认模型配置"""
    try:
        kb = Knowledgebase.get_or_none(
            (Knowledgebase.id == kb_id) & 
            (Knowledgebase.tenant_id == tenant_id)
        )
        
        if not kb:
            logger.error(f"知识库 {kb_id} 不存在")
            return None
        
        return {
            "embedding_model": kb.embd_id,
            "llm_model": getattr(kb, 'llm_id', None),
            "rerank_model": getattr(kb, 'rerank_id', None)
        }
        
    except Exception as e:
        logger.error(f"获取知识库默认模型失败: {e}")
        return None


def create_evaluation_config_template(tenant_id: str, kb_id: str):
    """创建评估配置模板"""
    logger.info("创建评估配置模板...")
    
    # 获取知识库默认模型
    kb_models = get_kb_default_models(tenant_id, kb_id)
    if not kb_models:
        logger.error("无法获取知识库模型配置")
        return None
    
    # 获取可用模型
    embedding_models = get_available_models(tenant_id, LLMType.EMBEDDING)
    chat_models = get_available_models(tenant_id, LLMType.CHAT)
    rerank_models = get_available_models(tenant_id, LLMType.RERANK)
    
    # 创建配置模板
    config_template = {
        "evaluation_config": {
            "embedding_model": kb_models["embedding_model"],
            "llm_model": kb_models["llm_model"],
            "rerank_model": kb_models["rerank_model"],
            "metrics": ["faithfulness", "answer_relevancy", "context_precision", "context_recall"],
            "similarity_threshold": 0.2,
            "vector_similarity_weight": 0.3,
            "top_n": 5,
            "top_k": 100
        },
        "available_models": {
            "embedding_models": embedding_models,
            "chat_models": chat_models,
            "rerank_models": rerank_models
        },
        "model_authorization_status": {}
    }
    
    # 检查模型授权状态
    if kb_models["embedding_model"]:
        is_auth, msg = check_model_authorization(tenant_id, kb_models["embedding_model"], LLMType.EMBEDDING)
        config_template["model_authorization_status"]["embedding_model"] = {
            "authorized": is_auth,
            "message": msg
        }
    
    if kb_models["llm_model"]:
        is_auth, msg = check_model_authorization(tenant_id, kb_models["llm_model"], LLMType.CHAT)
        config_template["model_authorization_status"]["llm_model"] = {
            "authorized": is_auth,
            "message": msg
        }
    
    if kb_models["rerank_model"]:
        is_auth, msg = check_model_authorization(tenant_id, kb_models["rerank_model"], LLMType.RERANK)
        config_template["model_authorization_status"]["rerank_model"] = {
            "authorized": is_auth,
            "message": msg
        }
    
    return config_template


def fix_model_authorization_issues(tenant_id: str, kb_id: str):
    """修复模型授权问题"""
    logger.info(f"开始修复租户 {tenant_id} 知识库 {kb_id} 的模型授权问题")
    
    # 创建配置模板
    config = create_evaluation_config_template(tenant_id, kb_id)
    if not config:
        return False
    
    # 检查是否有授权问题
    auth_issues = []
    for model_type, status in config["model_authorization_status"].items():
        if not status["authorized"]:
            auth_issues.append(f"{model_type}: {status['message']}")
    
    if auth_issues:
        logger.error("发现模型授权问题:")
        for issue in auth_issues:
            logger.error(f"  - {issue}")
        
        logger.info("建议解决方案:")
        logger.info("1. 检查模型是否存在且属于正确的租户")
        logger.info("2. 确保模型状态为启用")
        logger.info("3. 验证模型类型是否正确")
        
        # 提供可用的替代模型
        logger.info("可用的替代模型:")
        for model_type, models in config["available_models"].items():
            if models:
                logger.info(f"  {model_type}:")
                for model in models[:3]:  # 只显示前3个
                    logger.info(f"    - {model['id']}: {model['llm_name']}")
        
        return False
    else:
        logger.info("✅ 所有模型授权检查通过")
        return True


def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: python fix_model_auth.py <tenant_id> <kb_id>")
        print("示例: python fix_model_auth.py test_tenant test_kb")
        sys.exit(1)
    
    tenant_id = sys.argv[1]
    kb_id = sys.argv[2]
    
    logger.info(f"🔧 开始修复模型授权问题")
    logger.info(f"租户ID: {tenant_id}")
    logger.info(f"知识库ID: {kb_id}")
    
    success = fix_model_authorization_issues(tenant_id, kb_id)
    
    if success:
        logger.info("🎉 模型授权检查通过，可以正常使用评估功能")
    else:
        logger.error("❌ 发现模型授权问题，请根据建议进行修复")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
