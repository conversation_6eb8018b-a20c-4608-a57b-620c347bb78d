#!/usr/bin/env python3
"""
简化的RAG评估测试
验证基本功能是否正常
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_ragas_import():
    """测试Ragas导入"""
    logger.info("测试Ragas导入...")
    
    try:
        from rag.evaluation.ragas_evaluator import RagasEvaluator, RAGAS_AVAILABLE, HAS_CONTEXT_RELEVANCY
        
        logger.info(f"✅ Ragas可用: {RAGAS_AVAILABLE}")
        logger.info(f"✅ Context Relevancy可用: {HAS_CONTEXT_RELEVANCY}")
        
        if RAGAS_AVAILABLE:
            # 测试创建评估器
            evaluator = RagasEvaluator("test_tenant", "test_kb")
            available_metrics = list(evaluator.available_metrics.keys())
            logger.info(f"✅ 可用指标: {available_metrics}")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Ragas导入失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def test_dataset_manager():
    """测试数据集管理器"""
    logger.info("测试数据集管理器...")
    
    try:
        from rag.evaluation.dataset_manager import DatasetManager
        
        manager = DatasetManager("test_tenant")
        logger.info("✅ 数据集管理器创建成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据集管理器测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def test_evaluation_runner():
    """测试评估运行器"""
    logger.info("测试评估运行器...")
    
    try:
        from rag.evaluation.evaluation_runner import EvaluationRunner
        
        runner = EvaluationRunner("test_tenant")
        logger.info("✅ 评估运行器创建成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 评估运行器测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def test_basic_ragas_functionality():
    """测试基本Ragas功能"""
    logger.info("测试基本Ragas功能...")
    
    try:
        from ragas import evaluate
        from ragas.metrics import faithfulness, answer_relevancy
        from datasets import Dataset
        
        # 创建简单的测试数据
        test_data = {
            "question": ["什么是人工智能？"],
            "answer": ["人工智能是计算机科学的一个分支。"],
            "contexts": [["人工智能（AI）是计算机科学的一个分支，专注于创建能够执行通常需要人类智能的任务的系统。"]],
            "ground_truth": ["人工智能是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。"]
        }
        
        dataset = Dataset.from_dict(test_data)
        logger.info("✅ 测试数据集创建成功")
        
        # 尝试评估（可能会因为模型配置问题失败，但至少验证API可用）
        try:
            result = evaluate(dataset, metrics=[faithfulness, answer_relevancy])
            logger.info(f"✅ Ragas评估成功: {result}")
        except Exception as e:
            logger.warning(f"⚠️  Ragas评估失败（可能是模型配置问题）: {e}")
            # 这是预期的，因为我们没有配置实际的模型
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 基本Ragas功能测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始简化RAG评估测试")
    
    tests = [
        ("Ragas导入", test_ragas_import),
        ("数据集管理器", test_dataset_manager),
        ("评估运行器", test_evaluation_runner),
        ("基本Ragas功能", test_basic_ragas_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 测试: {test_name}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed >= 3:  # 至少前3个测试通过
        logger.info("🎉 核心功能测试通过！RAG评估功能基本可用")
        return True
    else:
        logger.error("⚠️  核心功能测试失败，需要进一步修复")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
