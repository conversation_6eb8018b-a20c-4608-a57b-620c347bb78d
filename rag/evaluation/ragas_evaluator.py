"""
Ragas评估引擎
集成ragas库对RAG系统进行全面评估
"""

import logging
import time
import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

try:
    from ragas import evaluate
    from ragas.metrics import (
        faithfulness,
        answer_relevancy,
        context_precision,
        context_recall,
        answer_correctness,
        answer_similarity
    )
    # 尝试导入context_relevancy，如果不存在则跳过
    try:
        from ragas.metrics import context_relevancy
        HAS_CONTEXT_RELEVANCY = True
    except ImportError:
        logging.warning("context_relevancy指标在当前Ragas版本中不可用")
        HAS_CONTEXT_RELEVANCY = False
        context_relevancy = None

    from datasets import Dataset
    from rag.evaluation.ragas_adapter import setup_ragas_models
    RAGAS_AVAILABLE = True
except ImportError as e:
    logging.warning(f"Ragas库未安装，请运行: pip install ragas，错误: {e}")
    RAGAS_AVAILABLE = False
    HAS_CONTEXT_RELEVANCY = False

from api.db.services.llm_service import LLMBundle
from api.db import LLMType
from rag.nlp.search import Dealer
from api import settings


@dataclass
class EvaluationConfig:
    """评估配置"""
    metrics: List[str]  # 要评估的指标列表
    similarity_threshold: float = 0.2
    vector_similarity_weight: float = 0.3
    top_n: int = 8
    top_k: int = 1024
    rerank_id: Optional[str] = None
    llm_id: Optional[str] = None
    embedding_id: Optional[str] = None
    # LLM生成参数
    temperature: float = 0.1
    max_tokens: int = 1024
    top_p: float = 0.9
    presence_penalty: float = 0.0
    frequency_penalty: float = 0.0


@dataclass
class EvaluationSample:
    """评估样本"""
    question: str
    ground_truth: Optional[str] = None
    contexts: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class EvaluationResult:
    """单个样本的评估结果"""
    sample_id: str
    question: str
    generated_answer: str
    retrieved_contexts: List[str]
    metrics: Dict[str, float]
    execution_time: float
    error_message: Optional[str] = None


class RagasEvaluator:
    """Ragas评估器"""

    def __init__(self, tenant_id: str, kb_id: str):
        self.tenant_id = tenant_id
        self.kb_id = kb_id

        # 检查docStoreConn是否可用
        if hasattr(settings, 'docStoreConn') and settings.docStoreConn is not None:
            self.dealer = Dealer(settings.docStoreConn)
        else:
            logging.warning("docStoreConn不可用，将在运行时初始化")
            self.dealer = None

        # 可用的评估指标映射
        self.available_metrics = {
            'faithfulness': faithfulness,
            'answer_relevancy': answer_relevancy,
            'context_precision': context_precision,
            'context_recall': context_recall,
            'answer_correctness': answer_correctness,
            'answer_similarity': answer_similarity
        }

        # 如果context_relevancy可用，则添加到指标中
        if HAS_CONTEXT_RELEVANCY and context_relevancy is not None:
            self.available_metrics['context_relevancy'] = context_relevancy

    def _ensure_dealer(self):
        """确保dealer已初始化"""
        if self.dealer is None:
            if hasattr(settings, 'docStoreConn') and settings.docStoreConn is not None:
                self.dealer = Dealer(settings.docStoreConn)
            else:
                raise RuntimeError("docStoreConn未配置，无法初始化Dealer")

    def _get_models(self, config: EvaluationConfig):
        """获取评估所需的模型"""
        from api.db.services.knowledgebase_service import KnowledgebaseService

        # 获取知识库信息
        e, kb = KnowledgebaseService.get_by_id(self.kb_id)
        if not e:
            raise ValueError(f"知识库 {self.kb_id} 不存在")

        # 获取embedding模型
        embd_id = config.embedding_id or kb.embd_id
        embd_mdl = LLMBundle(self.tenant_id, LLMType.EMBEDDING, embd_id)

        # 获取LLM模型
        llm_id = config.llm_id or kb.tenant_id  # 使用租户默认LLM
        llm_mdl = LLMBundle(self.tenant_id, LLMType.CHAT, llm_id)

        # 获取rerank模型（如果配置了）
        rerank_mdl = None
        if config.rerank_id:
            rerank_mdl = LLMBundle(self.tenant_id, LLMType.RERANK, config.rerank_id)

        return embd_mdl, llm_mdl, rerank_mdl, kb

    def _retrieve_and_generate(self, question: str, config: EvaluationConfig) -> tuple:
        """执行检索和生成"""
        embd_mdl, llm_mdl, rerank_mdl, kb = self._get_models(config)

        # 确保dealer已初始化
        self._ensure_dealer()

        # 执行检索
        ranks = self.dealer.retrieval(
            question=question,
            embd_mdl=embd_mdl,
            tenant_ids=self.tenant_id,
            kb_ids=[self.kb_id],
            page=1,
            page_size=config.top_n,
            similarity_threshold=config.similarity_threshold,
            vector_similarity_weight=config.vector_similarity_weight,
            top=config.top_k,
            rerank_mdl=rerank_mdl
        )

        # 提取检索到的文档
        retrieved_contexts = []
        for chunk in ranks.get("chunks", []):
            retrieved_contexts.append(chunk.get("content_with_weight", ""))

        # 生成答案
        if retrieved_contexts:
            context_text = "\n\n".join(retrieved_contexts)
            prompt = f"""基于以下上下文回答问题：

上下文：
{context_text}

问题：{question}

请基于上下文提供准确的答案："""

            # 调用LLM生成答案
            try:
                # LLMBundle.chat需要三个参数: system, history, gen_conf
                gen_conf = {
                    "temperature": config.temperature,
                    "max_tokens": config.max_tokens,
                    "top_p": config.top_p
                }
                # 只有当值大于0时才添加penalty参数
                if config.presence_penalty > 0:
                    gen_conf["presence_penalty"] = config.presence_penalty
                if config.frequency_penalty > 0:
                    gen_conf["frequency_penalty"] = config.frequency_penalty

                response = llm_mdl.chat(prompt, [], gen_conf)
                # 处理不同类型的响应
                if isinstance(response, dict):
                    generated_answer = response.get("content", str(response))
                elif isinstance(response, str):
                    generated_answer = response
                else:
                    generated_answer = str(response)

                # 确保答案不为空
                if not generated_answer or generated_answer.strip() == "":
                    generated_answer = "未能生成有效答案"

            except Exception as e:
                logging.error(f"LLM生成答案失败: {e}")
                import traceback
                logging.error(f"详细错误信息: {traceback.format_exc()}")
                generated_answer = "生成答案时发生错误"
        else:
            generated_answer = "未找到相关信息"

        return retrieved_contexts, generated_answer

    def evaluate_sample(self, sample: EvaluationSample, config: EvaluationConfig) -> EvaluationResult:
        """评估单个样本"""
        if not RAGAS_AVAILABLE:
            raise ImportError("Ragas库未安装，无法进行评估")

        start_time = time.time()
        sample_id = f"sample_{int(start_time * 1000)}"

        try:
            # 执行检索和生成
            retrieved_contexts, generated_answer = self._retrieve_and_generate(
                sample.question, config
            )

            # 获取模型并设置Ragas
            embd_mdl, llm_mdl, rerank_mdl, kb = self._get_models(config)

            try:
                # 检测Ragas版本并使用对应的适配器
                import ragas
                ragas_version = ragas.__version__
                logging.info(f"检测到Ragas版本: {ragas_version}")

                if ragas_version.startswith('0.1.'):
                    # 使用0.1.x适配器
                    from rag.evaluation.ragas_adapter import setup_ragas_models
                    llm_adapter, embedding_adapter = setup_ragas_models(llm_mdl, embd_mdl)
                    logging.info(f"使用Ragas {ragas_version} (0.1.x) 适配器")
                else:
                    # 使用0.2.x适配器
                    from rag.evaluation.ragas_adapter_v2 import setup_ragas_models_v2
                    llm_adapter, embedding_adapter = setup_ragas_models_v2(llm_mdl, embd_mdl)
                    logging.info(f"使用Ragas {ragas_version} (0.2.x) 适配器")
            except Exception as e:
                logging.warning(f"设置Ragas模型失败，将使用默认配置: {e}")
                # 如果设置失败，创建默认适配器
                try:
                    import ragas
                    if ragas.__version__.startswith('0.1.'):
                        from rag.evaluation.ragas_adapter import create_ragas_models
                        llm_adapter, embedding_adapter = create_ragas_models(llm_mdl, embd_mdl)
                        logging.info("使用Ragas 0.1.x 默认适配器")
                    else:
                        from rag.evaluation.ragas_adapter_v2 import create_ragas_models_v2
                        llm_adapter, embedding_adapter = create_ragas_models_v2(llm_mdl, embd_mdl)
                        logging.info("使用Ragas 0.2.x 默认适配器")
                except Exception as fallback_e:
                    logging.error(f"创建默认适配器也失败: {fallback_e}")
                    raise e

            # 准备ragas评估数据
            eval_data = {
                "question": [sample.question],
                "answer": [generated_answer],
                "contexts": [retrieved_contexts],
            }

            # 如果有标准答案，添加到评估数据中
            if sample.ground_truth:
                eval_data["ground_truth"] = [sample.ground_truth]

            # 创建数据集
            dataset = Dataset.from_dict(eval_data)
            logging.info(f"创建评估数据集: {eval_data}")

            # 选择要评估的指标
            selected_metrics = []
            for metric_name in config.metrics:
                if metric_name in self.available_metrics:
                    selected_metrics.append(self.available_metrics[metric_name])
                else:
                    logging.warning(f"未知的评估指标: {metric_name}")

            if not selected_metrics:
                raise ValueError("没有有效的评估指标")

            # 执行评估
            metric_names = []
            for m in selected_metrics:
                if hasattr(m, '__name__'):
                    metric_names.append(m.__name__)
                elif hasattr(m, 'name'):
                    metric_names.append(m.name)
                else:
                    metric_names.append(str(type(m).__name__))

            logging.info(f"开始执行Ragas评估，指标: {metric_names}")
            logging.info(f"数据集大小: {len(dataset)}")

            try:
                logging.info(f"[RagasEvaluator] 开始调用Ragas evaluate函数")
                logging.info(f"[RagasEvaluator] 数据集类型: {type(dataset)}")
                logging.info(f"[RagasEvaluator] 指标数量: {len(selected_metrics)}")
                logging.info(f"[RagasEvaluator] LLM适配器类型: {type(llm_adapter)}")
                logging.info(f"[RagasEvaluator] Embedding适配器类型: {type(embedding_adapter)}")

                # 检查适配器的方法
                llm_methods = [method for method in dir(llm_adapter) if not method.startswith('_')]
                embedding_methods = [method for method in dir(embedding_adapter) if not method.startswith('_')]
                logging.info(f"[RagasEvaluator] LLM适配器方法: {llm_methods}")
                logging.info(f"[RagasEvaluator] Embedding适配器方法: {embedding_methods}")

                # 测试适配器是否正常工作
                logging.info(f"[RagasEvaluator] 测试LLM适配器...")
                try:
                    test_result = llm_adapter.generate_text("测试提示")
                    logging.info(f"[RagasEvaluator] LLM适配器测试成功: {test_result[:50]}...")
                except Exception as e:
                    logging.error(f"[RagasEvaluator] LLM适配器测试失败: {e}")

                logging.info(f"[RagasEvaluator] 测试Embedding适配器...")
                try:
                    test_result = embedding_adapter.embed_query("测试文本")
                    logging.info(f"[RagasEvaluator] Embedding适配器测试成功: 向量维度={len(test_result)}")
                except Exception as e:
                    logging.error(f"[RagasEvaluator] Embedding适配器测试失败: {e}")

                # 检查Ragas是否会使用我们的适配器
                logging.info(f"[RagasEvaluator] 检查适配器类型...")
                logging.info(f"[RagasEvaluator] LLM适配器ID: {id(llm_adapter)}")
                logging.info(f"[RagasEvaluator] Embedding适配器ID: {id(embedding_adapter)}")

                # 直接传递模型适配器到evaluate函数
                result = evaluate(
                    dataset,
                    metrics=selected_metrics,
                    llm=llm_adapter,
                    embeddings=embedding_adapter
                )
                logging.info(f"Ragas评估完成，结果: {result}")
            except Exception as e:
                logging.error(f"Ragas评估失败: {e}")
                import traceback
                logging.error(f"详细错误信息: {traceback.format_exc()}")
                raise e

            # 提取评估结果
            metrics_result = {}
            for metric_name in config.metrics:
                if metric_name in result:
                    metrics_result[metric_name] = float(result[metric_name])
                else:
                    logging.warning(f"指标 {metric_name} 未在结果中找到，可用指标: {list(result.keys())}")
                    metrics_result[metric_name] = 0.0

            execution_time = time.time() - start_time

            return EvaluationResult(
                sample_id=sample_id,
                question=sample.question,
                generated_answer=generated_answer,
                retrieved_contexts=retrieved_contexts,
                metrics=metrics_result,
                execution_time=execution_time
            )

        except Exception as e:
            execution_time = time.time() - start_time
            logging.error(f"评估样本失败: {e}")

            return EvaluationResult(
                sample_id=sample_id,
                question=sample.question,
                generated_answer="",
                retrieved_contexts=[],
                metrics={},
                execution_time=execution_time,
                error_message=str(e)
            )

    def evaluate_batch(self, samples: List[EvaluationSample], config: EvaluationConfig) -> List[EvaluationResult]:
        """批量评估样本"""
        results = []

        for i, sample in enumerate(samples):
            logging.info(f"评估进度: {i+1}/{len(samples)}")
            result = self.evaluate_sample(sample, config)
            results.append(result)

        return results

    def generate_report(self, results: List[EvaluationResult]) -> Dict[str, Any]:
        """生成评估报告"""
        if not results:
            return {"error": "没有评估结果"}

        # 统计信息
        total_samples = len(results)
        successful_samples = len([r for r in results if not r.error_message])
        failed_samples = total_samples - successful_samples

        # 计算平均指标
        metric_names = set()
        for result in results:
            metric_names.update(result.metrics.keys())

        avg_metrics = {}
        for metric_name in metric_names:
            values = [r.metrics.get(metric_name, 0) for r in results if not r.error_message and metric_name in r.metrics]
            if values:
                avg_metrics[f"avg_{metric_name}"] = sum(values) / len(values)

        # 计算平均执行时间
        execution_times = [r.execution_time for r in results if not r.error_message]
        avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0

        # 详细结果
        detailed_results = []
        for result in results:
            detailed_results.append({
                "sample_id": result.sample_id,
                "question": result.question,
                "generated_answer": result.generated_answer,
                "metrics": result.metrics,
                "execution_time": result.execution_time,
                "error_message": result.error_message
            })

        report = {
            "summary": {
                "total_samples": total_samples,
                "successful_samples": successful_samples,
                "failed_samples": failed_samples,
                "avg_execution_time": avg_execution_time,
                **avg_metrics
            },
            "detailed_results": detailed_results
        }

        return report

    @staticmethod
    def get_available_metrics() -> List[str]:
        """获取可用的评估指标列表"""
        return list(RagasEvaluator({}, {}).available_metrics.keys())