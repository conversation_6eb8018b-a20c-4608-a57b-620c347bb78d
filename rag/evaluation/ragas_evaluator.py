"""
Ragas评估引擎
集成ragas库对RAG系统进行全面评估
"""

import logging
import time
import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

try:
    from ragas import evaluate
    from ragas.metrics import (
        faithfulness,
        answer_relevancy,
        context_precision,
        context_recall,
        context_relevancy,
        answer_correctness,
        answer_similarity
    )
    from datasets import Dataset
    RAGAS_AVAILABLE = True
except ImportError:
    logging.warning("Ragas库未安装，请运行: pip install ragas")
    RAGAS_AVAILABLE = False

from api.db.services.llm_service import LLMBundle
from api.db import LLMType
from rag.nlp.search import Dealer
from api import settings


@dataclass
class EvaluationConfig:
    """评估配置"""
    metrics: List[str]  # 要评估的指标列表
    similarity_threshold: float = 0.2
    vector_similarity_weight: float = 0.3
    top_n: int = 8
    top_k: int = 1024
    rerank_id: Optional[str] = None
    llm_id: Optional[str] = None
    embedding_id: Optional[str] = None


@dataclass
class EvaluationSample:
    """评估样本"""
    question: str
    ground_truth: Optional[str] = None
    contexts: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class EvaluationResult:
    """单个样本的评估结果"""
    sample_id: str
    question: str
    generated_answer: str
    retrieved_contexts: List[str]
    metrics: Dict[str, float]
    execution_time: float
    error_message: Optional[str] = None


class RagasEvaluator:
    """Ragas评估器"""
    
    def __init__(self, tenant_id: str, kb_id: str):
        self.tenant_id = tenant_id
        self.kb_id = kb_id
        self.dealer = Dealer(settings.docStoreConn)
        
        # 可用的评估指标映射
        self.available_metrics = {
            'faithfulness': faithfulness,
            'answer_relevancy': answer_relevancy,
            'context_precision': context_precision,
            'context_recall': context_recall,
            'context_relevancy': context_relevancy,
            'answer_correctness': answer_correctness,
            'answer_similarity': answer_similarity
        }
    
    def _get_models(self, config: EvaluationConfig):
        """获取评估所需的模型"""
        from api.db.services.knowledgebase_service import KnowledgebaseService
        
        # 获取知识库信息
        e, kb = KnowledgebaseService.get_by_id(self.kb_id)
        if not e:
            raise ValueError(f"知识库 {self.kb_id} 不存在")
        
        # 获取embedding模型
        embd_id = config.embedding_id or kb.embd_id
        embd_mdl = LLMBundle(self.tenant_id, LLMType.EMBEDDING, embd_id)
        
        # 获取LLM模型
        llm_id = config.llm_id or kb.tenant_id  # 使用租户默认LLM
        llm_mdl = LLMBundle(self.tenant_id, LLMType.CHAT, llm_id)
        
        # 获取rerank模型（如果配置了）
        rerank_mdl = None
        if config.rerank_id:
            rerank_mdl = LLMBundle(self.tenant_id, LLMType.RERANK, config.rerank_id)
        
        return embd_mdl, llm_mdl, rerank_mdl, kb
    
    def _retrieve_and_generate(self, question: str, config: EvaluationConfig) -> tuple:
        """执行检索和生成"""
        embd_mdl, llm_mdl, rerank_mdl, kb = self._get_models(config)
        
        # 执行检索
        ranks = self.dealer.retrieval(
            question=question,
            embd_mdl=embd_mdl,
            tenant_ids=self.tenant_id,
            kb_ids=[self.kb_id],
            page=1,
            page_size=config.top_n,
            similarity_threshold=config.similarity_threshold,
            vector_similarity_weight=config.vector_similarity_weight,
            top=config.top_k,
            rerank_mdl=rerank_mdl
        )
        
        # 提取检索到的文档
        retrieved_contexts = []
        for chunk in ranks.get("chunks", []):
            retrieved_contexts.append(chunk.get("content_with_weight", ""))
        
        # 生成答案
        if retrieved_contexts:
            context_text = "\n\n".join(retrieved_contexts)
            prompt = f"""基于以下上下文回答问题：

上下文：
{context_text}

问题：{question}

请基于上下文提供准确的答案："""
            
            # 调用LLM生成答案
            try:
                response = llm_mdl.chat(prompt, [])
                generated_answer = response.get("content", "")
            except Exception as e:
                logging.error(f"LLM生成答案失败: {e}")
                generated_answer = "生成答案时发生错误"
        else:
            generated_answer = "未找到相关信息"
            
        return retrieved_contexts, generated_answer
    
    def evaluate_sample(self, sample: EvaluationSample, config: EvaluationConfig) -> EvaluationResult:
        """评估单个样本"""
        if not RAGAS_AVAILABLE:
            raise ImportError("Ragas库未安装，无法进行评估")
        
        start_time = time.time()
        sample_id = f"sample_{int(start_time * 1000)}"
        
        try:
            # 执行检索和生成
            retrieved_contexts, generated_answer = self._retrieve_and_generate(
                sample.question, config
            )
            
            # 准备ragas评估数据
            eval_data = {
                "question": [sample.question],
                "answer": [generated_answer],
                "contexts": [retrieved_contexts],
            }
            
            # 如果有标准答案，添加到评估数据中
            if sample.ground_truth:
                eval_data["ground_truth"] = [sample.ground_truth]
            
            # 创建数据集
            dataset = Dataset.from_dict(eval_data)
            
            # 选择要评估的指标
            selected_metrics = []
            for metric_name in config.metrics:
                if metric_name in self.available_metrics:
                    selected_metrics.append(self.available_metrics[metric_name])
                else:
                    logging.warning(f"未知的评估指标: {metric_name}")
            
            if not selected_metrics:
                raise ValueError("没有有效的评估指标")
            
            # 执行评估
            result = evaluate(dataset, metrics=selected_metrics)
            
            # 提取评估结果
            metrics_result = {}
            for metric_name in config.metrics:
                if metric_name in result:
                    metrics_result[metric_name] = float(result[metric_name])
            
            execution_time = time.time() - start_time
            
            return EvaluationResult(
                sample_id=sample_id,
                question=sample.question,
                generated_answer=generated_answer,
                retrieved_contexts=retrieved_contexts,
                metrics=metrics_result,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logging.error(f"评估样本失败: {e}")
            
            return EvaluationResult(
                sample_id=sample_id,
                question=sample.question,
                generated_answer="",
                retrieved_contexts=[],
                metrics={},
                execution_time=execution_time,
                error_message=str(e)
            )
    
    def evaluate_batch(self, samples: List[EvaluationSample], config: EvaluationConfig) -> List[EvaluationResult]:
        """批量评估样本"""
        results = []
        
        for i, sample in enumerate(samples):
            logging.info(f"评估进度: {i+1}/{len(samples)}")
            result = self.evaluate_sample(sample, config)
            results.append(result)
        
        return results
    
    def generate_report(self, results: List[EvaluationResult]) -> Dict[str, Any]:
        """生成评估报告"""
        if not results:
            return {"error": "没有评估结果"}
        
        # 统计信息
        total_samples = len(results)
        successful_samples = len([r for r in results if not r.error_message])
        failed_samples = total_samples - successful_samples
        
        # 计算平均指标
        metric_names = set()
        for result in results:
            metric_names.update(result.metrics.keys())
        
        avg_metrics = {}
        for metric_name in metric_names:
            values = [r.metrics.get(metric_name, 0) for r in results if not r.error_message and metric_name in r.metrics]
            if values:
                avg_metrics[f"avg_{metric_name}"] = sum(values) / len(values)
        
        # 计算平均执行时间
        execution_times = [r.execution_time for r in results if not r.error_message]
        avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
        
        # 详细结果
        detailed_results = []
        for result in results:
            detailed_results.append({
                "sample_id": result.sample_id,
                "question": result.question,
                "generated_answer": result.generated_answer,
                "metrics": result.metrics,
                "execution_time": result.execution_time,
                "error_message": result.error_message
            })
        
        report = {
            "summary": {
                "total_samples": total_samples,
                "successful_samples": successful_samples,
                "failed_samples": failed_samples,
                "avg_execution_time": avg_execution_time,
                **avg_metrics
            },
            "detailed_results": detailed_results
        }
        
        return report
    
    @staticmethod
    def get_available_metrics() -> List[str]:
        """获取可用的评估指标列表"""
        return list(RagasEvaluator({}, {}).available_metrics.keys()) 