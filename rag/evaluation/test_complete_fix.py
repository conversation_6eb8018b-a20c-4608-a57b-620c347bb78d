#!/usr/bin/env python3
"""
测试完整的RAG评估修复
验证LLM参数配置和API调用修复
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_evaluation_config_with_llm_params():
    """测试带LLM参数的评估配置"""
    logger.info("测试带LLM参数的评估配置...")
    
    try:
        from rag.evaluation.ragas_evaluator import EvaluationConfig
        
        # 创建包含LLM参数的配置
        config = EvaluationConfig(
            metrics=["faithfulness", "answer_relevancy"],
            llm_id="gpt-4o-2024-11-20@TogetherAI",
            temperature=0.2,
            max_tokens=2048,
            top_p=0.8,
            presence_penalty=0.1,
            frequency_penalty=0.1
        )
        
        logger.info(f"✅ 评估配置创建成功:")
        logger.info(f"  - 指标: {config.metrics}")
        logger.info(f"  - LLM模型: {config.llm_id}")
        logger.info(f"  - 温度: {config.temperature}")
        logger.info(f"  - 最大tokens: {config.max_tokens}")
        logger.info(f"  - top_p: {config.top_p}")
        logger.info(f"  - presence_penalty: {config.presence_penalty}")
        logger.info(f"  - frequency_penalty: {config.frequency_penalty}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 评估配置测试失败: {e}")
        return False


def test_gen_conf_generation():
    """测试gen_conf生成"""
    logger.info("测试gen_conf生成...")
    
    try:
        from rag.evaluation.ragas_evaluator import EvaluationConfig
        
        config = EvaluationConfig(
            metrics=["faithfulness"],
            temperature=0.3,
            max_tokens=1500,
            top_p=0.7,
            presence_penalty=0.2,
            frequency_penalty=0.3
        )
        
        # 模拟生成gen_conf的逻辑
        gen_conf = {
            "temperature": config.temperature,
            "max_tokens": config.max_tokens,
            "top_p": config.top_p
        }
        
        # 只有当值大于0时才添加penalty参数
        if config.presence_penalty > 0:
            gen_conf["presence_penalty"] = config.presence_penalty
        if config.frequency_penalty > 0:
            gen_conf["frequency_penalty"] = config.frequency_penalty
        
        logger.info(f"✅ gen_conf生成成功: {gen_conf}")
        
        # 验证参数
        expected_keys = ["temperature", "max_tokens", "top_p", "presence_penalty", "frequency_penalty"]
        for key in expected_keys:
            if key in gen_conf:
                logger.info(f"  ✓ {key}: {gen_conf[key]}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ gen_conf生成测试失败: {e}")
        return False


def test_ragas_adapter_with_params():
    """测试Ragas适配器参数传递"""
    logger.info("测试Ragas适配器参数传递...")
    
    try:
        from rag.evaluation.ragas_adapter import RAGFlowLLMAdapter
        
        # 模拟LLMBundle
        class MockLLMBundle:
            def __init__(self):
                self.api_base = "https://aigc.sankuai.com/v1/openai/native/"
                
            def chat(self, system, history, gen_conf):
                logger.info(f"模拟LLM调用，gen_conf: {gen_conf}")
                return {"content": "测试响应"}
        
        mock_bundle = MockLLMBundle()
        adapter = RAGFlowLLMAdapter(mock_bundle)
        
        # 测试带参数的generate_text调用
        result = adapter.generate_text(
            "测试提示",
            temperature=0.3,
            max_tokens=1500,
            top_p=0.7,
            presence_penalty=0.2,
            frequency_penalty=0.3
        )
        
        logger.info(f"✅ 适配器参数传递测试成功，结果: {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 适配器参数传递测试失败: {e}")
        return False


def test_api_llm_config_format():
    """测试API LLM配置格式"""
    logger.info("测试API LLM配置格式...")
    
    try:
        # 模拟前端发送的llm_config
        llm_config = {
            "temperature": 0.2,
            "max_tokens": 2048,
            "top_p": 0.8,
            "presence_penalty": 0.1,
            "frequency_penalty": 0.1
        }
        
        logger.info(f"✅ API LLM配置格式: {llm_config}")
        
        # 验证配置可以转换为EvaluationConfig
        from rag.evaluation.ragas_evaluator import EvaluationConfig
        
        config = EvaluationConfig(
            metrics=["faithfulness", "answer_relevancy"],
            llm_id="gpt-4o-2024-11-20@TogetherAI",
            **llm_config  # 展开LLM配置
        )
        
        logger.info(f"✅ 配置转换成功，温度: {config.temperature}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ API配置格式测试失败: {e}")
        return False


def test_default_values():
    """测试默认值"""
    logger.info("测试默认值...")
    
    try:
        from rag.evaluation.ragas_evaluator import EvaluationConfig
        
        # 创建只有必需参数的配置
        config = EvaluationConfig(
            metrics=["faithfulness"]
        )
        
        logger.info(f"✅ 默认值测试:")
        logger.info(f"  - 温度: {config.temperature} (默认: 0.1)")
        logger.info(f"  - 最大tokens: {config.max_tokens} (默认: 1024)")
        logger.info(f"  - top_p: {config.top_p} (默认: 0.9)")
        logger.info(f"  - presence_penalty: {config.presence_penalty} (默认: 0.0)")
        logger.info(f"  - frequency_penalty: {config.frequency_penalty} (默认: 0.0)")
        
        # 验证默认值
        assert config.temperature == 0.1
        assert config.max_tokens == 1024
        assert config.top_p == 0.9
        assert config.presence_penalty == 0.0
        assert config.frequency_penalty == 0.0
        
        logger.info("✅ 所有默认值正确")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 默认值测试失败: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始完整的RAG评估修复测试")
    
    tests = [
        ("带LLM参数的评估配置", test_evaluation_config_with_llm_params),
        ("gen_conf生成", test_gen_conf_generation),
        ("Ragas适配器参数传递", test_ragas_adapter_with_params),
        ("API LLM配置格式", test_api_llm_config_format),
        ("默认值", test_default_values),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 测试: {test_name}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 完整的RAG评估修复验证成功！")
        logger.info("主要修复点:")
        logger.info("1. ✅ 添加了LLM生成参数配置支持")
        logger.info("2. ✅ 修复了LLMBundle.chat()参数问题")
        logger.info("3. ✅ 修复了Ragas适配器API调用")
        logger.info("4. ✅ 确保使用正确的API域名")
        logger.info("5. ✅ 提供了合理的默认值")
        return True
    else:
        logger.error("⚠️  部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
