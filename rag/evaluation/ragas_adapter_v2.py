"""
Ragas 0.2.x 适配器
用于将RAGFlow的LLM和Embedding模型适配到Ragas 0.2.x框架
"""

import logging
from typing import List, Dict, Any, Optional, Union
import asyncio

# 导入LangChain类型
try:
    from langchain_core.prompt_values import StringPromptValue
    LANGCHAIN_AVAILABLE = True
except ImportError:
    # 如果LangChain不可用，创建占位符
    class StringPromptValue:
        pass
    LANGCHAIN_AVAILABLE = False

try:
    import ragas
    from ragas.llms.base import BaseRagasLLM
    from ragas.embeddings.base import BaseRagasEmbeddings
    RAGAS_AVAILABLE = True
    RAGAS_VERSION = ragas.__version__
    logging.info(f"Ragas版本: {RAGAS_VERSION}")
except ImportError:
    logging.warning("Ragas not available")
    RAGAS_AVAILABLE = False
    RAGAS_VERSION = None
    # 创建占位符基类
    class BaseRagasLLM:
        pass
    class BaseRagasEmbeddings:
        pass

from api.db.services.llm_service import LLMBundle


class RAGFlowLLMAdapterV2(BaseRagasLLM):
    """RAGFlow LLM适配器 - Ragas 0.2.x版本"""

    def __init__(self, llm_bundle: LLMBundle):
        # Ragas 0.2.x可能不需要调用super().__init__()
        self.llm_bundle = llm_bundle
        self.call_count = 0
        logging.info(f"[RAGFlowLLMAdapterV2] 初始化完成，Ragas版本: {RAGAS_VERSION}")

    def generate_text(self, prompt: str, **kwargs) -> str:
        """生成单个文本"""
        self.call_count += 1
        logging.info(f"[RAGFlowLLMAdapterV2] generate_text调用 #{self.call_count}: prompt长度={len(prompt)}")

        try:
            # LLMBundle.chat需要三个参数: system, history, gen_conf
            gen_conf = {
                "temperature": kwargs.get("temperature", 0.1),
                "max_tokens": kwargs.get("max_tokens", 1024),
                "top_p": kwargs.get("top_p", 0.9)
            }

            response = self.llm_bundle.chat(prompt, [], gen_conf)

            # 处理不同类型的响应，确保返回字符串
            if isinstance(response, dict):
                result = response.get("content", str(response))
            elif isinstance(response, str):
                result = response
            else:
                result = str(response)

            # 确保返回的是字符串
            return str(result)

        except Exception as e:
            logging.error(f"[RAGFlowLLMAdapterV2] generate_text失败: {e}")
            return "生成失败"

    def generate(self, prompts: Union[str, List[str], StringPromptValue, List[StringPromptValue]], **kwargs) -> List[str]:
        """批量生成文本"""
        self.call_count += 1
        logging.info(f"[RAGFlowLLMAdapterV2] generate调用 #{self.call_count}: prompts类型={type(prompts)}")

        # 处理不同类型的输入
        if isinstance(prompts, str):
            prompt_list = [prompts]
        elif isinstance(prompts, list):
            prompt_list = prompts
        else:
            # 处理LangChain PromptValue对象
            if hasattr(prompts, 'text'):
                prompt_list = [prompts.text]
            else:
                prompt_list = [str(prompts)]

        results = []
        for prompt in prompt_list:
            # 处理PromptValue对象
            if hasattr(prompt, 'text'):
                prompt_text = prompt.text
            else:
                prompt_text = str(prompt)

            result = self.generate_text(prompt_text, **kwargs)
            results.append(str(result))  # 确保是字符串

        # 确保返回字符串列表
        return [str(item) for item in results]

    async def agenerate_text(self, prompt: str, **kwargs) -> str:
        """异步生成单个文本 - 直接调用同步方法避免异步问题"""
        logging.info(f"[RAGFlowLLMAdapterV2] agenerate_text调用（同步模式）: prompt长度={len(prompt)}")
        # 直接调用同步方法，避免Ragas 0.2.15的异步bug
        return self.generate_text(prompt, **kwargs)

    async def agenerate(self, prompts: Union[str, List[str], StringPromptValue, List[StringPromptValue]], **kwargs) -> List[str]:
        """异步批量生成文本 - 直接调用同步方法避免异步问题"""
        logging.info(f"[RAGFlowLLMAdapterV2] agenerate调用（同步模式）: prompts类型={type(prompts)}")
        # 直接调用同步方法，避免Ragas 0.2.15的异步bug
        return self.generate(prompts, **kwargs)


class RAGFlowEmbeddingAdapterV2(BaseRagasEmbeddings):
    """RAGFlow Embedding适配器 - Ragas 0.2.x版本"""

    def __init__(self, embedding_bundle: LLMBundle):
        # Ragas 0.2.x可能不需要调用super().__init__()
        self.embedding_bundle = embedding_bundle
        logging.info(f"[RAGFlowEmbeddingAdapterV2] 初始化完成，Ragas版本: {RAGAS_VERSION}")

    def embed_query(self, text: str) -> List[float]:
        """嵌入查询文本"""
        logging.info(f"[RAGFlowEmbeddingAdapterV2] embed_query调用: text长度={len(text)}")
        try:
            embeddings, _ = self.embedding_bundle.encode_queries(text)

            # 处理numpy数组
            if hasattr(embeddings, 'tolist'):
                result = embeddings.tolist()
            elif isinstance(embeddings, list):
                result = [float(x) for x in embeddings]
            else:
                result = [float(embeddings)]

            logging.info(f"[RAGFlowEmbeddingAdapterV2] embed_query完成，向量维度={len(result)}")
            return result

        except Exception as e:
            logging.error(f"[RAGFlowEmbeddingAdapterV2] embed_query失败: {e}")
            return [0.0] * 768

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """嵌入文档列表"""
        logging.info(f"[RAGFlowEmbeddingAdapterV2] embed_documents调用: texts数量={len(texts)}")
        try:
            results = []
            for text in texts:
                result = self.embed_query(text)
                results.append(result)

            logging.info(f"[RAGFlowEmbeddingAdapterV2] embed_documents完成，向量数量={len(results)}")
            return results

        except Exception as e:
            logging.error(f"[RAGFlowEmbeddingAdapterV2] embed_documents失败: {e}")
            return [[0.0] * 768] * len(texts)

    async def aembed_query(self, text: str) -> List[float]:
        """异步嵌入查询文本 - 直接调用同步方法避免异步问题"""
        logging.info(f"[RAGFlowEmbeddingAdapterV2] aembed_query调用（同步模式）: text长度={len(text)}")
        # 直接调用同步方法，避免Ragas 0.2.15的异步bug
        return self.embed_query(text)

    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
        """异步嵌入文档列表 - 直接调用同步方法避免异步问题"""
        logging.info(f"[RAGFlowEmbeddingAdapterV2] aembed_documents调用（同步模式）: texts数量={len(texts)}")
        # 直接调用同步方法，避免Ragas 0.2.15的异步bug
        return self.embed_documents(texts)

    # Ragas 0.2.x可能需要的额外方法
    def embed_text(self, text: str) -> List[float]:
        """嵌入单个文本"""
        return self.embed_query(text)

    async def aembed_text(self, text: str) -> List[float]:
        """异步嵌入单个文本 - 直接调用同步方法避免异步问题"""
        return self.embed_text(text)


def create_ragas_models_v2(llm_bundle: LLMBundle, embedding_bundle: LLMBundle):
    """创建Ragas 0.2.x兼容的模型"""
    if not RAGAS_AVAILABLE:
        raise ImportError("Ragas not available")

    llm_adapter = RAGFlowLLMAdapterV2(llm_bundle)
    embedding_adapter = RAGFlowEmbeddingAdapterV2(embedding_bundle)

    logging.info(f"Ragas {RAGAS_VERSION} 模型适配器创建完成")
    return llm_adapter, embedding_adapter


def setup_ragas_models_v2(llm_bundle: LLMBundle, embedding_bundle: LLMBundle):
    """设置Ragas 0.2.x模型"""
    try:
        llm_adapter, embedding_adapter = create_ragas_models_v2(llm_bundle, embedding_bundle)

        # Ragas 0.2.x可能不支持全局设置，直接返回适配器
        logging.info(f"Ragas {RAGAS_VERSION} 模型适配器设置完成")
        return llm_adapter, embedding_adapter

    except Exception as e:
        logging.error(f"创建Ragas {RAGAS_VERSION} 模型适配器失败: {e}")
        raise e
