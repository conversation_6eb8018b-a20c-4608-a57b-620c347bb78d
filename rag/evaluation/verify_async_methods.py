#!/usr/bin/env python3
"""
验证异步方法是否正确添加
"""

import sys
import os
import logging
import inspect

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def verify_adapter_methods():
    """验证适配器方法"""
    logger.info("验证适配器方法...")
    
    try:
        from rag.evaluation.ragas_adapter import RAGFlowLLMAdapter, RAGFlowEmbeddingAdapter
        
        # 检查LLM适配器方法
        logger.info("检查RAGFlowLLMAdapter方法:")
        llm_methods = [method for method in dir(RAGFlowLLMAdapter) if not method.startswith('_')]
        for method in llm_methods:
            method_obj = getattr(RAGFlowLLMAdapter, method)
            if callable(method_obj):
                is_async = inspect.iscoroutinefunction(method_obj)
                logger.info(f"  - {method}: {'异步' if is_async else '同步'}")
        
        # 检查Embedding适配器方法
        logger.info("\n检查RAGFlowEmbeddingAdapter方法:")
        emb_methods = [method for method in dir(RAGFlowEmbeddingAdapter) if not method.startswith('_')]
        for method in emb_methods:
            method_obj = getattr(RAGFlowEmbeddingAdapter, method)
            if callable(method_obj):
                is_async = inspect.iscoroutinefunction(method_obj)
                logger.info(f"  - {method}: {'异步' if is_async else '同步'}")
        
        # 检查关键方法是否存在
        required_llm_methods = ['generate', 'agenerate', 'agenerate_text']
        required_emb_methods = ['embed_query', 'embed_documents', 'aembed_query', 'aembed_documents']
        
        logger.info("\n检查必需的LLM方法:")
        for method in required_llm_methods:
            if hasattr(RAGFlowLLMAdapter, method):
                method_obj = getattr(RAGFlowLLMAdapter, method)
                is_async = inspect.iscoroutinefunction(method_obj)
                logger.info(f"  ✅ {method}: {'异步' if is_async else '同步'}")
            else:
                logger.error(f"  ❌ {method}: 缺失")
        
        logger.info("\n检查必需的Embedding方法:")
        for method in required_emb_methods:
            if hasattr(RAGFlowEmbeddingAdapter, method):
                method_obj = getattr(RAGFlowEmbeddingAdapter, method)
                is_async = inspect.iscoroutinefunction(method_obj)
                logger.info(f"  ✅ {method}: {'异步' if is_async else '同步'}")
            else:
                logger.error(f"  ❌ {method}: 缺失")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 验证适配器方法失败: {e}")
        return False


def check_method_signatures():
    """检查方法签名"""
    logger.info("\n检查方法签名...")
    
    try:
        from rag.evaluation.ragas_adapter import RAGFlowLLMAdapter, RAGFlowEmbeddingAdapter
        
        # 检查LLM方法签名
        logger.info("LLM适配器方法签名:")
        
        if hasattr(RAGFlowLLMAdapter, 'agenerate_text'):
            sig = inspect.signature(RAGFlowLLMAdapter.agenerate_text)
            logger.info(f"  agenerate_text{sig}")
        
        if hasattr(RAGFlowLLMAdapter, 'agenerate'):
            sig = inspect.signature(RAGFlowLLMAdapter.agenerate)
            logger.info(f"  agenerate{sig}")
        
        # 检查Embedding方法签名
        logger.info("\nEmbedding适配器方法签名:")
        
        if hasattr(RAGFlowEmbeddingAdapter, 'aembed_query'):
            sig = inspect.signature(RAGFlowEmbeddingAdapter.aembed_query)
            logger.info(f"  aembed_query{sig}")
        
        if hasattr(RAGFlowEmbeddingAdapter, 'aembed_documents'):
            sig = inspect.signature(RAGFlowEmbeddingAdapter.aembed_documents)
            logger.info(f"  aembed_documents{sig}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查方法签名失败: {e}")
        return False


def check_source_code():
    """检查源代码"""
    logger.info("\n检查源代码...")
    
    try:
        import rag.evaluation.ragas_adapter as adapter_module
        
        # 获取源代码
        source = inspect.getsource(adapter_module)
        
        # 检查关键方法是否存在
        checks = [
            ("agenerate_text方法", "async def agenerate_text"),
            ("agenerate方法", "async def agenerate"),
            ("aembed_query方法", "async def aembed_query"),
            ("aembed_documents方法", "async def aembed_documents"),
            ("run_in_executor调用", "run_in_executor"),
            ("asyncio.gather调用", "asyncio.gather"),
        ]
        
        for check_name, pattern in checks:
            if pattern in source:
                logger.info(f"  ✅ {check_name}: 存在")
            else:
                logger.error(f"  ❌ {check_name}: 缺失")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查源代码失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("🚀 开始验证异步方法修复")
    
    tests = [
        ("适配器方法验证", verify_adapter_methods),
        ("方法签名检查", check_method_signatures),
        ("源代码检查", check_source_code),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 {test_name}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} 通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
    
    logger.info(f"\n📊 验证结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 异步方法修复验证成功！")
        logger.info("\n主要修复:")
        logger.info("1. ✅ 添加了agenerate_text异步方法")
        logger.info("2. ✅ 修复了agenerate方法的实现")
        logger.info("3. ✅ 确保所有异步方法正确实现")
        logger.info("4. ✅ 使用run_in_executor处理同步调用")
        
        logger.info("\n现在Ragas应该能够正确调用异步方法，不再出现:")
        logger.info("TypeError(object list can't be used in 'await' expression)")
        
        return True
    else:
        logger.error("⚠️  验证失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
