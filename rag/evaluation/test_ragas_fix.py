#!/usr/bin/env python3
"""
测试Ragas模型适配器修复
验证API调用使用正确的域名
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_ragas_adapter_creation():
    """测试Ragas适配器创建"""
    logger.info("测试Ragas适配器创建...")

    try:
        from rag.evaluation.ragas_adapter import create_ragas_models, setup_ragas_models
        from api.db.services.llm_service import LLMBundle
        from api.db import LLMType

        logger.info("✅ Ragas适配器模块导入成功")

        # 模拟LLMBundle创建（不实际调用API）
        logger.info("模拟LLMBundle创建...")
        logger.info("create_ragas_models函数应该创建RAGFlowLLMAdapter和RAGFlowEmbeddingAdapter")
        logger.info("setup_ragas_models函数应该返回适配器而不依赖全局设置")

        return True

    except Exception as e:
        logger.error(f"❌ Ragas适配器测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def test_simple_evaluation():
    """测试简单的评估功能"""
    logger.info("开始测试简单评估功能...")

    # 测试参数
    tenant_id = "test_tenant"
    kb_id = "test_kb"

    try:
        # 创建评估器
        evaluator = RagasEvaluator(tenant_id, kb_id)
        logger.info("✅ 评估器创建成功")

        # 创建测试样本
        sample = EvaluationSample(
            question="什么是人工智能？",
            ground_truth="人工智能是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。"
        )

        # 创建评估配置
        config = EvaluationConfig(
            metrics=["faithfulness", "answer_relevancy"],
            similarity_threshold=0.2,
            vector_similarity_weight=0.3,
            top_n=5,
            top_k=100
        )

        logger.info("✅ 测试配置创建成功")
        return True

    except Exception as e:
        logger.error(f"❌ 简单评估测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def test_dataset_manager():
    """测试数据集管理器"""
    logger.info("开始测试数据集管理器...")

    tenant_id = "test_tenant"

    try:
        # 创建数据集管理器
        manager = DatasetManager(tenant_id)
        logger.info("✅ 数据集管理器创建成功")

        # 测试可用指标
        available_metrics = RagasEvaluator.get_available_metrics()
        logger.info(f"✅ 可用评估指标: {available_metrics}")

        return True

    except Exception as e:
        logger.error(f"❌ 数据集管理器测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def test_evaluation_runner():
    """测试评估运行器"""
    logger.info("开始测试评估运行器...")

    tenant_id = "test_tenant"

    try:
        # 创建评估运行器
        runner = EvaluationRunner(tenant_id)
        logger.info("✅ 评估运行器创建成功")

        return True

    except Exception as e:
        logger.error(f"❌ 评估运行器测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始RAG评估功能测试")

    tests = [
        ("Ragas可用性", test_ragas_availability),
        ("简单评估功能", test_simple_evaluation),
        ("数据集管理器", test_dataset_manager),
        ("评估运行器", test_evaluation_runner),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        logger.info(f"\n📋 测试: {test_name}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")

    logger.info(f"\n📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        logger.info("🎉 所有测试通过！RAG评估功能修复成功")
        return True
    else:
        logger.error("⚠️  部分测试失败，需要进一步修复")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
