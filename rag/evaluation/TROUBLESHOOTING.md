# RAG评估功能故障排除指南

## 🔍 常见问题及解决方案

### 1. 模型授权问题

**错误信息**: `Model(xxx) not authorized`

**原因分析**:
- 模型不存在
- 模型不属于当前租户
- 模型未启用
- 模型类型不匹配

**解决方案**:
```bash
# 检查模型授权状态
python rag/evaluation/fix_model_auth.py <tenant_id> <kb_id>

# 诊断评估问题
python rag/evaluation/diagnose_evaluation_issues.py <tenant_id>
```

### 2. 前端显示"已成功"但实际失败

**原因分析**:
- 任务启动成功，但执行过程中失败
- 前端只检查了启动状态，未检查执行状态

**解决方案**:
1. 检查服务端日志中的详细错误信息
2. 在任务管理页面查看任务的实际状态
3. 等待任务完成后再查看结果

### 3. 查看结果功能不可用

**原因分析**:
- 前端组件未正确实现跳转逻辑
- 后端API返回数据格式不匹配

**解决方案**:
- 已修复前端跳转逻辑，点击"查看结果"会自动跳转到结果分析页面
- 后端API已改进，返回更完整的结果数据

## 🛠️ 诊断工具

### 1. 模型授权检查工具
```bash
python rag/evaluation/fix_model_auth.py <tenant_id> <kb_id>
```

**功能**:
- 检查知识库默认模型配置
- 验证模型授权状态
- 提供可用模型建议

### 2. 评估问题诊断工具
```bash
python rag/evaluation/diagnose_evaluation_issues.py <tenant_id> [hours]
```

**功能**:
- 分析最近失败的评估任务
- 识别常见配置问题
- 提供修复建议

### 3. 简化功能测试
```bash
python rag/evaluation/simple_test.py
```

**功能**:
- 测试Ragas库可用性
- 验证核心组件功能
- 检查基本配置

## 📋 完整的问题解决流程

### 步骤1: 诊断问题
```bash
# 1. 检查模型授权
python rag/evaluation/fix_model_auth.py <tenant_id> <kb_id>

# 2. 诊断失败任务
python rag/evaluation/diagnose_evaluation_issues.py <tenant_id>

# 3. 测试基本功能
python rag/evaluation/simple_test.py
```

### 步骤2: 修复配置
1. **模型配置问题**:
   - 使用诊断工具提供的可用模型ID
   - 在前端创建新任务时选择正确的模型
   - 确保模型属于当前租户且已启用

2. **知识库问题**:
   - 验证知识库存在且有数据
   - 检查知识库的默认模型配置
   - 确保知识库属于当前租户

3. **网络和API问题**:
   - 检查模型API密钥是否有效
   - 验证API端点是否可访问
   - 确认网络连接正常

### 步骤3: 重新运行评估
1. 在前端创建新的评估任务
2. 使用修复后的模型配置
3. 监控任务执行状态
4. 查看详细结果

## 🔧 配置建议

### 推荐的评估配置
```json
{
  "metrics": [
    "faithfulness",
    "answer_relevancy", 
    "context_precision",
    "context_recall"
  ],
  "retrieval_config": {
    "similarity_threshold": 0.2,
    "vector_similarity_weight": 0.3,
    "top_n": 5,
    "top_k": 100
  },
  "llm_config": {
    "embedding_id": "your_embedding_model_id",
    "llm_id": "your_chat_model_id",
    "rerank_id": "your_rerank_model_id"  // 可选
  }
}
```

### 模型选择建议
1. **Embedding模型**: 选择与知识库索引时相同的模型
2. **Chat模型**: 选择支持中文且性能较好的模型
3. **Rerank模型**: 可选，用于提高检索精度

## 📊 监控和日志

### 关键日志位置
- 服务端日志: `logs/ragflow_server.log`
- 任务执行日志: `logs/task_executor_*.log`
- 评估专用日志: 在代码中搜索 "评估" 相关的日志

### 关键监控指标
- 任务成功率
- 平均执行时间
- 模型调用成功率
- 错误类型分布

## 🆘 获取帮助

如果以上方法都无法解决问题，请：

1. **收集信息**:
   - 租户ID和知识库ID
   - 完整的错误日志
   - 使用的模型配置
   - 评估任务配置

2. **运行诊断**:
   ```bash
   python rag/evaluation/diagnose_evaluation_issues.py <tenant_id> > diagnosis.log 2>&1
   ```

3. **提供反馈**:
   - 将诊断结果和错误日志一起提供
   - 描述期望的行为和实际行为
   - 说明复现步骤

## 🎯 最佳实践

1. **模型管理**:
   - 定期检查模型状态
   - 使用稳定的模型配置
   - 备份重要的模型配置

2. **任务管理**:
   - 使用描述性的任务名称
   - 定期清理失败的任务
   - 监控任务执行状态

3. **数据管理**:
   - 确保评估数据集质量
   - 定期备份评估结果
   - 使用版本控制管理配置

4. **性能优化**:
   - 合理设置并发数
   - 监控资源使用情况
   - 优化评估指标选择
