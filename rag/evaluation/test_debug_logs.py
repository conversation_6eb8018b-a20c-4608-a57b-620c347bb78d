#!/usr/bin/env python3
"""
测试调试日志和重复方法修复
"""

import sys
import os
import logging
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_llm_adapter_async():
    """测试LLM适配器异步方法"""
    logger.info("测试LLM适配器异步方法...")
    
    try:
        from rag.evaluation.ragas_adapter import RAGFlowLLMAdapter
        
        # 模拟LLMBundle
        class MockLLMBundle:
            def chat(self, system, history, gen_conf):
                return {"content": f"异步响应: {system[:30]}..."}
        
        adapter = RAGFlowLLMAdapter(MockLLMBundle())
        
        # 测试agenerate_text方法
        logger.info("测试agenerate_text方法...")
        result = await adapter.agenerate_text("测试异步提示")
        logger.info(f"✅ agenerate_text结果: {result}")
        
        # 测试agenerate方法
        logger.info("测试agenerate方法...")
        
        # 模拟StringPromptValue
        class MockPromptValue:
            def __init__(self, text):
                self.text = text
        
        # 测试不同类型的prompts
        test_cases = [
            ["提示1", "提示2"],  # 字符串列表
            "单个提示",  # 单个字符串
            MockPromptValue("PromptValue提示"),  # 单个PromptValue
        ]
        
        for i, prompts in enumerate(test_cases):
            logger.info(f"测试用例 {i+1}: {type(prompts)}")
            results = await adapter.agenerate(prompts)
            logger.info(f"✅ agenerate结果: {results}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ LLM适配器异步方法测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


async def test_embedding_adapter_async():
    """测试Embedding适配器异步方法"""
    logger.info("测试Embedding适配器异步方法...")
    
    try:
        from rag.evaluation.ragas_adapter import RAGFlowEmbeddingAdapter
        import numpy as np
        
        # 模拟LLMBundle
        class MockEmbeddingBundle:
            def encode_queries(self, text):
                # 返回numpy数组模拟真实情况
                return np.array([0.1, 0.2, 0.3]), None
        
        adapter = RAGFlowEmbeddingAdapter(MockEmbeddingBundle())
        
        # 测试aembed_query方法
        logger.info("测试aembed_query方法...")
        result = await adapter.aembed_query("测试文本")
        logger.info(f"✅ aembed_query结果: {type(result)} - {result}")
        
        # 测试aembed_documents方法
        logger.info("测试aembed_documents方法...")
        texts = ["文本1", "文本2", "文本3"]
        results = await adapter.aembed_documents(texts)
        logger.info(f"✅ aembed_documents结果: {len(results)} 个向量")
        
        # 测试aembed_text方法
        logger.info("测试aembed_text方法...")
        result = await adapter.aembed_text("测试文本")
        logger.info(f"✅ aembed_text结果: {type(result)} - {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Embedding适配器异步方法测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def test_method_definitions():
    """测试方法定义是否正确"""
    logger.info("测试方法定义...")
    
    try:
        from rag.evaluation.ragas_adapter import RAGFlowLLMAdapter, RAGFlowEmbeddingAdapter
        import inspect
        
        # 检查LLM适配器方法
        logger.info("检查LLM适配器方法定义:")
        llm_methods = [method for method in dir(RAGFlowLLMAdapter) if not method.startswith('_')]
        for method in llm_methods:
            method_obj = getattr(RAGFlowLLMAdapter, method)
            if callable(method_obj):
                is_async = inspect.iscoroutinefunction(method_obj)
                sig = inspect.signature(method_obj)
                logger.info(f"  - {method}: {'异步' if is_async else '同步'} {sig}")
        
        # 检查是否有重复的agenerate_text定义
        agenerate_text_methods = [method for method in dir(RAGFlowLLMAdapter) if method == 'agenerate_text']
        logger.info(f"agenerate_text方法数量: {len(agenerate_text_methods)}")
        
        if len(agenerate_text_methods) > 1:
            logger.error("❌ 发现重复的agenerate_text方法定义")
            return False
        
        # 检查Embedding适配器方法
        logger.info("\n检查Embedding适配器方法定义:")
        emb_methods = [method for method in dir(RAGFlowEmbeddingAdapter) if not method.startswith('_')]
        for method in emb_methods:
            method_obj = getattr(RAGFlowEmbeddingAdapter, method)
            if callable(method_obj):
                is_async = inspect.iscoroutinefunction(method_obj)
                sig = inspect.signature(method_obj)
                logger.info(f"  - {method}: {'异步' if is_async else '同步'} {sig}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 方法定义测试失败: {e}")
        return False


def test_source_code_check():
    """检查源代码是否有问题"""
    logger.info("检查源代码...")
    
    try:
        import rag.evaluation.ragas_adapter as adapter_module
        import inspect
        
        # 获取源代码
        source = inspect.getsource(adapter_module)
        
        # 检查是否有重复的方法定义
        agenerate_text_count = source.count('def agenerate_text')
        async_agenerate_text_count = source.count('async def agenerate_text')
        
        logger.info(f"源代码中agenerate_text定义数量: {agenerate_text_count}")
        logger.info(f"源代码中async agenerate_text定义数量: {async_agenerate_text_count}")
        
        if agenerate_text_count > 1 or async_agenerate_text_count > 1:
            logger.error("❌ 源代码中发现重复的方法定义")
            return False
        
        # 检查关键方法是否存在
        required_patterns = [
            "async def agenerate_text",
            "async def agenerate",
            "async def aembed_query",
            "async def aembed_documents",
            "async def aembed_text",
            "def embed_text",
        ]
        
        for pattern in required_patterns:
            if pattern in source:
                logger.info(f"✅ 找到: {pattern}")
            else:
                logger.error(f"❌ 缺失: {pattern}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 源代码检查失败: {e}")
        return False


async def main():
    """主测试函数"""
    logger.info("🚀 开始调试日志和重复方法修复测试")
    
    tests = [
        ("方法定义检查", test_method_definitions),
        ("源代码检查", test_source_code_check),
        ("LLM适配器异步测试", test_llm_adapter_async),
        ("Embedding适配器异步测试", test_embedding_adapter_async),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 测试: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 调试日志和重复方法修复验证成功！")
        logger.info("\n主要修复:")
        logger.info("1. ✅ 移除了重复的agenerate_text方法定义")
        logger.info("2. ✅ 添加了详细的调试日志")
        logger.info("3. ✅ 确保所有异步方法正确实现")
        logger.info("4. ✅ 添加了embed_text和aembed_text方法")
        
        logger.info("\n现在重新运行评估任务，应该能看到详细的调试日志")
        logger.info("这将帮助我们定位异步调用错误的具体位置")
        
        return True
    else:
        logger.error("⚠️  部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
