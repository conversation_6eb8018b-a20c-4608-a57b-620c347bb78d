#!/usr/bin/env python3
"""
测试新的评估配置逻辑
验证自动模型配置功能
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))

from rag.evaluation.evaluation_runner import EvaluationRunner
from rag.evaluation.dataset_manager import DatasetManager
from api.db.db_models import LLM, Knowledgebase
from api.db import LLMType

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_auto_model_config(tenant_id: str, kb_id: str):
    """测试自动模型配置"""
    logger.info("测试自动模型配置功能...")
    
    try:
        # 获取知识库信息
        kb = Knowledgebase.get_or_none(
            (Knowledgebase.id == kb_id) & 
            (Knowledgebase.tenant_id == tenant_id)
        )
        
        if not kb:
            logger.error(f"知识库 {kb_id} 不存在")
            return False
        
        logger.info(f"知识库信息:")
        logger.info(f"  ID: {kb.id}")
        logger.info(f"  名称: {kb.name}")
        logger.info(f"  Embedding模型: {kb.embd_id}")
        
        # 获取可用的Chat模型
        chat_models = LLM.select().where(
            (LLM.tenant_id == tenant_id) &
            (LLM.model_type == LLMType.CHAT.value) &
            (LLM.status == 1)
        ).limit(3)
        
        logger.info(f"可用的Chat模型:")
        for model in chat_models:
            logger.info(f"  - {model.id}: {model.llm_name}")
        
        if not chat_models:
            logger.error("没有可用的Chat模型")
            return False
        
        # 选择第一个可用的Chat模型进行测试
        test_llm_id = chat_models[0].id
        logger.info(f"使用Chat模型进行测试: {test_llm_id}")
        
        # 创建测试数据集
        dataset_manager = DatasetManager(tenant_id)
        
        # 检查是否已有测试数据集
        datasets = dataset_manager.list_datasets()
        test_dataset = None
        for ds in datasets:
            if ds["name"] == "自动配置测试数据集":
                test_dataset = ds
                break
        
        if not test_dataset:
            # 创建测试数据集
            dataset_id = dataset_manager.create_dataset(
                name="自动配置测试数据集",
                kb_id=kb_id,
                created_by="system",
                description="用于测试自动模型配置的数据集"
            )
            
            # 添加测试样本
            dataset_manager.add_sample(
                dataset_id=dataset_id,
                question="什么是人工智能？",
                ground_truth="人工智能是计算机科学的一个分支，旨在创建能够执行通常需要人类智能的任务的系统。"
            )
            
            logger.info(f"创建测试数据集: {dataset_id}")
        else:
            dataset_id = test_dataset["id"]
            logger.info(f"使用现有测试数据集: {dataset_id}")
        
        # 测试评估任务创建
        runner = EvaluationRunner(tenant_id)
        
        try:
            task_id = runner.create_task(
                name="自动配置测试任务",
                dataset_id=dataset_id,
                kb_id=kb_id,
                created_by="system",
                metrics=["faithfulness", "answer_relevancy"],
                llm_id=test_llm_id  # 指定LLM模型
            )
            
            logger.info(f"✅ 成功创建评估任务: {task_id}")
            
            # 获取任务详情验证配置
            task = runner.get_task(task_id)
            if task:
                import json
                llm_config = json.loads(task.llm_config)
                retrieval_config = json.loads(task.retrieval_config)
                
                logger.info(f"任务配置验证:")
                logger.info(f"  LLM配置: {llm_config}")
                logger.info(f"  检索配置: {retrieval_config}")
                
                # 验证embedding模型是否自动设置
                if llm_config.get("embedding_id") == kb.embd_id:
                    logger.info("✅ Embedding模型自动配置正确")
                else:
                    logger.error("❌ Embedding模型自动配置失败")
                
                # 验证LLM模型是否正确设置
                if llm_config.get("llm_id") == test_llm_id:
                    logger.info("✅ LLM模型配置正确")
                else:
                    logger.error("❌ LLM模型配置失败")
                
                # 验证默认检索配置
                expected_config = {
                    "similarity_threshold": 0.2,
                    "vector_similarity_weight": 0.3,
                    "top_n": 5,
                    "top_k": 100
                }
                
                config_correct = all(
                    retrieval_config.get(key) == value 
                    for key, value in expected_config.items()
                )
                
                if config_correct:
                    logger.info("✅ 默认检索配置正确")
                else:
                    logger.error("❌ 默认检索配置不正确")
                    logger.error(f"期望: {expected_config}")
                    logger.error(f"实际: {retrieval_config}")
            
            return True
            
        except Exception as e:
            logger.error(f"创建评估任务失败: {e}")
            return False
        
    except Exception as e:
        logger.error(f"测试自动模型配置失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False


def test_llm_api():
    """测试LLM API"""
    logger.info("测试LLM API...")
    
    try:
        import requests
        import json
        
        # 模拟API调用
        url = "http://localhost:9380/api/evaluation/llms"
        headers = {
            "Authorization": "Bearer your_token_here",
            "Content-Type": "application/json"
        }
        
        # 注意：这里需要实际的认证token
        logger.info("LLM API端点: /api/evaluation/llms")
        logger.info("该API将返回当前租户可用的Chat类型LLM模型")
        
        return True
        
    except Exception as e:
        logger.error(f"测试LLM API失败: {e}")
        return False


def main():
    """主测试函数"""
    if len(sys.argv) < 3:
        print("用法: python test_new_config.py <tenant_id> <kb_id>")
        print("示例: python test_new_config.py test_tenant test_kb")
        sys.exit(1)
    
    tenant_id = sys.argv[1]
    kb_id = sys.argv[2]
    
    logger.info("🚀 开始测试新的评估配置逻辑")
    logger.info(f"租户ID: {tenant_id}")
    logger.info(f"知识库ID: {kb_id}")
    
    tests = [
        ("自动模型配置", lambda: test_auto_model_config(tenant_id, kb_id)),
        ("LLM API", test_llm_api),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 测试: {test_name}")
        try:
            if test_func():
                logger.info(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} 测试失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 测试异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！新的配置逻辑工作正常")
        return True
    else:
        logger.error("⚠️  部分测试失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
