#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
import os
import logging
from api.utils import get_base_config, decrypt_database_config
from api.utils.file_utils import get_project_base_directory
from api.utils.config_loader import get_es_config, get_config, get_zebra_config, get_mss_s3_config, get_squirrel_config

# Server
RAG_CONF_PATH = os.path.join(get_project_base_directory(), "conf")

# 使用新的配置加载机制获取配置
ES = get_es_config()
ZEBRA = get_zebra_config()
MSS_S3 = get_mss_s3_config()
MT_REDIS = get_squirrel_config()

# 后续代码保持不变，但不再需要环境变量处理部分
# 支持通过环境变量覆盖配置
# 处理布尔值配置，确保即使配置文件中使用了YAML的布尔值，也能正确处理
def get_bool_config(env_var, config_key, default="false"):
    config_value = ES.get(config_key, default)
    # 如果配置值已经是布尔值，直接使用；否则将字符串转为布尔值
    if isinstance(config_value, bool):
        default_value = config_value
    else:
        default_value = str(config_value).lower() == "true"
    # 从环境变量获取值，并转换为布尔值
    env_value = os.environ.get(env_var, "").lower()
    if env_value == "true":
        return True
    elif env_value == "false":
        return False
    else:
        return default_value

# 注意: 这些赋值已在get_es_config中处理，此处仅保留以确保向后兼容
# 如果项目的其他部分仍在使用settings.ES，这些值仍然有效
# ES["use_company_infra"] = get_bool_config("USE_COMPANY_INFRA", "use_company_infra")
# ES["cluster_name"] = os.environ.get("ES_CLUSTER_NAME", ES.get("cluster_name", ""))
# ES["app_key"] = os.environ.get("ES_APP_KEY", ES.get("app_key", ""))
# ES["access_key"] = os.environ.get("ES_ACCESS_KEY", ES.get("access_key", ""))
# ES["discovery_url"] = os.environ.get("ES_DISCOVERY_URL", ES.get("discovery_url", ""))
# ES["port"] = int(os.environ.get("ES_PORT", ES.get("port", 8080)))
# ES["use_ssl"] = get_bool_config("ES_USE_SSL", "use_ssl")
# ES["verify_certs"] = get_bool_config("ES_VERIFY_CERTS", "verify_certs")

# 其余配置保持不变
INFINITY = get_base_config("infinity", {"uri": "infinity:23817"})
AZURE = get_base_config("azure", {})
S3 = get_base_config("s3", {})
MINIO = decrypt_database_config(name="minio")
OSS = get_base_config("oss", {})
try:
    REDIS = decrypt_database_config(name="redis")
except Exception:
    REDIS = {}
    pass

DOC_MAXIMUM_SIZE = int(os.environ.get("MAX_CONTENT_LENGTH", 128 * 1024 * 1024))

SVR_QUEUE_NAME = "rag_flow_svr_queue"
SVR_QUEUE_RETENTION = 60*60
SVR_QUEUE_MAX_LEN = 1024
SVR_CONSUMER_NAME = "rag_flow_svr_consumer"
SVR_CONSUMER_GROUP_NAME = "rag_flow_svr_consumer_group"
PAGERANK_FLD = "pagerank_fea"
TAG_FLD = "tag_feas"
USE_SQUIRREL = True  # True使用Squirrel，False使用Redis

def print_rag_settings():
    logging.info(f"MAX_CONTENT_LENGTH: {DOC_MAXIMUM_SIZE}")
    logging.info(f"SERVER_QUEUE_MAX_LEN: {SVR_QUEUE_MAX_LEN}")
    logging.info(f"SERVER_QUEUE_RETENTION: {SVR_QUEUE_RETENTION}")
    logging.info(f"MAX_FILE_COUNT_PER_USER: {int(os.environ.get('MAX_FILE_NUM_PER_USER', 0))}")
    if ES.get("use_company_infra"):
        logging.info(f"使用公司ES8集群: {ES.get('cluster_name')}")
    # 打印MSS S3配置
    if MSS_S3:
        if MSS_S3.get("appkey"):
            logging.info(f"使用美团MSS S3存储 (透明密钥): appkey={MSS_S3.get('appkey')}, endpoint={MSS_S3.get('endpoint')}")
        elif MSS_S3.get("access_key"):
            logging.info(f"使用美团MSS S3存储 (账号密钥): endpoint={MSS_S3.get('endpoint')}")
        else:
            logging.info("MSS S3配置不完整，请检查配置")
    
    # 打印美团Redis配置
    if MT_REDIS:
        logging.info(f"使用美团Squirrel SDK连接Redis: cluster={MT_REDIS.get('cluster_name')}, appkey={MT_REDIS.get('appkey')}")
    else:
        logging.info("美团Redis配置不存在，请检查配置")