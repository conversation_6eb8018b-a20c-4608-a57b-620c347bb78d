# OCR模型文件说明

## 关于OCR模型

OCR（光学字符识别）功能需要两个主要的模型文件：
- `det.onnx` - 文本检测模型
- `rec.onnx` - 文本识别模型

这些模型文件在项目运行时位于 `rag/res/deepdoc/` 目录。

## 使用下载脚本

我们提供了一个自动下载并放置模型的脚本：

```bash
# 在项目根目录下运行
python script/deepdoc.py
```

脚本将自动：
1. 从Hugging Face下载必要的模型文件
2. 将它们放置在正确的位置
3. 清理临时文件

## 手动下载

如果自动下载失败，您可以：

1. 访问 https://huggingface.co/InfiniFlow/deepdoc
2. 下载 `det.onnx` 和 `rec.onnx` 文件
3. 手动创建 `rag/res/deepdoc/` 目录（如果不存在）
4. 将这两个文件放在该目录中

## 使用Git LFS（开发者）

如果您是项目开发者，可以考虑将这些模型文件通过Git LFS添加到仓库中：

```bash
# 初始化Git LFS
git lfs install

# 将模型文件添加到LFS跟踪
git lfs track "rag/res/deepdoc/*.onnx"

# 添加.gitattributes到仓库
git add .gitattributes

# 添加模型文件
git add rag/res/deepdoc/det.onnx rag/res/deepdoc/rec.onnx
git commit -m "添加OCR模型文件"
```

这样其他开发者克隆仓库时可以自动获取模型文件。

## 故障排除

如果OCR功能不正常工作，请检查：

1. 确认 `rag/res/deepdoc/` 目录中存在 `det.onnx` 和 `rec.onnx` 文件
2. 文件大小是否正常（通常每个文件约几十MB）
3. 运行 `python script/deepdoc.py` 重新下载模型文件

如有进一步问题，请联系项目维护者。 