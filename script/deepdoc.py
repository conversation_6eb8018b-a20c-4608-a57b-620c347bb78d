#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import logging
from huggingface_hub import snapshot_download
import shutil

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def ensure_dir(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        logging.info(f"创建目录: {directory}")

def download_models():
    """下载所有必需的模型文件"""
    # 设置目标目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    target_dir = os.path.join(current_dir, "rag", "res", "deepdoc")
    ensure_dir(target_dir)
    
    # 临时下载目录
    temp_dir = os.path.join(current_dir, "temp_downloads")
    ensure_dir(temp_dir)
    
    try:
        # 1. 下载 deepdoc 模型文件
        logging.info("开始下载 deepdoc 模型文件...")
        deepdoc_dir = snapshot_download(
            repo_id="InfiniFlow/deepdoc",
            local_dir=os.path.join(temp_dir, "deepdoc"),
            local_dir_use_symlinks=False
        )
        
        # 2. 下载文本连接模型
        logging.info("开始下载文本连接模型...")
        text_concat_dir = snapshot_download(
            repo_id="InfiniFlow/text_concat_xgb_v1.0",
            local_dir=os.path.join(temp_dir, "text_concat"),
            local_dir_use_symlinks=False
        )
        
        # 3. 移动所有文件到目标目录
        required_files = [
            # deepdoc 模型文件
            {"src": os.path.join(deepdoc_dir, "det.onnx"), "required": True},
            {"src": os.path.join(deepdoc_dir, "rec.onnx"), "required": True},
            {"src": os.path.join(deepdoc_dir, "ocr.res"), "required": True},
            {"src": os.path.join(deepdoc_dir, "layout.onnx"), "required": True},
            {"src": os.path.join(deepdoc_dir, "layout.laws.onnx"), "required": False},
            {"src": os.path.join(deepdoc_dir, "layout.manual.onnx"), "required": False},
            {"src": os.path.join(deepdoc_dir, "layout.paper.onnx"), "required": False},
            {"src": os.path.join(deepdoc_dir, "tsr.onnx"), "required": True},
            # 文本连接模型
            {"src": os.path.join(text_concat_dir, "updown_concat_xgb.model"), "required": True},
        ]
        
        for file_info in required_files:
            src = file_info["src"]
            filename = os.path.basename(src)
            dst = os.path.join(target_dir, filename)
            
            if os.path.exists(src):
                shutil.copy2(src, dst)
                logging.info(f"成功复制文件: {filename}")
            elif file_info["required"]:
                raise FileNotFoundError(f"找不到必需的文件: {filename}")
            else:
                logging.warning(f"可选文件不存在: {filename}")
        
        logging.info("所有文件下载和复制完成！")
        
    except Exception as e:
        logging.error(f"下载过程中出错: {str(e)}")
        raise
    finally:
        # 清理临时目录
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            logging.info("清理临时下载目录")

def main():
    """主函数"""
    try:
        # 检查是否设置了镜像
        if not os.environ.get('HF_ENDPOINT'):
            logging.warning("未设置 HF_ENDPOINT 环境变量，建议设置: export HF_ENDPOINT=https://hf-mirror.com")
        
        download_models()
        
    except Exception as e:
        logging.error(f"程序执行失败: {str(e)}")
        exit(1)

if __name__ == "__main__":
    main() 