```mermaid
graph LR
    %% 定义节点样式
    classDef main fill:#f96,stroke:#333,stroke-width:2px
    classDef component fill:#9cf,stroke:#333,stroke-width:1px
    classDef tool fill:#9f9,stroke:#333,stroke-width:1px
    classDef data fill:#ff9,stroke:#333,stroke-width:1px
    classDef external fill:#ccc,stroke:#333,stroke-width:1px

    %% 主组件
    TE[TaskExecutor] --> CM[任务收集模块]
    TE --> MM[内存监控]
    TE --> EPH[优雅退出处理]
    TE --> HBR[心跳报告]

    %% 任务收集与处理
    CM --> TPM[任务处理模块]
    TPM --> SMH{处理类型选择}

    %% 处理类型
    SMH --> STD[标准文档处理]
    SMH --> RAP[Raptor处理]
    SMH --> GRA[GraphRAG处理]
    SMH --> GRS[图关系解析]
    SMH --> GCO[图社区处理]

    %% 标准处理详情
    STD --> CHK[分块器]
    CHK --> EMB[嵌入器]
    CHK --> TAG[标签生成]
    CHK --> KEY[关键词提取]
    CHK --> QST[问题生成]
    EMB --> IDX[索引存储]

    %% 外部组件交互
    TE -.-> RDS[(Redis队列)]
    TE -.-> STG[(对象存储)]
    TE -.-> DBS[(文档存储)]
    TE -.-> LLM[LLM服务]

    %% 数据流转
    RDS --> CM
    TPM --> STG
    TPM --> DBS
    TPM --> LLM

    %% 应用样式
    class TE main
    class CM,TPM,SMH,HBR,EPH,MM component
    class STD,RAP,GRA,GRS,GCO component
    class CHK,EMB,TAG,KEY,QST,IDX tool
    class RDS,STG,DBS,LLM external
``` 