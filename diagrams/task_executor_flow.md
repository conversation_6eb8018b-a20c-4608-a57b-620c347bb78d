```mermaid
graph TD
    %% 定义节点样式
    classDef system fill:#f9f,stroke:#333,stroke-width:1px
    classDef process fill:#bbf,stroke:#333,stroke-width:1px
    classDef storage fill:#ffa,stroke:#333,stroke-width:1px
    classDef decision fill:#fbb,stroke:#333,stroke-width:1px
    
    %% 主流程
    A[启动 TaskExecutor] --> B[初始化配置]
    B --> C[创建PID文件]
    C --> D[注册信号处理器]
    D --> E[启动状态报告线程]
    E --> F[开始主循环处理任务]
    
    %% 任务处理流程
    F --> G{从Redis队列获取任务}
    G -->|无任务| H[等待1秒]
    H --> G
    G -->|有任务| I[检查任务状态]
    I -->|已取消| J[标记任务失败]
    I -->|有效任务| K{确定任务类型}
    
    %% 不同类型任务处理
    K -->|标准处理| L1[文档分块处理]
    K -->|Raptor| L2[运行Raptor]
    K -->|GraphRAG| L3[运行GraphRAG]
    
    %% 标准处理流程详细步骤
    L1 --> M1[build_chunks]
    M1 --> M2[生成关键词和问题]
    M2 --> M3[标记内容]
    M3 --> N[向量嵌入]
    L2 --> N
    
    %% 向量存储和索引
    N --> O[存储到文档库]
    O --> P[更新任务状态]
    L3 --> P
    
    %% 任务完成流程
    P --> Q[确认任务完成]
    J --> Q
    Q --> F
    
    %% 状态报告循环
    RS1[状态报告循环] --> RS2[收集统计信息]
    RS2 --> RS3[发送心跳到Redis]
    RS3 --> RS4[清理过期记录]
    RS4 --> RS1
    
    %% 退出流程
    EX1[接收退出信号] --> EX2[设置退出标志]
    EX2 --> EX3[停止接收新任务]
    EX3 --> EX4[等待当前任务完成]
    EX4 --> EX5[执行清理工作]
    EX5 --> EX6[从Redis移除消费者]
    EX6 --> EX7[删除PID文件]
    
    %% 应用样式
    class A,B,C,D,E system
    class F,G,H,I,K,L1,L2,L3,M1,M2,M3,N,O,P,Q process
    class J,EX1,EX2,EX3,EX4,EX5,EX6,EX7 decision
    class RS1,RS2,RS3,RS4 process
``` 