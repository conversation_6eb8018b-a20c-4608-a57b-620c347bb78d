```mermaid
sequenceDiagram
    %% 参与者定义
    participant Client as 客户端
    participant TaskSvr as 任务服务
    participant TaskExe as 任务执行器
    participant Redis as Redis队列
    participant Storage as 对象存储
    participant LLM as LLM服务
    participant DocStore as 文档存储
    
    %% 任务创建和入队
    Note over Client,TaskSvr: 任务创建阶段
    Client->>TaskSvr: 创建处理任务
    TaskSvr->>Redis: 发送任务到队列
    
    %% 任务获取
    Note over TaskExe,Redis: 任务收集阶段
    TaskExe->>Redis: 轮询任务消息
    Redis-->>TaskExe: 返回任务消息
    TaskExe->>TaskSvr: 获取任务详情
    TaskSvr-->>TaskExe: 返回任务完整信息
    
    %% 任务执行
    Note over TaskExe,Storage: 文档处理阶段
    TaskExe->>Storage: 获取文档二进制数据
    Storage-->>TaskExe: 返回文档数据
    
    alt 标准处理流程
        TaskExe->>TaskExe: 执行文档分块
        TaskExe->>LLM: 请求生成关键词/问题/标签
        LLM-->>TaskExe: 返回生成结果
        TaskExe->>TaskExe: 为每个块生成向量
    else Raptor处理流程
        TaskExe->>TaskExe: 提取文档内容
        TaskExe->>LLM: 发送内容进行抽象处理
        LLM-->>TaskExe: 返回抽象结果
    else GraphRAG处理流程
        TaskExe->>TaskExe: 提取文档内容
        TaskExe->>LLM: 构建知识图谱
        LLM-->>TaskExe: 返回知识图谱
    end
    
    %% 存储结果
    Note over TaskExe,DocStore: 存储索引阶段
    TaskExe->>Storage: 保存图片内容(如有)
    TaskExe->>DocStore: 保存处理结果和向量
    DocStore-->>TaskExe: 确认存储完成
    
    %% 更新进度
    Note over TaskExe,TaskSvr: 完成阶段
    TaskExe->>TaskSvr: 更新任务进度/状态
    TaskExe->>Redis: 确认任务完成(ACK)
    
    %% 状态报告
    Note over TaskExe,Redis: 心跳报告
    loop 每30秒
        TaskExe->>Redis: 发送心跳和状态统计
    end
``` 