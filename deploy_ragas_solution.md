# Ragas依赖部署解决方案

## 🎯 问题描述
内网环境下载Ragas相关依赖（langchain、datasets等）非常慢，导致部署失败。

## 🚀 解决方案
通过本地下载依赖包，打包上传到S3，部署时从S3下载安装。

## 📋 操作步骤

### 第一步：本地准备依赖包（在网络良好的环境）

1. **下载依赖包**：
   ```bash
   chmod +x download_ragas_deps.sh
   ./download_ragas_deps.sh
   ```

2. **打包依赖**：
   ```bash
   ./package_ragas_deps.sh
   ```

3. **上传到S3**：
   ```bash
   # 将生成的 ragas_deps_YYYYMMDD_HHMMSS.tar.gz 上传到S3
   # 重命名为 ragas_deps_latest.tar.gz
   ```

### 第二步：修改部署配置

1. **build.sh已修改**：
   - 优先从S3下载Ragas依赖包
   - 失败时回退到内网pip安装

2. **requirements.txt处理**：
   - 可以选择使用 `requirements_without_ragas.txt` 替换原文件
   - 或者保持原文件不变，让build.sh处理

### 第三步：部署验证

1. **运行构建**：
   ```bash
   ./build.sh
   ```

2. **验证安装**：
   ```bash
   source venv/bin/activate
   python -c "import ragas, datasets; print('Ragas安装成功')"
   ```

## 📁 文件说明

- `download_ragas_deps.sh`: 本地下载Ragas依赖脚本
- `package_ragas_deps.sh`: 打包依赖脚本（由download脚本生成）
- `install_ragas_from_wheels.sh`: 从wheel文件安装脚本（由download脚本生成）
- `requirements_without_ragas.txt`: 不包含Ragas的依赖文件
- `build.sh`: 修改后的构建脚本

## 🔧 S3文件结构

```
https://msstest.sankuai.com/sg-ug-api-test-sdk-agent/
├── ragas_deps_latest.tar.gz  # Ragas依赖包
├── deepdoc_res.tar.gz        # 现有的deepdoc资源
├── nltk_data.tar.gz          # 现有的nltk数据
└── mini_racer-*.whl          # 现有的mini-racer包
```

## ⚡ 优势

1. **快速部署**: 从S3下载比从PyPI快得多
2. **版本固定**: 确保所有环境使用相同版本
3. **离线安装**: 不依赖外网连接
4. **回退机制**: S3失败时自动回退到pip安装
5. **增量更新**: 只需更新Ragas相关包

## 🔍 故障排除

1. **S3下载失败**: 检查网络连接和URL
2. **wheel安装失败**: 检查Python版本和平台兼容性
3. **依赖冲突**: 使用 `--force-reinstall` 强制重装

## 📊 预期效果

- 部署时间从 30+ 分钟减少到 5 分钟以内
- 成功率从 50% 提升到 95%+
- 网络依赖降到最低
