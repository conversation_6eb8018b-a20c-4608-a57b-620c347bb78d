# 系统依赖（排除Ragas相关包）
# 注意：这个文件用于内网pip安装，不包含Ragas相关依赖

# 基础依赖
aiofiles==24.1.0 ; python_version >= "3.10" and python_version < "3.13"
aiohttp==3.11.11 ; python_version >= "3.10" and python_version < "3.13"
aiosignal==1.3.2 ; python_version >= "3.10" and python_version < "3.13"
annotated-types==0.7.0 ; python_version >= "3.10" and python_version < "3.13"
anyio==4.7.0 ; python_version >= "3.10" and python_version < "3.13"
argon2-cffi-bindings==21.2.0 ; python_version >= "3.10" and python_version < "3.13"
argon2-cffi==23.1.0 ; python_version >= "3.10" and python_version < "3.13"
asttokens==3.0.0 ; python_version >= "3.10" and python_version < "3.13"
attrs==24.3.0 ; python_version >= "3.10" and python_version < "3.13"
beautifulsoup4==4.12.3 ; python_version >= "3.10" and python_version < "3.13"
bidict==0.23.1 ; python_version >= "3.10" and python_version < "3.13"
bleach==6.2.0 ; python_version >= "3.10" and python_version < "3.13"
blinker==1.9.0 ; python_version >= "3.10" and python_version < "3.13"
boto3==1.35.84 ; python_version >= "3.10" and python_version < "3.13"
botocore==1.35.84 ; python_version >= "3.10" and python_version < "3.13"
cachetools==5.5.0 ; python_version >= "3.10" and python_version < "3.13"
certifi==2024.12.14 ; python_version >= "3.10" and python_version < "3.13"
cffi==1.17.1 ; python_version >= "3.10" and python_version < "3.13"
chardet==5.2.0 ; python_version >= "3.10" and python_version < "3.13"
charset-normalizer==3.4.1 ; python_version >= "3.10" and python_version < "3.13"
click==8.1.8 ; python_version >= "3.10" and python_version < "3.13"
colorama==0.4.6 ; python_version >= "3.10" and python_version < "3.13"
coloredlogs==15.0.1 ; python_version >= "3.10" and python_version < "3.13" and (sys_platform == "darwin" or platform_machine != "x86_64")
contourpy==1.3.1 ; python_version >= "3.10" and python_version < "3.13"
cramjam==2.9.1 ; python_version >= "3.10" and python_version < "3.13"
crawl4ai==0.3.8 ; python_version >= "3.10" and python_version < "3.13"
cryptography==44.0.2 ; python_version >= "3.10" and python_version < "3.13"
cssselect==1.3.0 ; python_version >= "3.10" and python_version < "3.13"
cycler==0.12.1 ; python_version >= "3.10" and python_version < "3.13"
dashscope==1.20.11 ; python_version >= "3.10" and python_version < "3.13"
datrie==0.8.2 ; python_version >= "3.10" and python_version < "3.13"
decorator==5.2.1 ; python_version >= "3.10" and python_version < "3.13"
deepl==1.18.0 ; python_version >= "3.10" and python_version < "3.13"
demjson3==3.0.6 ; python_version >= "3.10" and python_version < "3.13"
deprecated==1.2.18 ; python_version >= "3.10" and python_version < "3.13"

# 排除Ragas相关依赖
# dill>=0.3.0,<0.3.8 ; python_version >= "3.10" and python_version < "3.13"
# multiprocess==0.70.15 ; python_version >= "3.10" and python_version < "3.13"
# fsspec>=2023.5.0,<2024.6.0 ; python_version >= "3.10" and python_version < "3.13"
# huggingface-hub>=0.20.0,<0.24.0 ; python_version >= "3.10" and python_version < "3.13"
# fastparquet>=2024.2.0,<2024.6.0 ; python_version >= "3.10" and python_version < "3.13"

discord-py==2.3.2 ; python_version >= "3.10" and python_version < "3.13"
diskcache==5.6.3 ; python_version >= "3.10" and python_version < "3.13"
distro==1.9.0 ; python_version >= "3.10" and python_version < "3.13"
docstring-parser==0.16 ; python_version >= "3.10" and python_version < "3.13"
docutils==0.21.2 ; python_version >= "3.10" and python_version < "3.13"
duckduckgo-search==7.5.2 ; python_version >= "3.10" and python_version < "3.13"
editdistance==0.8.1 ; python_version >= "3.10" and python_version < "3.13"
elastic-transport==8.12.0 ; python_version >= "3.10" and python_version < "3.13"
elasticsearch-dsl==8.12.0 ; python_version >= "3.10" and python_version < "3.13"
elasticsearch==8.12.1 ; python_version >= "3.10" and python_version < "3.13"
et-xmlfile==2.0.0 ; python_version >= "3.10" and python_version < "3.13"
exceptiongroup==1.2.2 ; python_version == "3.10"
fake-useragent==1.5.1 ; python_version >= "3.10" and python_version < "3.13"
fastavro==1.10.0 ; python_version >= "3.10" and python_version < "3.13"
feedparser==6.0.11 ; python_version >= "3.10" and python_version < "3.13"
filelock==3.15.4 ; python_version >= "3.10" and python_version < "3.13"
flasgger==0.9.7.1 ; python_version >= "3.10" and python_version < "3.13"
flask-cors==5.0.0 ; python_version >= "3.10" and python_version < "3.13"
flask-login==0.6.3 ; python_version >= "3.10" and python_version < "3.13"
flask-session==0.8.0 ; python_version >= "3.10" and python_version < "3.13"
flask==3.0.3 ; python_version >= "3.10" and python_version < "3.13"
flatbuffers==25.2.10 ; python_version >= "3.10" and python_version < "3.13" and (sys_platform == "darwin" or platform_machine != "x86_64")
fonttools==4.56.0 ; python_version >= "3.10" and python_version < "3.13"
free-proxy==1.1.3 ; python_version >= "3.10" and python_version < "3.13"
frozenlist==1.5.0 ; python_version >= "3.10" and python_version < "3.13"
future==1.0.0 ; python_version >= "3.10" and python_version < "3.13"
gensim==4.3.3 ; python_version >= "3.10" and python_version < "3.13"

# 继续其他依赖...
flask==3.0.3 ; python_version >= "3.10" and python_version < "3.13"
peewee==3.17.1 ; python_version >= "3.10" and python_version < "3.13"
pymysql==1.1.1 ; python_version >= "3.10" and python_version < "3.13"
redis==5.2.1 ; python_version >= "3.10" and python_version < "3.13"
requests==2.32.2 ; python_version >= "3.10" and python_version < "3.13"
sqlalchemy>=2.0.27,<3.0.0 ; python_version >= "3.10" and python_version < "3.13"

# 注意：Ragas相关依赖将通过S3下载的wheel包安装
# ragas==0.1.9
# datasets==2.19.1
