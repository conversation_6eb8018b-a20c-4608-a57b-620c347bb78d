#!/usr/bin/env bash
set -e

echo "🚀 开始下载Ragas相关依赖包..."

# 创建下载目录
DOWNLOAD_DIR="ragas_wheels"
mkdir -p "$DOWNLOAD_DIR"
cd "$DOWNLOAD_DIR"

# 设置Python版本和平台
PYTHON_VERSION="310"  # Python 3.10
PLATFORM="linux_x86_64"

echo "📦 下载Ragas核心依赖..."

# Ragas核心依赖列表
RAGAS_DEPS=(
    "ragas==0.1.9"
    "datasets==2.19.1"
    "langchain-core"
    "langchain"
    "langsmith"
    "huggingface-hub>=0.20.0,<0.24.0"
    "fsspec>=2023.5.0,<2024.6.0"
    "dill>=0.3.0,<0.3.8"
    "multiprocess==0.70.15"
    "transformers"
    "torch"
    "numpy"
    "pandas"
    "pydantic"
    "pydantic-core"
    "typing-extensions"
    "tqdm"
    "requests"
    "aiohttp"
    "jsonschema"
    "pyarrow"
    "xxhash"
)

# 下载所有依赖包（包括子依赖）
for package in "${RAGAS_DEPS[@]}"; do
    echo "📥 下载 $package 及其依赖..."
    pip download \
        --dest . \
        --platform "$PLATFORM" \
        --python-version "$PYTHON_VERSION" \
        --only-binary=:all: \
        --no-deps \
        "$package" || echo "⚠️ $package 下载失败，可能需要手动处理"
done

# 下载完整依赖树（包括子依赖）
echo "📥 下载完整依赖树..."
pip download \
    --dest . \
    --platform "$PLATFORM" \
    --python-version "$PYTHON_VERSION" \
    --only-binary=:all: \
    ragas==0.1.9 datasets==2.19.1

cd ..

# 创建打包脚本
echo "📦 创建打包脚本..."
cat > package_ragas_deps.sh << 'EOF'
#!/usr/bin/env bash
set -e

PACKAGE_NAME="ragas_deps_$(date +%Y%m%d_%H%M%S).tar.gz"
echo "📦 打包Ragas依赖到: $PACKAGE_NAME"

# 打包wheel文件
tar -czf "$PACKAGE_NAME" ragas_wheels/

# 显示包大小
echo "📊 包大小: $(du -h "$PACKAGE_NAME" | cut -f1)"
echo "📁 包含文件数: $(tar -tzf "$PACKAGE_NAME" | wc -l)"

echo "✅ 打包完成: $PACKAGE_NAME"
echo "🚀 请将此文件上传到S3: https://msstest.sankuai.com/sg-ug-api-test-sdk-agent/$PACKAGE_NAME"
EOF

chmod +x package_ragas_deps.sh

# 创建安装脚本
echo "📝 创建安装脚本..."
cat > install_ragas_from_wheels.sh << 'EOF'
#!/usr/bin/env bash
set -e

WHEELS_DIR="ragas_wheels"

echo "🔍 检查wheel文件目录..."
if [ ! -d "$WHEELS_DIR" ]; then
    echo "❌ 错误: $WHEELS_DIR 目录不存在"
    exit 1
fi

echo "📊 找到 $(ls "$WHEELS_DIR"/*.whl 2>/dev/null | wc -l) 个wheel文件"

# 激活虚拟环境（如果存在）
if [ -f "venv/bin/activate" ]; then
    echo "🔧 激活虚拟环境..."
    source venv/bin/activate
fi

echo "📦 从本地wheel文件安装Ragas依赖..."

# 安装所有wheel文件
pip install \
    --find-links "$WHEELS_DIR" \
    --no-index \
    --no-deps \
    "$WHEELS_DIR"/*.whl

echo "🔧 安装Ragas核心包..."
pip install \
    --find-links "$WHEELS_DIR" \
    --no-index \
    ragas==0.1.9 datasets==2.19.1

echo "✅ Ragas依赖安装完成！"

# 验证安装
echo "🔍 验证安装..."
python -c "
try:
    import ragas
    import datasets
    print('✅ Ragas和datasets导入成功！')
    print(f'Ragas版本: {ragas.__version__}')
    print(f'Datasets版本: {datasets.__version__}')
except ImportError as e:
    print(f'❌ 导入失败: {e}')
    exit(1)
"
EOF

chmod +x install_ragas_from_wheels.sh

echo "✅ 下载脚本创建完成！"
echo ""
echo "📋 下一步操作："
echo "1. 运行 ./package_ragas_deps.sh 打包依赖"
echo "2. 将生成的tar.gz文件上传到S3"
echo "3. 在部署脚本中使用 install_ragas_from_wheels.sh"
