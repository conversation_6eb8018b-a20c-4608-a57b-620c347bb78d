# MySQL迁移资源汇总

本文档汇总了MySQL从Docker部署迁移到公司内部组件的所有相关资源。

## 文件说明

| 文件名 | 用途 |
|--------|------|
| `create_tables.sql` | 数据库表结构创建脚本，基于Peewee ORM模型生成 |
| `mysql_migration_guide.md` | 详细的迁移指南，包含所有步骤和注意事项 |
| `test_db_connection.py` | 测试与公司内部MySQL的连接和权限的脚本 |
| `flask_sqlalchemy_example.py` | Flask-SQLAlchemy连接公司内部MySQL的示例代码 |
| `flask_sqlalchemy_zebra_example.py` | Flask-SQLAlchemy结合Zebra代理连接公司内部MySQL的示例代码 |
| `dbutils_example.py` | 使用DBUtils库连接公司内部MySQL的示例代码 |

## 使用说明

### 1. 准备工作

1. 阅读 `mysql_migration_guide.md` 文档，了解迁移流程和注意事项
2. 从DBA获取以下信息：
   - 数据库连接地址和端口（或Zebra引用键）
   - 访问凭证（用户名和密码）
   - 分配的数据库名称
   - 网络访问限制信息
   - 若使用Zebra代理，需获取app_key和ref_key

### 2. 创建数据库表结构

1. 检查 `create_tables.sql` 文件，确认表结构符合需求
2. 与公司DBA合作，在内部MySQL上执行DDL脚本创建表结构
3. 确认所有表格已正确创建

### 3. 测试数据库连接

根据连接方式选择合适的测试脚本：

#### 直接连接方式
```bash
python test_db_connection.py --host <公司MySQL地址> --port <端口> --user <用户名> --password <密码> --database <数据库名>
```

#### 使用Zebra代理方式
```bash
python flask_sqlalchemy_zebra_example.py --app-key <Zebra应用Key> --ref-key <数据库引用Key>
```

### 4. 实施迁移

1. 在Docker MySQL中备份当前数据
```bash
docker exec ragflow-mysql mysqldump -u root -p'infini_rag_flow' rag_flow > rag_flow_backup.sql
```

2. 去除备份SQL中的DDL语句
```bash
grep -i "^INSERT" rag_flow_backup.sql > rag_flow_data_only.sql
```

3. 向内部MySQL导入数据（如无法直接连接，请联系DBA协助）

4. 修改应用配置文件（`conf/service_conf.yaml`）更新数据库连接参数

5. 注释或删除Docker配置文件中的MySQL服务相关配置

### 5. 适配应用代码

根据项目需要，选择以下一种方式修改代码：

#### 方式一：使用Flask-SQLAlchemy结合Zebra代理（推荐）

1. 安装必要的依赖：
```bash
pip install pycat zebraproxyclient pymysql flask-sqlalchemy
```

2. 参考 `flask_sqlalchemy_zebra_example.py` 修改数据库连接代码：
   - 关键点：先执行hack_flask_sqlalchemy函数替换引擎，再初始化应用
   - 确保使用正确的Zebra配置：app_key和ref_key

3. 移除代码中自动创建表的部分，如`DB.create_tables()`调用

#### 方式二：使用Flask-SQLAlchemy直接连接

参考 `flask_sqlalchemy_example.py` 修改数据库连接代码

#### 方式三：使用DBUtils连接池

参考 `dbutils_example.py` 修改数据库连接代码

## 重要注意事项

1. **DDL限制**：公司内部MySQL组件不支持应用程序执行DDL操作（CREATE TABLE、ALTER TABLE等），所有表结构变更必须通过DBA执行

2. **数据备份**：迁移前务必备份所有数据，以防意外情况发生

3. **网络连接**：确保应用服务器能够访问内部MySQL（网络策略、防火墙等）

4. **Zebra代理**：如果使用Zebra代理服务：
   - 确保app_key和ref_key配置正确
   - 安装必要的依赖：pycat和zebraproxyclient
   - 遵循hack_flask_sqlalchemy的正确执行顺序
   - 获取相关监控和告警通知配置

## 联系支持

如遇问题请联系：
- 项目技术负责人：[填写姓名和联系方式]
- 公司DBA：[填写姓名和联系方式]
- MySQL组件团队：[填写姓名和联系方式]
- Zebra服务团队：[填写姓名和联系方式] 