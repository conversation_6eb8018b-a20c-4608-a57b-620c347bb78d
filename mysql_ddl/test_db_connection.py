#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试与公司内部MySQL数据库连接的脚本
用于验证从Docker MySQL迁移到公司内部MySQL组件后的连接是否正常
"""

import mysql.connector
import sys
import argparse

def test_connection(host, port, user, password, database):
    """测试数据库连接并验证基本表是否存在"""
    config = {
        'host': host,
        'port': port,
        'user': user,
        'password': password,
        'database': database
    }
    
    try:
        print(f"尝试连接到 {host}:{port}/{database}...")
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        # 测试基本表是否存在且可访问
        tables_to_check = ['user', 'tenant', 'document', 'knowledgebase']
        
        print("检查表是否存在:")
        for table in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                result = cursor.fetchone()
                print(f"  - {table}: √ (包含 {result[0]} 条记录)")
            except mysql.connector.Error as err:
                print(f"  - {table}: ✗ (错误: {err})")
        
        # 测试插入权限 (如果需要的话)
        print("\n检查数据操作权限:")
        try:
            test_uuid = "test_" + "0" * 27  # 32字符长度的测试ID
            
            # 尝试插入测试数据
            cursor.execute(
                "INSERT INTO user (id, nickname, email, create_time) VALUES (%s, %s, %s, %s)",
                (test_uuid, "测试用户", "<EMAIL>", 1678900000000)
            )
            print("  - INSERT: √")
            
            # 尝试查询刚插入的数据
            cursor.execute(f"SELECT * FROM user WHERE id = '{test_uuid}'")
            print("  - SELECT: √")
            
            # 尝试更新数据
            cursor.execute(
                "UPDATE user SET nickname = %s WHERE id = %s",
                ("更新后的测试用户", test_uuid)
            )
            print("  - UPDATE: √")
            
            # 尝试删除测试数据
            cursor.execute(f"DELETE FROM user WHERE id = '{test_uuid}'")
            print("  - DELETE: √")
            
            # 回滚事务，确保不会实际修改数据
            conn.rollback()
            print("  - 所有操作已回滚，数据库未被修改")
        except mysql.connector.Error as err:
            print(f"  - 权限测试失败: {err}")
            conn.rollback()
        
        print("\n连接测试: 成功")
        return True
    except mysql.connector.Error as err:
        print(f"连接失败: {err}")
        return False
    finally:
        if 'conn' in locals() and conn.is_connected():
            cursor.close()
            conn.close()
            print("数据库连接已关闭")


def main():
    parser = argparse.ArgumentParser(description='测试MySQL数据库连接')
    parser.add_argument('--host', required=True, help='数据库主机地址')
    parser.add_argument('--port', type=int, default=3306, help='数据库端口号')
    parser.add_argument('--user', required=True, help='数据库用户名')
    parser.add_argument('--password', required=True, help='数据库密码')
    parser.add_argument('--database', required=True, help='数据库名称')
    
    args = parser.parse_args()
    
    success = test_connection(
        args.host, 
        args.port, 
        args.user, 
        args.password, 
        args.database
    )
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main() 