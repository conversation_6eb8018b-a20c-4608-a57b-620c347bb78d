# MySQL迁移指南：从Docker到公司MySQL基建

本文档提供了将当前项目中Docker启动的MySQL迁移至公司MySQL基建的详细指南。

## 先决条件

1. 确保已经获得了以下信息：
   - 公司MySQL的Zebra应用Key
   - 公司MySQL的数据库引用Key
   - 相关表结构已在公司MySQL中创建

2. 确保已安装以下Python依赖：
   ```
   pymysql>=1.0.2
   zebraproxyclient>=0.0.9
   sqlalchemy>=1.4.0
   tqdm
   pycat  # 用于Cat监控服务（可选）
   ```

## 关于Zebra适配器

本项目使用了自定义的Zebra适配器，将Zebra代理与PeeWee ORM集成。适配器的核心原理是：

1. 使用`zebraproxyclient.api.sqlalchemy.create_engine`创建SQLAlchemy引擎
2. 通过引擎的`raw_connection()`方法获取底层的数据库连接
3. 将此连接提供给PeeWee的PooledMySQLDatabase

**注意**：适配器依赖于SQLAlchemy引擎，因此必须同时安装SQLAlchemy。

## 第一阶段：准备工作

### 1. 安装所需依赖

```bash
# 安装必要的Python依赖
pip install pymysql>=1.0.2 zebraproxyclient>=0.0.9 sqlalchemy>=1.4.0 tqdm pycat
```

### 2. 确认配置文件正确

检查 `conf/zebra/config.py` 文件中的配置是否正确：
- `ZEBRA_APP_KEY`: 公司MySQL的应用Key
- `ZEBRA_REF_KEYS`: 数据库引用Key映射

如有需要，调整连接池设置：
- `POOL_SIZE`: 最大连接数
- `POOL_TIMEOUT`: 连接超时时间
- `POOL_RECYCLE`: 连接回收时间

## 第二阶段：测试连接

### 1. 验证公司MySQL连接

运行测试脚本，确认能通过Zebra代理成功连接到公司MySQL：

```bash
python mysql_ddl/test_zebra_connection.py
```

如果连接成功，脚本会显示公司MySQL中的表信息。如果连接失败，请检查配置和网络设置。

### 2. 查看表结构是否匹配

确认公司MySQL中的表结构与当前Docker MySQL中的表结构匹配：

```bash
# 如果需要查看公司MySQL中的表结构
python mysql_ddl/test_zebra_connection.py --show-schema
```

## 第三阶段：数据迁移

### 1. 准备迁移环境

在进行数据迁移前，建议进行以下准备：
- 停止所有可能会写入数据库的服务
- 备份当前Docker MySQL数据（以防万一）

### 2. 执行数据迁移

使用数据迁移脚本将数据从Docker MySQL迁移到公司MySQL：

```bash
python mysql_ddl/migrate_to_zebra.py \
  --src-host localhost \
  --src-port 5455 \
  --src-user root \
  --src-password infini_rag_flow \
  --src-database rag_flow
```

迁移脚本支持以下选项：
- `--tables`: 指定要迁移的表（默认迁移所有表）
- `--skip-tables`: 指定要跳过的表
- `--batch-size`: 设置分批处理的大小（默认1000条记录）
- `--ref-key`: 指定Zebra数据库引用Key（默认使用配置中的default）

例如，只迁移用户和文档表：
```bash
python mysql_ddl/migrate_to_zebra.py --tables user document
```

### 3. 验证数据迁移

迁移完成后，验证数据是否迁移成功：

```bash
python mysql_ddl/test_zebra_connection.py
```

检查关键表的记录数是否与源数据库一致。

## 第四阶段：切换应用连接

### 1. 切换数据库连接

项目已经集成了条件切换代码，可以通过设置环境变量 `USE_ZEBRA=1` 来启用Zebra连接：

```bash
# Linux/macOS
export USE_ZEBRA=1

# Windows
set USE_ZEBRA=1
```

### 2. 重启应用服务

使用新的环境变量重启应用服务：

```bash
# 如果使用docker-compose
docker-compose down
USE_ZEBRA=1 docker-compose up -d

# 如果是直接启动Python应用
USE_ZEBRA=1 python path/to/your/app.py
```

### 3. 监控应用运行

切换后密切监控应用运行状态：
- 检查日志中是否有数据库连接错误
- 监控应用的关键功能是否正常
- 监控数据库连接池的状态

## 第五阶段：验证与回滚

### 1. 验证应用正常运行

通过以下方式验证应用是否正常：
- 执行自动化测试（如果有）
- 手动测试关键功能
- 监控系统性能和错误日志

### 2. 回滚计划（如果需要）

如果遇到严重问题，可以通过以下步骤回滚：

1. 停止使用公司MySQL的服务：
   ```bash
   # 取消环境变量设置
   unset USE_ZEBRA  # Linux/macOS
   set USE_ZEBRA=   # Windows
   ```

2. 重启应用服务，回退到使用Docker MySQL

## 最终阶段：清理工作

在确认迁移成功并稳定运行一段时间后：

1. 修改默认配置，永久启用Zebra连接
2. 考虑是否需要保留Docker MySQL服务或者可以停用
3. 更新项目文档，记录数据库连接的变更

## 联系支持

如果在迁移过程中遇到问题，请联系：
- DBA团队：[联系人和联系方式]
- Zebra服务团队：[联系人和联系方式]
- 项目技术负责人：[联系人和联系方式] 