# RAG评估系统数据库表结构说明

## 概述

本文档说明RAG评估系统相关数据库表的结构和部署方法。

## 表结构说明

### 1. evaluation_dataset (评估数据集表)
存储评估数据集的基本信息。

**主要字段：**
- `id`: 数据集唯一标识
- `name`: 数据集名称
- `description`: 数据集描述
- `kb_id`: 关联的知识库ID
- `tenant_id`: 租户ID
- `created_by`: 创建者ID
- `dataset_type`: 数据集类型（qa_pairs, questions_only）
- `status`: 状态（active, archived）
- `total_samples`: 样本总数

### 2. evaluation_sample (评估样本表)
存储具体的评估样本数据。

**主要字段：**
- `id`: 样本唯一标识
- `dataset_id`: 所属数据集ID
- `question`: 问题内容
- `ground_truth`: 标准答案
- `contexts`: 相关文档片段（JSON格式）
- `metadata`: 额外信息（JSON格式）

### 3. evaluation_task (评估任务表)
存储评估任务的配置和状态信息。

**主要字段：**
- `id`: 任务唯一标识
- `name`: 任务名称
- `dataset_id`: 使用的数据集ID
- `kb_id`: 评估的知识库ID
- `tenant_id`: 租户ID
- `created_by`: 创建者ID
- `metrics`: 评估指标配置（JSON格式）
- `retrieval_config`: 检索配置（JSON格式）
- `llm_config`: LLM配置（JSON格式）
- `status`: 任务状态（pending, running, completed, failed）
- `progress`: 进度百分比
- `total_samples`: 总样本数
- `processed_samples`: 已处理样本数
- `started_at`: 开始时间
- `completed_at`: 完成时间

### 4. evaluation_result (评估结果表)
存储每个样本的详细评估结果。

**主要字段：**
- `id`: 结果唯一标识
- `task_id`: 所属任务ID
- `sample_id`: 样本ID
- `retrieved_contexts`: 检索到的文档（JSON格式）
- `generated_answer`: 生成的答案
- `faithfulness`: 忠实度指标
- `answer_relevancy`: 答案相关性指标
- `context_precision`: 上下文精确度指标
- `context_recall`: 上下文召回率指标
- `context_relevancy`: 上下文相关性指标
- `answer_correctness`: 答案正确性指标
- `answer_similarity`: 答案相似性指标
- `execution_time`: 执行时间（秒）
- `error_message`: 错误信息

### 5. evaluation_report (评估报告表)
存储任务的汇总评估报告。

**主要字段：**
- `id`: 报告唯一标识
- `task_id`: 所属任务ID
- `avg_*`: 各项指标的平均值
- `total_samples`: 总样本数
- `successful_samples`: 成功样本数
- `failed_samples`: 失败样本数
- `avg_execution_time`: 平均执行时间
- `detailed_report`: 详细报告（JSON格式）

## 部署方法

### 方法1：使用完整DDL文件
```bash
# 执行完整的数据库创建脚本（包含所有表）
mysql -u username -p rag_flow < create_tables.sql
```

### 方法2：仅创建评估相关表
```bash
# 仅创建评估系统相关表
mysql -u username -p rag_flow < evaluation_tables.sql
```

### 方法3：手动执行
```sql
-- 连接到MySQL
mysql -u username -p

-- 选择数据库
USE rag_flow;

-- 执行评估表创建脚本
SOURCE evaluation_tables.sql;
```

## 索引说明

### 索引设计原则

基于实际使用场景，我们采用了精简的索引设计，只保留真正会用到的核心索引：

### 各表索引详情

#### 1. evaluation_dataset (评估数据集表)
- **主键索引**: `id`
- **复合索引**: `idx_evaluation_dataset_tenant_kb` (`tenant_id`, `kb_id`) - 用于按租户和知识库查询数据集
- **单列索引**: `idx_evaluation_dataset_created_by` (`created_by`) - 用于查询用户创建的数据集
- **单列索引**: `idx_evaluation_dataset_status` (`status`) - 用于过滤活跃/归档状态

#### 2. evaluation_sample (评估样本表)
- **主键索引**: `id`
- **外键索引**: `idx_evaluation_sample_dataset_id` (`dataset_id`) - 用于查询数据集下的样本

#### 3. evaluation_task (评估任务表)
- **主键索引**: `id`
- **复合索引**: `idx_evaluation_task_tenant_kb` (`tenant_id`, `kb_id`) - 用于按租户和知识库查询任务
- **外键索引**: `idx_evaluation_task_dataset_id` (`dataset_id`) - 用于查询数据集相关任务
- **单列索引**: `idx_evaluation_task_status` (`status`) - 用于监控任务状态
- **单列索引**: `idx_evaluation_task_created_by` (`created_by`) - 用于查询用户创建的任务

#### 4. evaluation_result (评估结果表)
- **主键索引**: `id`
- **外键索引**: `idx_evaluation_result_task_id` (`task_id`) - 用于查询任务的所有结果

#### 5. evaluation_report (评估报告表)
- **主键索引**: `id`
- **唯一索引**: `idx_evaluation_report_task_id` (`task_id`) - 确保每个任务只有一个报告

### 索引优化说明

**移除的冗余索引：**
1. **时间字段索引** - `create_time`, `update_time`, `create_date`, `update_date` 等时间字段的索引被移除，因为：
   - 这些字段很少作为主要查询条件
   - 如果需要按时间范围查询，可以在应用层处理或临时创建索引

2. **统计字段索引** - `total_samples`, `processed_samples`, `progress` 等统计字段的索引被移除，因为：
   - 这些字段主要用于显示，不用于查询过滤
   - 避免频繁更新导致的索引维护开销

3. **评估指标索引** - `faithfulness`, `answer_relevancy` 等评估指标字段的索引被移除，因为：
   - 这些字段主要用于统计分析，不用于精确查询
   - 如需要范围查询可以在报告层面处理

4. **名称字段索引** - `name` 字段索引被移除，因为：
   - 名称查询通常是模糊查询，普通索引效果有限
   - 如需要可以考虑全文索引

**保留的核心索引：**
1. **外键关联索引** - 保证JOIN查询性能
2. **状态过滤索引** - 支持常见的状态筛选查询
3. **租户隔离索引** - 支持多租户数据隔离
4. **用户权限索引** - 支持按用户查询数据

### 性能优化建议

1. **复合索引使用**：
   - `tenant_id, kb_id` 复合索引可以同时支持 `tenant_id` 单独查询和 `tenant_id + kb_id` 组合查询
   - 查询时注意字段顺序，优先使用索引前缀

2. **分区表考虑**：
   - 对于大量数据的 `evaluation_result` 表，可考虑按 `task_id` 进行分区
   - 有助于提高查询性能和数据管理

3. **索引监控**：
   - 定期监控索引使用情况：`SHOW INDEX FROM table_name`
   - 根据实际查询模式调整索引策略

4. **查询优化**：
   - 尽量使用覆盖索引减少回表查询
   - 避免在索引字段上使用函数或表达式

## 外键约束

DDL文件中包含了外键约束的定义（默认注释掉），如需启用外键约束：

1. 确保相关表已存在
2. 取消相应ALTER TABLE语句的注释
3. 执行外键约束创建语句

**注意：** 启用外键约束会影响数据删除操作，请根据实际需求决定是否启用。

## 数据迁移

如果是在现有系统上添加评估功能：

1. 备份现有数据库
2. 执行evaluation_tables.sql创建新表
3. 验证表结构是否正确
4. 测试评估功能

## 监控建议

1. 监控evaluation_task表的任务状态分布
2. 监控evaluation_result表的数据增长速度
3. 监控各项评估指标的分布情况
4. 设置长时间运行任务的告警

## 故障排除

### 常见问题

1. **表创建失败**
   - 检查数据库权限
   - 确认字符集设置
   - 检查表名冲突

2. **外键约束错误**
   - 确认被引用表存在
   - 检查字段类型匹配
   - 验证数据一致性

3. **索引创建失败**
   - 检查字段长度限制
   - 确认索引名称唯一性

### 日志查看

```sql
-- 查看表创建状态
SHOW TABLES LIKE 'evaluation_%';

-- 查看表结构
DESCRIBE evaluation_dataset;
DESCRIBE evaluation_sample;
DESCRIBE evaluation_task;
DESCRIBE evaluation_result;
DESCRIBE evaluation_report;

-- 查看索引信息
SHOW INDEX FROM evaluation_result;
``` 