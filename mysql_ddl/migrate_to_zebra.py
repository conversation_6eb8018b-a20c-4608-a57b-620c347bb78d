#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据迁移脚本
用于将数据从Docker MySQL迁移到公司内部MySQL
"""

import os
import sys
import argparse
import logging
import time
import pymysql
from tqdm import tqdm
from datetime import datetime

# 设置项目根目录
project_root = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入Zebra适配器
from api.db.zebra_peewee_adapter import ZebraPooledMySQLDatabase
from conf.zebra.config import ZEBRA_APP_KEY, ZEBRA_REF_KEYS

# 定义分批处理的大小
BATCH_SIZE = 1000

def get_docker_mysql_connection(host, port, user, password, database):
    """
    获取Docker MySQL连接
    
    Args:
        host: 数据库主机
        port: 数据库端口
        user: 数据库用户名
        password: 数据库密码
        database: 数据库名称
    
    Returns:
        pymysql.Connection: 数据库连接
    """
    return pymysql.connect(
        host=host,
        port=port,
        user=user,
        password=password,
        database=database,
        charset='utf8mb4',
        cursorclass=pymysql.cursors.DictCursor
    )

def get_zebra_connection(ref_key=None):
    """
    获取Zebra MySQL连接
    
    Args:
        ref_key: 数据库引用Key，如果为None则使用默认引用Key
    
    Returns:
        ZebraPooledMySQLDatabase: Zebra数据库连接
    """
    if ref_key is None:
        ref_key = ZEBRA_REF_KEYS["default"]
    
    # 创建数据库连接
    db = ZebraPooledMySQLDatabase("rag_flow", ref_key=ref_key)
    
    # 返回连接
    return db

def get_tables(connection):
    """
    获取数据库中的所有表
    
    Args:
        connection: 数据库连接
    
    Returns:
        list: 表名列表
    """
    with connection.cursor() as cursor:
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        
        # 根据结果格式获取表名
        if tables:
            if isinstance(tables[0], dict) and "Tables_in_rag_flow" in tables[0]:
                return [table["Tables_in_rag_flow"] for table in tables]
            elif isinstance(tables[0], dict):
                return [list(table.values())[0] for table in tables]
            else:
                # 元组格式
                return [table[0] for table in tables]
        return []

def get_table_count(connection, table):
    """
    获取表中的记录数
    
    Args:
        connection: 数据库连接
        table: 表名
    
    Returns:
        int: 记录数
    """
    with connection.cursor() as cursor:
        cursor.execute(f"SELECT COUNT(*) FROM {table}")
        result = cursor.fetchone()
        
        # 处理不同格式的结果
        if isinstance(result, dict):
            return result.get("COUNT(*)") or list(result.values())[0]
        else:
            # 元组格式
            return result[0]

def get_table_schema(connection, table):
    """
    获取表结构
    
    Args:
        connection: 数据库连接
        table: 表名
    
    Returns:
        dict: 表结构信息
    """
    with connection.cursor() as cursor:
        # 获取字段信息
        cursor.execute(f"DESCRIBE {table}")
        fields_result = cursor.fetchall()
        
        # 处理不同格式的结果
        fields = []
        for field in fields_result:
            if isinstance(field, dict):
                fields.append(field)
            else:
                # 元组格式转换为字典
                fields.append({
                    "Field": field[0],
                    "Type": field[1],
                    "Null": field[2],
                    "Key": field[3] if len(field) > 3 else "",
                    "Default": field[4] if len(field) > 4 else None,
                    "Extra": field[5] if len(field) > 5 else ""
                })
        
        # 获取索引信息
        cursor.execute(f"SHOW INDEX FROM {table}")
        indexes_result = cursor.fetchall()
        
        # 处理不同格式的结果
        indexes = []
        for index in indexes_result:
            if isinstance(index, dict):
                indexes.append(index)
            else:
                # 元组格式转换为字典（简化版）
                indexes.append({
                    "Table": index[0],
                    "Non_unique": index[1],
                    "Key_name": index[2],
                    "Column_name": index[4],
                    "Seq_in_index": index[3]
                })
        
        # 获取建表语句
        cursor.execute(f"SHOW CREATE TABLE {table}")
        create_result = cursor.fetchone()
        
        # 处理不同格式的结果
        if isinstance(create_result, dict):
            create_sql = create_result.get("Create Table") or list(create_result.values())[1]
        else:
            # 元组格式
            create_sql = create_result[1]  # 第二个元素是建表语句
        
        return {
            "fields": fields,
            "indexes": indexes,
            "create_sql": create_sql
        }

def migrate_table_data(src_conn, dest_conn, table, batch_size=BATCH_SIZE):
    """
    迁移表数据
    
    Args:
        src_conn: 源数据库连接
        dest_conn: 目标数据库连接
        table: 表名
        batch_size: 分批处理的大小
    
    Returns:
        int: 迁移的记录数
    """
    # 获取表中的记录数
    total_count = get_table_count(src_conn, table)
    
    if total_count == 0:
        logger.info(f"表 {table} 中没有数据，跳过迁移")
        return 0
    
    logger.info(f"开始迁移表 {table} 的数据，总记录数: {total_count}")
    
    # 创建进度条
    progress_bar = tqdm(total=total_count, desc=f"迁移 {table}")
    
    # 清空目标表
    with dest_conn.cursor() as cursor:
        cursor.execute(f"DELETE FROM {table}")
        dest_conn.commit()
    
    # 分批获取和插入数据
    offset = 0
    migrated_count = 0
    
    while offset < total_count:
        # 获取一批数据
        with src_conn.cursor() as cursor:
            cursor.execute(f"SELECT * FROM {table} LIMIT {offset}, {batch_size}")
            rows = cursor.fetchall()
        
        if not rows:
            break
        
        # 插入数据到目标表
        with dest_conn.cursor() as cursor:
            for row in rows:
                # 构建插入语句
                fields = ", ".join(f"`{field}`" for field in row.keys())
                placeholders = ", ".join(["%s"] * len(row))
                values = list(row.values())
                
                sql = f"INSERT INTO {table} ({fields}) VALUES ({placeholders})"
                
                # 执行插入
                try:
                    cursor.execute(sql, values)
                    migrated_count += 1
                except Exception as e:
                    logger.error(f"插入数据失败: {e}")
                    logger.error(f"SQL: {sql}")
                    logger.error(f"Values: {values}")
        
        # 提交事务
        dest_conn.commit()
        
        # 更新进度条
        progress_bar.update(len(rows))
        
        # 更新偏移量
        offset += batch_size
    
    # 关闭进度条
    progress_bar.close()
    
    logger.info(f"表 {table} 的数据迁移完成，共迁移 {migrated_count} 条记录")
    
    return migrated_count

def migrate_all_data(src_conn, dest_conn, tables=None, skip_tables=None):
    """
    迁移所有表数据
    
    Args:
        src_conn: 源数据库连接
        dest_conn: 目标数据库连接
        tables: 要迁移的表，如果为None则迁移所有表
        skip_tables: 要跳过的表
    
    Returns:
        dict: 各表迁移的记录数
    """
    # 获取所有表
    all_tables = get_tables(src_conn)
    if not tables:
        tables = all_tables
    
    # 过滤要跳过的表
    if skip_tables:
        tables = [table for table in tables if table not in skip_tables]
    
    # 迁移每个表的数据
    migration_stats = {}
    
    for table in tables:
        start_time = time.time()
        migrated_count = migrate_table_data(src_conn, dest_conn, table)
        end_time = time.time()
        
        migration_stats[table] = {
            "records": migrated_count,
            "time": end_time - start_time
        }
    
    return migration_stats

def main():
    """主函数，解析命令行参数并执行迁移"""
    global BATCH_SIZE
    
    parser = argparse.ArgumentParser(description='数据从Docker MySQL迁移到公司内部MySQL')
    parser.add_argument('--src-host', default='localhost', help='源数据库主机')
    parser.add_argument('--src-port', type=int, default=5455, help='源数据库端口')
    parser.add_argument('--src-user', default='root', help='源数据库用户名')
    parser.add_argument('--src-password', default='infini_rag_flow', help='源数据库密码')
    parser.add_argument('--src-database', default='rag_flow', help='源数据库名称')
    parser.add_argument('--ref-key', help='数据库引用Key，默认使用配置中的default')
    parser.add_argument('--tables', nargs='*', help='指定要迁移的表，默认迁移所有表')
    parser.add_argument('--skip-tables', nargs='*', help='指定要跳过的表')
    parser.add_argument('--batch-size', type=int, default=BATCH_SIZE, help='分批处理的大小')
    
    args = parser.parse_args()
    args.skip_tables = ['api_4_conversation', 'api_token', 'canvas_template', 'conversation', 'dialog', 'document', 'file', 'file2document', 'invitation_code', 'knowledgebase', 'llm', 'llm_factories', 'migratehistory']
    BATCH_SIZE = args.batch_size
    
    try:
        # 连接源数据库
        logger.info(f"正在连接源数据库: {args.src_host}:{args.src_port}/{args.src_database}")
        src_conn = get_docker_mysql_connection(
            args.src_host, args.src_port, args.src_user, args.src_password, args.src_database
        )

        # 连接目标数据库
        logger.info(f"正在连接目标数据库: Zebra/{args.ref_key or ZEBRA_REF_KEYS['default']}")
        dest_conn = get_zebra_connection(args.ref_key)
        
        # 执行数据迁移
        logger.info("开始数据迁移...")
        start_time = time.time()
        
        stats = migrate_all_data(src_conn, dest_conn, args.tables, args.skip_tables)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 输出迁移统计信息
        logger.info("\n数据迁移完成！")
        logger.info(f"总耗时: {total_time:.2f} 秒")
        
        total_records = sum(stat["records"] for stat in stats.values())
        logger.info(f"总迁移记录数: {total_records}")
        
        logger.info("\n各表迁移统计:")
        for table, stat in stats.items():
            logger.info(f"  - {table}: {stat['records']} 条记录, 耗时 {stat['time']:.2f} 秒")
        
        # 关闭连接
        src_conn.close()
        dest_conn.close()
        
        return 0
    
    except Exception as e:
        logger.error(f"数据迁移失败: {e}", exc_info=True)
        return 1

if __name__ == '__main__':
    sys.exit(main()) 