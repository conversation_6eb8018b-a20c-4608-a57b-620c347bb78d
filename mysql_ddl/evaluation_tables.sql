-- RAG评估系统数据库表结构
-- 用于创建评估相关的数据库表

USE rag_flow;

-- ==================== RAG评估系统相关表 ====================

-- 评估数据集表
CREATE TABLE IF NOT EXISTS `evaluation_dataset` (
  `id` VARCHAR(32) NOT NULL,
  `name` VARCHAR(128) NOT NULL COMMENT '数据集名称',
  `description` TEXT DEFAULT NULL COMMENT '数据集描述',
  `kb_id` VARCHAR(32) NOT NULL COMMENT '关联的知识库ID',
  `tenant_id` VARCHAR(32) NOT NULL COMMENT '租户ID',
  `created_by` VARCHAR(32) NOT NULL COMMENT '创建者ID',
  `dataset_type` VARCHAR(32) DEFAULT 'qa_pairs' COMMENT '数据集类型：qa_pairs, questions_only',
  `status` VARCHAR(16) DEFAULT 'active' COMMENT '状态：active, archived',
  `total_samples` INT DEFAULT 0 COMMENT '样本总数',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_evaluation_dataset_tenant_kb` (`tenant_id`, `kb_id`),
  INDEX `idx_evaluation_dataset_created_by` (`created_by`),
  INDEX `idx_evaluation_dataset_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评估数据集表';

-- 评估样本表
CREATE TABLE IF NOT EXISTS `evaluation_sample` (
  `id` VARCHAR(32) NOT NULL,
  `dataset_id` VARCHAR(32) NOT NULL COMMENT '数据集ID',
  `question` TEXT NOT NULL COMMENT '问题',
  `ground_truth` TEXT DEFAULT NULL COMMENT '标准答案',
  `contexts` TEXT DEFAULT NULL COMMENT '相关文档片段(JSON格式)',
  `metadata` TEXT DEFAULT NULL COMMENT '额外信息(JSON格式)',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_evaluation_sample_dataset_id` (`dataset_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评估样本表';

-- 评估任务表
CREATE TABLE IF NOT EXISTS `evaluation_task` (
  `id` VARCHAR(32) NOT NULL,
  `name` VARCHAR(128) NOT NULL COMMENT '任务名称',
  `dataset_id` VARCHAR(32) NOT NULL COMMENT '数据集ID',
  `kb_id` VARCHAR(32) NOT NULL COMMENT '知识库ID',
  `tenant_id` VARCHAR(32) NOT NULL COMMENT '租户ID',
  `created_by` VARCHAR(32) NOT NULL COMMENT '创建者ID',
  `metrics` TEXT NOT NULL COMMENT '评估指标(JSON格式)',
  `retrieval_config` TEXT DEFAULT NULL COMMENT '检索配置(JSON格式)',
  `llm_config` TEXT DEFAULT NULL COMMENT 'LLM配置(JSON格式)',
  `status` VARCHAR(16) DEFAULT 'pending' COMMENT '任务状态：pending, running, completed, failed',
  `progress` INT DEFAULT 0 COMMENT '进度百分比',
  `total_samples` INT DEFAULT 0 COMMENT '总样本数',
  `processed_samples` INT DEFAULT 0 COMMENT '已处理样本数',
  `started_at` DATETIME DEFAULT NULL COMMENT '开始时间',
  `completed_at` DATETIME DEFAULT NULL COMMENT '完成时间',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_evaluation_task_tenant_kb` (`tenant_id`, `kb_id`),
  INDEX `idx_evaluation_task_dataset_id` (`dataset_id`),
  INDEX `idx_evaluation_task_status` (`status`),
  INDEX `idx_evaluation_task_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评估任务表';

-- 评估结果表
CREATE TABLE IF NOT EXISTS `evaluation_result` (
  `id` VARCHAR(32) NOT NULL,
  `task_id` VARCHAR(32) NOT NULL COMMENT '任务ID',
  `sample_id` VARCHAR(32) NOT NULL COMMENT '样本ID',
  `retrieved_contexts` TEXT DEFAULT NULL COMMENT '检索到的文档(JSON格式)',
  `generated_answer` TEXT DEFAULT NULL COMMENT '生成的答案',
  `faithfulness` FLOAT DEFAULT NULL COMMENT '忠实度指标',
  `answer_relevancy` FLOAT DEFAULT NULL COMMENT '答案相关性指标',
  `context_precision` FLOAT DEFAULT NULL COMMENT '上下文精确度指标',
  `context_recall` FLOAT DEFAULT NULL COMMENT '上下文召回率指标',
  `context_relevancy` FLOAT DEFAULT NULL COMMENT '上下文相关性指标',
  `answer_correctness` FLOAT DEFAULT NULL COMMENT '答案正确性指标',
  `answer_similarity` FLOAT DEFAULT NULL COMMENT '答案相似性指标',
  `execution_time` FLOAT DEFAULT NULL COMMENT '执行时间(秒)',
  `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_evaluation_result_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评估结果表';

-- 评估报告表
CREATE TABLE IF NOT EXISTS `evaluation_report` (
  `id` VARCHAR(32) NOT NULL,
  `task_id` VARCHAR(32) NOT NULL COMMENT '任务ID',
  `avg_faithfulness` FLOAT DEFAULT NULL COMMENT '平均忠实度',
  `avg_answer_relevancy` FLOAT DEFAULT NULL COMMENT '平均答案相关性',
  `avg_context_precision` FLOAT DEFAULT NULL COMMENT '平均上下文精确度',
  `avg_context_recall` FLOAT DEFAULT NULL COMMENT '平均上下文召回率',
  `avg_context_relevancy` FLOAT DEFAULT NULL COMMENT '平均上下文相关性',
  `avg_answer_correctness` FLOAT DEFAULT NULL COMMENT '平均答案正确性',
  `avg_answer_similarity` FLOAT DEFAULT NULL COMMENT '平均答案相似性',
  `total_samples` INT DEFAULT 0 COMMENT '总样本数',
  `successful_samples` INT DEFAULT 0 COMMENT '成功样本数',
  `failed_samples` INT DEFAULT 0 COMMENT '失败样本数',
  `avg_execution_time` FLOAT DEFAULT NULL COMMENT '平均执行时间',
  `detailed_report` TEXT DEFAULT NULL COMMENT '详细报告(JSON格式)',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_evaluation_report_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评估报告表';

-- 添加外键约束（可选，根据需要启用）
-- ALTER TABLE `evaluation_dataset` ADD CONSTRAINT `fk_evaluation_dataset_kb` FOREIGN KEY (`kb_id`) REFERENCES `knowledgebase` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `evaluation_dataset` ADD CONSTRAINT `fk_evaluation_dataset_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `evaluation_dataset` ADD CONSTRAINT `fk_evaluation_dataset_user` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`) ON DELETE CASCADE;

-- ALTER TABLE `evaluation_sample` ADD CONSTRAINT `fk_evaluation_sample_dataset` FOREIGN KEY (`dataset_id`) REFERENCES `evaluation_dataset` (`id`) ON DELETE CASCADE;

-- ALTER TABLE `evaluation_task` ADD CONSTRAINT `fk_evaluation_task_dataset` FOREIGN KEY (`dataset_id`) REFERENCES `evaluation_dataset` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `evaluation_task` ADD CONSTRAINT `fk_evaluation_task_kb` FOREIGN KEY (`kb_id`) REFERENCES `knowledgebase` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `evaluation_task` ADD CONSTRAINT `fk_evaluation_task_tenant` FOREIGN KEY (`tenant_id`) REFERENCES `tenant` (`id`) ON DELETE CASCADE;
-- ALTER TABLE `evaluation_task` ADD CONSTRAINT `fk_evaluation_task_user` FOREIGN KEY (`created_by`) REFERENCES `user` (`id`) ON DELETE CASCADE;

-- ALTER TABLE `evaluation_result` ADD CONSTRAINT `fk_evaluation_result_task` FOREIGN KEY (`task_id`) REFERENCES `evaluation_task` (`id`) ON DELETE CASCADE;

-- ALTER TABLE `evaluation_report` ADD CONSTRAINT `fk_evaluation_report_task` FOREIGN KEY (`task_id`) REFERENCES `evaluation_task` (`id`) ON DELETE CASCADE; 