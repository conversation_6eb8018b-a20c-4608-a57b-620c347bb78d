#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试Zebra MySQL连接的脚本
用于验证迁移到公司内部MySQL后的数据库连接是否正常
"""

import os
import sys
import argparse
import logging
import time
from datetime import datetime

# 设置项目根目录
project_root = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
sys.path.insert(0, project_root)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入Zebra适配器
from api.db.zebra_peewee_adapter import ZebraPooledMySQLDatabase
from conf.zebra.config import ZEBRA_APP_KEY, ZEBRA_REF_KEYS

def test_zebra_connection(ref_key=None, show_schema=False):
    """
    测试Zebra连接并执行基本查询
    
    Args:
        ref_key: 数据库引用Key，如果为None则使用默认引用Key
        show_schema: 是否显示表结构信息
    
    Returns:
        bool: 连接测试是否成功
    """
    if ref_key is None:
        ref_key = ZEBRA_REF_KEYS["default"]
    
    try:
        # 创建数据库连接
        logger.info(f"尝试通过Zebra连接到MySQL，应用Key: {ZEBRA_APP_KEY}, 引用Key: {ref_key}")
        db = ZebraPooledMySQLDatabase("rag_flow", ref_key=ref_key)
        
        # 执行查询
        cursor = db.execute_sql("SHOW TABLES")
        tables = cursor.fetchall()
        
        logger.info(f"数据库中的表：")
        for table in tables:
            table_name = table[0]
            
            try:
                count_cursor = db.execute_sql(f"SELECT COUNT(*) FROM {table_name}")
                count_result = count_cursor.fetchone()
                # 元组格式
                row_count = count_result[0]
                
                logger.info(f"  - {table_name}: {row_count} 行")
                
                # 如果需要显示表结构
                if show_schema:
                    try:
                        # 获取表结构 - 使用SHOW COLUMNS FROM代替DESC
                        schema_cursor = db.execute_sql(f"SHOW COLUMNS FROM {table_name}")
                        schema = schema_cursor.fetchall()
                        
                        logger.info(f"    表结构:")
                        for column in schema:
                            # 元组格式（假设顺序：Field, Type, Null, Key, ...）
                            field = column[0]
                            type_ = column[1]
                            null = column[2]
                            key = column[3] if len(column) > 3 else ''
                            logger.info(f"      {field}: {type_}, {'NULL' if null == 'YES' else 'NOT NULL'}{', ' + key if key else ''}")
                    except Exception as e:
                        logger.error(f"    无法获取表结构: {e}")
            except Exception as e:
                logger.error(f"  - {table_name}: 无法获取行数: {e}")
        
        # 测试一些特定表
        tables_to_check = ['user', 'tenant', 'document', 'knowledgebase']
        
        logger.info("检查关键表是否存在:")
        for table in tables_to_check:
            try:
                cursor = db.execute_sql(f"SELECT COUNT(*) FROM {table}")
                result = cursor.fetchone()
                row_count = result[0]
                logger.info(f"  - {table}: ✓ (包含 {row_count} 条记录)")
            except Exception as e:
                logger.error(f"  - {table}: ✗ (错误: {e})")
        
        # 关闭连接
        db.close()
        logger.info("Zebra连接测试成功！")
        return True
    
    except Exception as e:
        logger.error(f"Zebra连接测试失败: {e}")
        return False

def clear_all_tables_data(ref_key=None, confirm=False):
    """
    清除所有表中的数据
    
    Args:
        ref_key: 数据库引用Key，如果为None则使用默认引用Key
        confirm: 是否已确认执行清除操作
    
    Returns:
        bool: 清除操作是否成功
    """
    
    if ref_key is None:
        ref_key = ZEBRA_REF_KEYS["default"]
    
    try:
        # 创建数据库连接
        logger.info(f"尝试通过Zebra连接到MySQL，应用Key: {ZEBRA_APP_KEY}, 引用Key: {ref_key}")
        db = ZebraPooledMySQLDatabase("rag_flow", ref_key=ref_key)
        
        # 获取所有表
        cursor = db.execute_sql("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        
        logger.info(f"准备清除以下表中的所有数据：")
        for table_name in tables:
            logger.info(f"  - {table_name}")
        
        # 最后确认
        logger.warning("即将清除所有表中的数据，此操作不可逆！")
        
        # 清除每个表中的数据
        for table_name in tables:
            try:
                logger.info(f"正在清除表 {table_name} 中的数据...")
                db.execute_sql(f"TRUNCATE TABLE {table_name}")
                logger.info(f"表 {table_name} 数据清除完成")
            except Exception as e:
                logger.error(f"清除表 {table_name} 数据失败: {e}")
                # 尝试使用DELETE FROM
                try:
                    logger.info(f"尝试使用DELETE FROM清除表 {table_name} 中的数据...")
                    db.execute_sql(f"DELETE FROM {table_name}")
                    logger.info(f"表 {table_name} 数据清除完成")
                except Exception as e2:
                    logger.error(f"无法清除表 {table_name} 数据: {e2}")
        
        # 关闭连接
        db.close()
        logger.info("所有表数据清除操作完成！")
        return True
        
    except Exception as e:
        logger.error(f"清除表数据失败: {e}")
        return False

def main():
    """主函数，解析命令行参数并执行测试"""
    parser = argparse.ArgumentParser(description='测试Zebra MySQL连接')
    parser.add_argument('--ref-key', help='数据库引用Key，默认使用配置中的default')
    parser.add_argument('--show-schema', action='store_true', help='显示表结构信息')
    parser.add_argument('--clear-data', action='store_true', help='清除所有表中的数据')
    parser.add_argument('--confirm', action='store_true', help='确认执行清除数据操作')
    args = parser.parse_args()
    args.ref_key = "sgai_rag_flow_test"

    if args.clear_data:
        # 执行清除数据操作
        success = clear_all_tables_data(args.ref_key, args.confirm)
    else:
        # 执行连接测试
        success = test_zebra_connection(args.ref_key, args.show_schema)
    
    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main() 