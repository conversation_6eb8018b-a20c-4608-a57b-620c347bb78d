#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Flask-SQLAlchemy连接公司内部MySQL组件的示例代码
作为Docker MySQL迁移到公司内部MySQL组件的方案示例
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
import argparse
import os
from datetime import datetime
import uuid

# 创建Flask应用
app = Flask(__name__)

# 数据库配置
def configure_app(app, host, port, user, password, database):
    """配置Flask应用的数据库连接"""
    app.config['SQLALCHEMY_DATABASE_URI'] = (
        f"mysql+pymysql://{user}:{password}@{host}:{port}/{database}"
    )
    
    # 连接池配置
    app.config['SQLALCHEMY_POOL_SIZE'] = 100  # 最大连接数
    app.config['SQLALCHEMY_POOL_TIMEOUT'] = 30  # 连接超时时间（秒）
    app.config['SQLALCHEMY_POOL_RECYCLE'] = 3600  # 连接回收时间（秒）
    
    # 其他SQLAlchemy配置
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False  # 禁用修改跟踪，减少内存使用
    app.config['SQLALCHEMY_ECHO'] = False  # 设为True可以看到生成的SQL语句（调试用）
    
    return app

# 初始化SQLAlchemy对象
db = SQLAlchemy()

# 定义User模型（对应用户表）
class User(db.Model):
    __tablename__ = 'user'
    
    id = db.Column(db.String(32), primary_key=True)
    access_token = db.Column(db.String(255), index=True)
    nickname = db.Column(db.String(100), nullable=False, index=True)
    password = db.Column(db.String(255), index=True)
    email = db.Column(db.String(255), nullable=False, index=True)
    avatar = db.Column(db.Text)
    language = db.Column(db.String(32), default='Chinese', index=True)
    color_schema = db.Column(db.String(32), default='Bright', index=True)
    timezone = db.Column(db.String(64), default='UTC+8\tAsia/Shanghai', index=True)
    last_login_time = db.Column(db.DateTime, index=True)
    is_authenticated = db.Column(db.String(1), default='1', index=True)
    is_active = db.Column(db.String(1), default='1', index=True)
    is_anonymous = db.Column(db.String(1), default='0', index=True)
    login_channel = db.Column(db.String(255), index=True)
    status = db.Column(db.String(1), default='1', index=True)
    is_superuser = db.Column(db.Boolean, default=False, index=True)
    create_time = db.Column(db.BigInteger, index=True)
    create_date = db.Column(db.DateTime, index=True)
    update_time = db.Column(db.BigInteger, index=True)
    update_date = db.Column(db.DateTime, index=True)
    
    def __repr__(self):
        return f'<User {self.nickname}>'

# 定义Document模型（对应文档表）
class Document(db.Model):
    __tablename__ = 'document'
    
    id = db.Column(db.String(32), primary_key=True)
    thumbnail = db.Column(db.Text)
    kb_id = db.Column(db.String(256), nullable=False, index=True)
    parser_id = db.Column(db.String(32), nullable=False, index=True)
    parser_config = db.Column(db.Text)
    source_type = db.Column(db.String(128), default='local', index=True)
    source_url = db.Column(db.String(1024), index=True)
    last_update_time = db.Column(db.DateTime, index=True)
    update_interval = db.Column(db.Integer, default=0, index=True)
    next_update_time = db.Column(db.DateTime, index=True)
    type = db.Column(db.String(32), nullable=False, index=True)
    created_by = db.Column(db.String(32), nullable=False, index=True)
    name = db.Column(db.String(255), index=True)
    location = db.Column(db.String(255), index=True)
    size = db.Column(db.Integer, default=0, index=True)
    token_num = db.Column(db.Integer, default=0, index=True)
    chunk_num = db.Column(db.Integer, default=0, index=True)
    progress = db.Column(db.Float, default=0, index=True)
    progress_msg = db.Column(db.Text, default='')
    process_begin_at = db.Column(db.DateTime, index=True)
    process_duation = db.Column(db.Float, default=0)
    meta_fields = db.Column(db.Text)
    run = db.Column(db.String(1), default='0', index=True)
    status = db.Column(db.String(1), default='1', index=True)
    create_time = db.Column(db.BigInteger, index=True)
    create_date = db.Column(db.DateTime, index=True)
    update_time = db.Column(db.BigInteger, index=True)
    update_date = db.Column(db.DateTime, index=True)

    def __repr__(self):
        return f'<Document {self.name}>'

# 工具函数 - 当前时间戳（毫秒）
def current_timestamp():
    return int(datetime.now().timestamp() * 1000)

# 示例：用户数据访问对象（DAO）
class UserDAO:
    @staticmethod
    def create_user(nickname, email, password=None):
        """创建新用户"""
        now = current_timestamp()
        now_dt = datetime.fromtimestamp(now / 1000)
        
        new_user = User(
            id=uuid.uuid4().hex,
            nickname=nickname,
            email=email,
            password=password,
            create_time=now,
            create_date=now_dt,
            update_time=now,
            update_date=now_dt
        )
        
        db.session.add(new_user)
        db.session.commit()
        return new_user
    
    @staticmethod
    def get_user_by_id(user_id):
        """根据ID获取用户"""
        return User.query.filter_by(id=user_id).first()
    
    @staticmethod
    def get_users_by_email(email):
        """根据邮箱获取用户"""
        return User.query.filter_by(email=email).all()
    
    @staticmethod
    def update_user(user_id, **kwargs):
        """更新用户信息"""
        user = User.query.filter_by(id=user_id).first()
        if not user:
            return None
        
        # 更新字段
        for key, value in kwargs.items():
            if hasattr(user, key):
                setattr(user, key, value)
        
        # 更新时间
        now = current_timestamp()
        user.update_time = now
        user.update_date = datetime.fromtimestamp(now / 1000)
        
        db.session.commit()
        return user

# 示例：测试数据库连接并执行简单操作
def test_database(app_instance):
    with app_instance.app_context():
        try:
            # 检查用户表是否存在
            user_count = User.query.count()
            print(f"用户表存在，包含 {user_count} 条记录")
            
            # 检查文档表是否存在
            doc_count = Document.query.count()
            print(f"文档表存在，包含 {doc_count} 条记录")
            
            # 测试插入用户（不实际提交）
            test_user = User(
                id="test_" + "0" * 27,
                nickname="测试用户",
                email="<EMAIL>",
                create_time=current_timestamp(),
                create_date=datetime.now()
            )
            db.session.add(test_user)
            print("测试用户创建成功")
            
            # 回滚，不保存测试数据
            db.session.rollback()
            print("所有操作已回滚")
            
            return True
        except Exception as e:
            print(f"数据库测试失败: {e}")
            db.session.rollback()
            return False

def main():
    parser = argparse.ArgumentParser(description='Flask-SQLAlchemy连接MySQL示例')
    parser.add_argument('--host', required=True, help='数据库主机地址')
    parser.add_argument('--port', type=int, default=3306, help='数据库端口号')
    parser.add_argument('--user', required=True, help='数据库用户名')
    parser.add_argument('--password', required=True, help='数据库密码')
    parser.add_argument('--database', required=True, help='数据库名称')
    
    args = parser.parse_args()
    
    # 配置应用
    configure_app(app, args.host, args.port, args.user, args.password, args.database)
    
    # 初始化数据库与应用的连接
    db.init_app(app)
    
    # 测试数据库连接
    print(f"尝试连接到 {args.host}:{args.port}/{args.database}...")
    if test_database(app):
        print("Flask-SQLAlchemy连接测试成功！")
    else:
        print("Flask-SQLAlchemy连接测试失败！")
        exit(1)

if __name__ == "__main__":
    main() 