#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用DBUtils连接公司内部MySQL组件的示例代码
作为Docker MySQL迁移到公司内部MySQL组件的方案示例
"""

from dbutils.pooled_db import PooledDB
import pymysql
import argparse
import sys
import uuid
import time
import datetime

class MySQLPool:
    """MySQL连接池管理类"""
    
    def __init__(self, host, port, user, password, database, 
                 max_connections=100, min_cached=5, max_cached=20):
        """
        初始化数据库连接池
        
        参数:
            host: 数据库主机地址
            port: 数据库端口
            user: 数据库用户名
            password: 数据库密码
            database: 数据库名
            max_connections: 最大连接数
            min_cached: 最小空闲连接数
            max_cached: 最大空闲连接数
        """
        self.pool = PooledDB(
            creator=pymysql,  # 使用pymysql作为数据库连接库
            maxconnections=max_connections,  # 连接池允许的最大连接数
            mincached=min_cached,  # 初始化时创建的空闲连接数
            maxcached=max_cached,  # 连接池中允许的最大空闲连接数
            blocking=True,  # 连接池满后是否阻塞等待
            maxusage=None,  # 一个连接最多被使用的次数，None表示无限制
            setsession=[],  # 开始会话前执行的命令
            ping=0,  # 用于检查连接是否有效的间隔
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4',
            autocommit=False  # 默认不自动提交
        )
        self.host = host
        self.port = port
        self.database = database

    def get_connection(self):
        """获取数据库连接"""
        return self.pool.connection()
    
    def __str__(self):
        return f"MySQLPool(host={self.host}, port={self.port}, database={self.database})"


class UserDAO:
    """用户数据访问对象"""
    
    def __init__(self, mysql_pool):
        """
        初始化用户DAO
        
        参数:
            mysql_pool: MySQL连接池对象
        """
        self.pool = mysql_pool
    
    def get_user_count(self):
        """获取用户总数"""
        conn = self.pool.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("SELECT COUNT(*) FROM user")
            return cursor.fetchone()[0]
        finally:
            cursor.close()
            conn.close()
    
    def get_user_by_id(self, user_id):
        """根据ID获取用户"""
        conn = self.pool.get_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)  # 返回字典格式结果
        try:
            cursor.execute(
                "SELECT * FROM user WHERE id = %s",
                (user_id,)
            )
            return cursor.fetchone()
        finally:
            cursor.close()
            conn.close()
    
    def get_users_by_email(self, email):
        """根据邮箱获取用户"""
        conn = self.pool.get_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        try:
            cursor.execute(
                "SELECT * FROM user WHERE email = %s",
                (email,)
            )
            return cursor.fetchall()
        finally:
            cursor.close()
            conn.close()
    
    def create_user(self, nickname, email, password=None):
        """创建新用户"""
        conn = self.pool.get_connection()
        cursor = conn.cursor()
        try:
            # 生成用户ID
            user_id = uuid.uuid4().hex
            
            # 生成时间戳
            now = int(time.time() * 1000)  # 毫秒级时间戳
            now_dt = datetime.datetime.now()  # 日期时间对象
            
            # 执行插入
            cursor.execute(
                """
                INSERT INTO user (
                    id, nickname, email, password, 
                    language, color_schema, timezone,
                    is_authenticated, is_active, is_anonymous, status,
                    create_time, create_date, update_time, update_date
                ) VALUES (
                    %s, %s, %s, %s, 
                    %s, %s, %s,
                    %s, %s, %s, %s,
                    %s, %s, %s, %s
                )
                """,
                (
                    user_id, nickname, email, password,
                    'Chinese', 'Bright', 'UTC+8\tAsia/Shanghai',
                    '1', '1', '0', '1',
                    now, now_dt, now, now_dt
                )
            )
            conn.commit()
            return user_id
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            cursor.close()
            conn.close()
    
    def update_user(self, user_id, **kwargs):
        """更新用户信息"""
        if not kwargs:
            return False
        
        # 构建UPDATE语句
        set_clause = []
        params = []
        
        for key, value in kwargs.items():
            set_clause.append(f"{key} = %s")
            params.append(value)
        
        # 添加更新时间
        now = int(time.time() * 1000)
        now_dt = datetime.datetime.now()
        set_clause.append("update_time = %s")
        set_clause.append("update_date = %s")
        params.extend([now, now_dt])
        
        # 添加WHERE条件
        params.append(user_id)
        
        conn = self.pool.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute(
                f"UPDATE user SET {', '.join(set_clause)} WHERE id = %s",
                tuple(params)
            )
            conn.commit()
            return cursor.rowcount > 0
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            cursor.close()
            conn.close()


class DocumentDAO:
    """文档数据访问对象"""
    
    def __init__(self, mysql_pool):
        """
        初始化文档DAO
        
        参数:
            mysql_pool: MySQL连接池对象
        """
        self.pool = mysql_pool
    
    def get_document_count(self):
        """获取文档总数"""
        conn = self.pool.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("SELECT COUNT(*) FROM document")
            return cursor.fetchone()[0]
        finally:
            cursor.close()
            conn.close()
    
    def get_document_by_id(self, doc_id):
        """根据ID获取文档"""
        conn = self.pool.get_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        try:
            cursor.execute(
                "SELECT * FROM document WHERE id = %s",
                (doc_id,)
            )
            return cursor.fetchone()
        finally:
            cursor.close()
            conn.close()
    
    def get_documents_by_kb(self, kb_id, limit=100, offset=0):
        """获取指定知识库的文档列表"""
        conn = self.pool.get_connection()
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        try:
            cursor.execute(
                """
                SELECT id, name, type, size, progress, status, create_time, update_time
                FROM document 
                WHERE kb_id = %s
                ORDER BY create_time DESC
                LIMIT %s OFFSET %s
                """,
                (kb_id, limit, offset)
            )
            return cursor.fetchall()
        finally:
            cursor.close()
            conn.close()


def test_connection(mysql_pool):
    """测试数据库连接并验证基本表是否存在"""
    print(f"使用 {mysql_pool} 测试连接")
    
    try:
        # 测试用户表
        user_dao = UserDAO(mysql_pool)
        user_count = user_dao.get_user_count()
        print(f"用户表存在，包含 {user_count} 条记录")
        
        # 测试文档表
        doc_dao = DocumentDAO(mysql_pool)
        doc_count = doc_dao.get_document_count()
        print(f"文档表存在，包含 {doc_count} 条记录")
        
        # 测试事务（不实际提交）
        conn = mysql_pool.get_connection()
        cursor = conn.cursor()
        
        try:
            # 准备测试数据
            test_id = "test_" + "0" * 27
            test_nickname = "测试用户"
            test_email = "<EMAIL>"
            
            # 开始测试事务
            print("\n开始测试事务操作（会自动回滚）:")
            
            # 插入测试
            cursor.execute(
                """
                INSERT INTO user (id, nickname, email, create_time, create_date)
                VALUES (%s, %s, %s, %s, %s)
                """,
                (test_id, test_nickname, test_email, int(time.time() * 1000), datetime.datetime.now())
            )
            print("- INSERT: 成功插入测试用户")
            
            # 查询测试
            cursor.execute("SELECT * FROM user WHERE id = %s", (test_id,))
            result = cursor.fetchone()
            if result:
                print("- SELECT: 成功查询测试用户")
            
            # 更新测试
            cursor.execute(
                "UPDATE user SET nickname = %s WHERE id = %s",
                ("更新后的测试用户", test_id)
            )
            print("- UPDATE: 成功更新测试用户")
            
            # 删除测试
            cursor.execute("DELETE FROM user WHERE id = %s", (test_id,))
            print("- DELETE: 成功删除测试用户")
            
            # 回滚事务
            conn.rollback()
            print("所有操作已回滚，数据库未被修改")
            
        except Exception as e:
            conn.rollback()
            print(f"事务测试失败: {e}")
        finally:
            cursor.close()
            conn.close()
        
        print("\n连接测试成功！")
        return True
    except Exception as e:
        print(f"连接测试失败: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description='DBUtils连接MySQL示例')
    parser.add_argument('--host', required=True, help='数据库主机地址')
    parser.add_argument('--port', type=int, default=3306, help='数据库端口号')
    parser.add_argument('--user', required=True, help='数据库用户名')
    parser.add_argument('--password', required=True, help='数据库密码')
    parser.add_argument('--database', required=True, help='数据库名称')
    
    args = parser.parse_args()
    
    # 创建MySQL连接池
    mysql_pool = MySQLPool(
        host=args.host,
        port=args.port,
        user=args.user,
        password=args.password,
        database=args.database
    )
    
    # 测试连接
    success = test_connection(mysql_pool)
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main() 