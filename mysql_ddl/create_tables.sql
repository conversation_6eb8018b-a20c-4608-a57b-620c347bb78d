-- MySQL DDL Script for RA<PERSON><PERSON><PERSON>
-- 创建数据库
CREATE DATABASE IF NOT EXISTS rag_flow DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE rag_flow;

-- 基础模型字段
-- 所有表的通用字段（BaseModel）：
-- create_time BIGINT - 创建时间戳
-- create_date DATETIME - 创建日期时间
-- update_time BIGINT - 更新时间戳
-- update_date DATETIME - 更新日期时间

-- 用户表
CREATE TABLE IF NOT EXISTS `user` (
  `id` VARCHAR(32) NOT NULL,
  `access_token` VARCHAR(255) DEFAULT NULL,
  `nickname` VA<PERSON>HAR(100) NOT NULL,
  `password` VARCHAR(255) DEFAULT NULL,
  `email` VARCHAR(255) NOT NULL,
  `avatar` TEXT DEFAULT NULL,
  `language` VARCHAR(32) DEFAULT 'Chinese',
  `color_schema` VARCHAR(32) DEFAULT 'Bright',
  `timezone` VARCHAR(64) DEFAULT 'UTC+8\tAsia/Shanghai',
  `last_login_time` DATETIME DEFAULT NULL,
  `is_authenticated` VARCHAR(1) DEFAULT '1',
  `is_active` VARCHAR(1) DEFAULT '1',
  `is_anonymous` VARCHAR(1) DEFAULT '0',
  `login_channel` VARCHAR(255) DEFAULT NULL,
  `status` VARCHAR(1) DEFAULT '1',
  `is_superuser` BOOLEAN DEFAULT FALSE,
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_user_access_token` (`access_token`),
  INDEX `idx_user_nickname` (`nickname`),
  INDEX `idx_user_password` (`password`),
  INDEX `idx_user_email` (`email`),
  INDEX `idx_user_language` (`language`),
  INDEX `idx_user_color_schema` (`color_schema`),
  INDEX `idx_user_timezone` (`timezone`),
  INDEX `idx_user_last_login_time` (`last_login_time`),
  INDEX `idx_user_is_authenticated` (`is_authenticated`),
  INDEX `idx_user_is_active` (`is_active`),
  INDEX `idx_user_is_anonymous` (`is_anonymous`),
  INDEX `idx_user_login_channel` (`login_channel`),
  INDEX `idx_user_status` (`status`),
  INDEX `idx_user_is_superuser` (`is_superuser`),
  INDEX `idx_user_create_time` (`create_time`),
  INDEX `idx_user_create_date` (`create_date`),
  INDEX `idx_user_update_time` (`update_time`),
  INDEX `idx_user_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 租户表
CREATE TABLE IF NOT EXISTS `tenant` (
  `id` VARCHAR(32) NOT NULL,
  `created_by` VARCHAR(32) DEFAULT NULL COMMENT '创建者id',
  `name` VARCHAR(100) DEFAULT NULL,
  `public_key` VARCHAR(255) DEFAULT NULL,
  `llm_id` VARCHAR(128) NOT NULL,
  `embd_id` VARCHAR(128) NOT NULL,
  `asr_id` VARCHAR(128) NOT NULL,
  `img2txt_id` VARCHAR(128) NOT NULL,
  `rerank_id` VARCHAR(128) NOT NULL,
  `tts_id` VARCHAR(256) DEFAULT NULL,
  `parser_ids` VARCHAR(256) NOT NULL,
  `credit` INT DEFAULT 512,
  `status` VARCHAR(1) DEFAULT '1',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_tenant_created_by` (`created_by`),
  INDEX `idx_tenant_name` (`name`),
  INDEX `idx_tenant_public_key` (`public_key`),
  INDEX `idx_tenant_llm_id` (`llm_id`),
  INDEX `idx_tenant_embd_id` (`embd_id`),
  INDEX `idx_tenant_asr_id` (`asr_id`),
  INDEX `idx_tenant_img2txt_id` (`img2txt_id`),
  INDEX `idx_tenant_rerank_id` (`rerank_id`),
  INDEX `idx_tenant_tts_id` (`tts_id`),
  INDEX `idx_tenant_parser_ids` (`parser_ids`),
  INDEX `idx_tenant_credit` (`credit`),
  INDEX `idx_tenant_status` (`status`),
  INDEX `idx_tenant_create_time` (`create_time`),
  INDEX `idx_tenant_create_date` (`create_date`),
  INDEX `idx_tenant_update_time` (`update_time`),
  INDEX `idx_tenant_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户租户关系表
CREATE TABLE IF NOT EXISTS `user_tenant` (
  `id` VARCHAR(32) NOT NULL,
  `user_id` VARCHAR(32) NOT NULL,
  `tenant_id` VARCHAR(32) NOT NULL,
  `role` VARCHAR(32) NOT NULL,
  `invited_by` VARCHAR(32) NOT NULL,
  `status` VARCHAR(1) DEFAULT '1',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_user_tenant_user_id` (`user_id`),
  INDEX `idx_user_tenant_tenant_id` (`tenant_id`),
  INDEX `idx_user_tenant_role` (`role`),
  INDEX `idx_user_tenant_invited_by` (`invited_by`),
  INDEX `idx_user_tenant_status` (`status`),
  INDEX `idx_user_tenant_create_time` (`create_time`),
  INDEX `idx_user_tenant_create_date` (`create_date`),
  INDEX `idx_user_tenant_update_time` (`update_time`),
  INDEX `idx_user_tenant_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 邀请码表
CREATE TABLE IF NOT EXISTS `invitation_code` (
  `id` VARCHAR(32) NOT NULL,
  `code` VARCHAR(32) NOT NULL,
  `visit_time` DATETIME DEFAULT NULL,
  `user_id` VARCHAR(32) DEFAULT NULL,
  `tenant_id` VARCHAR(32) DEFAULT NULL,
  `status` VARCHAR(1) DEFAULT '1',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_invitation_code_code` (`code`),
  INDEX `idx_invitation_code_visit_time` (`visit_time`),
  INDEX `idx_invitation_code_user_id` (`user_id`),
  INDEX `idx_invitation_code_tenant_id` (`tenant_id`),
  INDEX `idx_invitation_code_status` (`status`),
  INDEX `idx_invitation_code_create_time` (`create_time`),
  INDEX `idx_invitation_code_create_date` (`create_date`),
  INDEX `idx_invitation_code_update_time` (`update_time`),
  INDEX `idx_invitation_code_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- LLM工厂表
CREATE TABLE IF NOT EXISTS `llm_factories` (
  `name` VARCHAR(128) NOT NULL,
  `logo` TEXT DEFAULT NULL,
  `tags` VARCHAR(255) NOT NULL,
  `status` VARCHAR(1) DEFAULT '1',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`name`),
  INDEX `idx_llm_factories_tags` (`tags`),
  INDEX `idx_llm_factories_status` (`status`),
  INDEX `idx_llm_factories_create_time` (`create_time`),
  INDEX `idx_llm_factories_create_date` (`create_date`),
  INDEX `idx_llm_factories_update_time` (`update_time`),
  INDEX `idx_llm_factories_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- LLM模型表
CREATE TABLE IF NOT EXISTS `llm` (
  `llm_name` VARCHAR(128) NOT NULL,
  `model_type` VARCHAR(128) NOT NULL,
  `fid` VARCHAR(128) NOT NULL,
  `max_tokens` INT DEFAULT 0,
  `tags` VARCHAR(255) NOT NULL,
  `status` VARCHAR(1) DEFAULT '1',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`fid`, `llm_name`),
  INDEX `idx_llm_llm_name` (`llm_name`),
  INDEX `idx_llm_model_type` (`model_type`),
  INDEX `idx_llm_fid` (`fid`),
  INDEX `idx_llm_tags` (`tags`),
  INDEX `idx_llm_status` (`status`),
  INDEX `idx_llm_create_time` (`create_time`),
  INDEX `idx_llm_create_date` (`create_date`),
  INDEX `idx_llm_update_time` (`update_time`),
  INDEX `idx_llm_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 租户LLM关系表
CREATE TABLE IF NOT EXISTS `tenant_llm` (
  `tenant_id` VARCHAR(32) NOT NULL,
  `llm_factory` VARCHAR(128) NOT NULL,
  `model_type` VARCHAR(128) DEFAULT NULL,
  `llm_name` VARCHAR(128) DEFAULT '',
  `api_key` VARCHAR(1024) DEFAULT NULL,
  `api_base` VARCHAR(255) DEFAULT NULL,
  `max_tokens` INT DEFAULT 8192,
  `used_tokens` INT DEFAULT 0,
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`tenant_id`, `llm_factory`, `llm_name`),
  INDEX `idx_tenant_llm_tenant_id` (`tenant_id`),
  INDEX `idx_tenant_llm_llm_factory` (`llm_factory`),
  INDEX `idx_tenant_llm_model_type` (`model_type`),
  INDEX `idx_tenant_llm_llm_name` (`llm_name`),
  INDEX `idx_tenant_llm_api_key` (`api_key`(255)),
  INDEX `idx_tenant_llm_max_tokens` (`max_tokens`),
  INDEX `idx_tenant_llm_used_tokens` (`used_tokens`),
  INDEX `idx_tenant_llm_create_time` (`create_time`),
  INDEX `idx_tenant_llm_create_date` (`create_date`),
  INDEX `idx_tenant_llm_update_time` (`update_time`),
  INDEX `idx_tenant_llm_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 知识库表
CREATE TABLE IF NOT EXISTS `knowledgebase` (
  `id` VARCHAR(32) NOT NULL,
  `avatar` TEXT DEFAULT NULL,
  `tenant_id` VARCHAR(32) NOT NULL,
  `name` VARCHAR(128) NOT NULL,
  `language` VARCHAR(32) DEFAULT 'Chinese',
  `description` TEXT DEFAULT NULL,
  `embd_id` VARCHAR(128) NOT NULL,
  `permission` VARCHAR(16) DEFAULT 'me',
  `created_by` VARCHAR(32) NOT NULL,
  `doc_num` INT DEFAULT 0,
  `token_num` INT DEFAULT 0,
  `chunk_num` INT DEFAULT 0,
  `similarity_threshold` FLOAT DEFAULT 0.2,
  `vector_similarity_weight` FLOAT DEFAULT 0.3,
  `parser_id` VARCHAR(32) DEFAULT 'naive',
  `parser_config` LONGTEXT DEFAULT NULL,
  `pagerank` INT DEFAULT 0,
  `status` VARCHAR(1) DEFAULT '1',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_knowledgebase_tenant_id` (`tenant_id`),
  INDEX `idx_knowledgebase_name` (`name`),
  INDEX `idx_knowledgebase_language` (`language`),
  INDEX `idx_knowledgebase_embd_id` (`embd_id`),
  INDEX `idx_knowledgebase_permission` (`permission`),
  INDEX `idx_knowledgebase_created_by` (`created_by`),
  INDEX `idx_knowledgebase_doc_num` (`doc_num`),
  INDEX `idx_knowledgebase_token_num` (`token_num`),
  INDEX `idx_knowledgebase_chunk_num` (`chunk_num`),
  INDEX `idx_knowledgebase_similarity_threshold` (`similarity_threshold`),
  INDEX `idx_knowledgebase_vector_similarity_weight` (`vector_similarity_weight`),
  INDEX `idx_knowledgebase_parser_id` (`parser_id`),
  INDEX `idx_knowledgebase_status` (`status`),
  INDEX `idx_knowledgebase_create_time` (`create_time`),
  INDEX `idx_knowledgebase_create_date` (`create_date`),
  INDEX `idx_knowledgebase_update_time` (`update_time`),
  INDEX `idx_knowledgebase_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 文档表
CREATE TABLE IF NOT EXISTS `document` (
  `id` VARCHAR(32) NOT NULL,
  `thumbnail` TEXT DEFAULT NULL,
  `kb_id` VARCHAR(256) NOT NULL,
  `parser_id` VARCHAR(32) NOT NULL,
  `parser_config` LONGTEXT DEFAULT NULL,
  `source_type` VARCHAR(128) DEFAULT 'local',
  `source_url` VARCHAR(1024) DEFAULT NULL,
  `last_update_time` DATETIME DEFAULT NULL,
  `update_interval` INT DEFAULT 0,
  `next_update_time` DATETIME DEFAULT NULL,
  `type` VARCHAR(32) NOT NULL,
  `created_by` VARCHAR(32) NOT NULL,
  `name` VARCHAR(255) DEFAULT NULL,
  `location` VARCHAR(255) DEFAULT NULL,
  `size` INT DEFAULT 0,
  `token_num` INT DEFAULT 0,
  `chunk_num` INT DEFAULT 0,
  `progress` FLOAT DEFAULT 0,
  `progress_msg` TEXT DEFAULT '',
  `process_begin_at` DATETIME DEFAULT NULL,
  `process_duation` FLOAT DEFAULT 0,
  `meta_fields` LONGTEXT DEFAULT NULL,
  `run` VARCHAR(1) DEFAULT '0',
  `status` VARCHAR(1) DEFAULT '1',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_document_kb_id` (`kb_id`),
  INDEX `idx_document_parser_id` (`parser_id`),
  INDEX `idx_document_source_type` (`source_type`),
  INDEX `idx_document_source_url` (`source_url`(255)),
  INDEX `idx_document_last_update_time` (`last_update_time`),
  INDEX `idx_document_update_interval` (`update_interval`),
  INDEX `idx_document_next_update_time` (`next_update_time`),
  INDEX `idx_document_type` (`type`),
  INDEX `idx_document_created_by` (`created_by`),
  INDEX `idx_document_name` (`name`),
  INDEX `idx_document_location` (`location`),
  INDEX `idx_document_size` (`size`),
  INDEX `idx_document_token_num` (`token_num`),
  INDEX `idx_document_chunk_num` (`chunk_num`),
  INDEX `idx_document_progress` (`progress`),
  INDEX `idx_document_process_begin_at` (`process_begin_at`),
  INDEX `idx_document_run` (`run`),
  INDEX `idx_document_status` (`status`),
  INDEX `idx_document_create_time` (`create_time`),
  INDEX `idx_document_create_date` (`create_date`),
  INDEX `idx_document_update_time` (`update_time`),
  INDEX `idx_document_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 文件表
CREATE TABLE IF NOT EXISTS `file` (
  `id` VARCHAR(32) NOT NULL,
  `parent_id` VARCHAR(32) NOT NULL,
  `tenant_id` VARCHAR(32) NOT NULL,
  `created_by` VARCHAR(32) NOT NULL,
  `name` VARCHAR(255) NOT NULL,
  `location` VARCHAR(255) DEFAULT NULL,
  `size` INT DEFAULT 0,
  `type` VARCHAR(32) NOT NULL,
  `source_type` VARCHAR(128) DEFAULT '',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_file_parent_id` (`parent_id`),
  INDEX `idx_file_tenant_id` (`tenant_id`),
  INDEX `idx_file_created_by` (`created_by`),
  INDEX `idx_file_name` (`name`),
  INDEX `idx_file_location` (`location`),
  INDEX `idx_file_size` (`size`),
  INDEX `idx_file_type` (`type`),
  INDEX `idx_file_source_type` (`source_type`),
  INDEX `idx_file_create_time` (`create_time`),
  INDEX `idx_file_create_date` (`create_date`),
  INDEX `idx_file_update_time` (`update_time`),
  INDEX `idx_file_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 文件文档关联表
CREATE TABLE IF NOT EXISTS `file2document` (
  `id` VARCHAR(32) NOT NULL,
  `file_id` VARCHAR(32) DEFAULT NULL,
  `document_id` VARCHAR(32) DEFAULT NULL,
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_file2document_file_id` (`file_id`),
  INDEX `idx_file2document_document_id` (`document_id`),
  INDEX `idx_file2document_create_time` (`create_time`),
  INDEX `idx_file2document_create_date` (`create_date`),
  INDEX `idx_file2document_update_time` (`update_time`),
  INDEX `idx_file2document_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 任务表
CREATE TABLE IF NOT EXISTS `task` (
  `id` VARCHAR(32) NOT NULL,
  `doc_id` VARCHAR(32) NOT NULL,
  `from_page` INT DEFAULT 0,
  `to_page` INT DEFAULT 100000000,
  `begin_at` DATETIME DEFAULT NULL,
  `process_duation` FLOAT DEFAULT 0,
  `progress` FLOAT DEFAULT 0,
  `progress_msg` TEXT DEFAULT '',
  `retry_count` INT DEFAULT 0,
  `digest` TEXT DEFAULT '',
  `chunk_ids` LONGTEXT DEFAULT '',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_task_doc_id` (`doc_id`),
  INDEX `idx_task_begin_at` (`begin_at`),
  INDEX `idx_task_progress` (`progress`),
  INDEX `idx_task_create_time` (`create_time`),
  INDEX `idx_task_create_date` (`create_date`),
  INDEX `idx_task_update_time` (`update_time`),
  INDEX `idx_task_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 对话应用表
CREATE TABLE IF NOT EXISTS `dialog` (
  `id` VARCHAR(32) NOT NULL,
  `tenant_id` VARCHAR(32) NOT NULL,
  `name` VARCHAR(255) DEFAULT NULL,
  `description` TEXT DEFAULT NULL,
  `icon` TEXT DEFAULT NULL,
  `language` VARCHAR(32) DEFAULT 'Chinese',
  `llm_id` VARCHAR(128) NOT NULL,
  `llm_setting` LONGTEXT DEFAULT NULL,
  `prompt_type` VARCHAR(16) DEFAULT 'simple',
  `prompt_config` LONGTEXT DEFAULT NULL,
  `similarity_threshold` FLOAT DEFAULT 0.2,
  `vector_similarity_weight` FLOAT DEFAULT 0.3,
  `top_n` INT DEFAULT 6,
  `top_k` INT DEFAULT 1024,
  `do_refer` VARCHAR(1) DEFAULT '1',
  `rerank_id` VARCHAR(128) NOT NULL,
  `kb_ids` LONGTEXT DEFAULT NULL,
  `status` VARCHAR(1) DEFAULT '1',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_dialog_tenant_id` (`tenant_id`),
  INDEX `idx_dialog_name` (`name`),
  INDEX `idx_dialog_language` (`language`),
  INDEX `idx_dialog_prompt_type` (`prompt_type`),
  INDEX `idx_dialog_top_n` (`top_n`),
  INDEX `idx_dialog_top_k` (`top_k`),
  INDEX `idx_dialog_do_refer` (`do_refer`),
  INDEX `idx_dialog_status` (`status`),
  INDEX `idx_dialog_create_time` (`create_time`),
  INDEX `idx_dialog_create_date` (`create_date`),
  INDEX `idx_dialog_update_time` (`update_time`),
  INDEX `idx_dialog_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 会话表
CREATE TABLE IF NOT EXISTS `conversation` (
  `id` VARCHAR(32) NOT NULL,
  `dialog_id` VARCHAR(32) NOT NULL,
  `name` VARCHAR(255) DEFAULT NULL,
  `message` LONGTEXT DEFAULT NULL,
  `reference` LONGTEXT DEFAULT NULL,
  `user_id` VARCHAR(255) DEFAULT NULL,
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_conversation_dialog_id` (`dialog_id`),
  INDEX `idx_conversation_name` (`name`),
  INDEX `idx_conversation_user_id` (`user_id`),
  INDEX `idx_conversation_create_time` (`create_time`),
  INDEX `idx_conversation_create_date` (`create_date`),
  INDEX `idx_conversation_update_time` (`update_time`),
  INDEX `idx_conversation_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- API令牌表
CREATE TABLE IF NOT EXISTS `api_token` (
  `tenant_id` VARCHAR(32) NOT NULL,
  `token` VARCHAR(255) NOT NULL,
  `dialog_id` VARCHAR(32) NOT NULL,
  `source` VARCHAR(16) DEFAULT NULL,
  `beta` VARCHAR(255) DEFAULT NULL,
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`tenant_id`, `token`),
  INDEX `idx_api_token_tenant_id` (`tenant_id`),
  INDEX `idx_api_token_token` (`token`),
  INDEX `idx_api_token_dialog_id` (`dialog_id`),
  INDEX `idx_api_token_source` (`source`),
  INDEX `idx_api_token_beta` (`beta`),
  INDEX `idx_api_token_create_time` (`create_time`),
  INDEX `idx_api_token_create_date` (`create_date`),
  INDEX `idx_api_token_update_time` (`update_time`),
  INDEX `idx_api_token_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- API会话表
CREATE TABLE IF NOT EXISTS `api_4_conversation` (
  `id` VARCHAR(32) NOT NULL,
  `dialog_id` VARCHAR(32) NOT NULL,
  `user_id` VARCHAR(255) NOT NULL,
  `message` LONGTEXT DEFAULT NULL,
  `reference` LONGTEXT DEFAULT NULL,
  `tokens` INT DEFAULT 0,
  `source` VARCHAR(16) DEFAULT NULL,
  `dsl` LONGTEXT DEFAULT NULL,
  `duration` FLOAT DEFAULT 0,
  `round` INT DEFAULT 0,
  `thumb_up` INT DEFAULT 0,
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_api_4_conversation_dialog_id` (`dialog_id`),
  INDEX `idx_api_4_conversation_user_id` (`user_id`),
  INDEX `idx_api_4_conversation_tokens` (`tokens`),
  INDEX `idx_api_4_conversation_source` (`source`),
  INDEX `idx_api_4_conversation_duration` (`duration`),
  INDEX `idx_api_4_conversation_round` (`round`),
  INDEX `idx_api_4_conversation_thumb_up` (`thumb_up`),
  INDEX `idx_api_4_conversation_create_time` (`create_time`),
  INDEX `idx_api_4_conversation_create_date` (`create_date`),
  INDEX `idx_api_4_conversation_update_time` (`update_time`),
  INDEX `idx_api_4_conversation_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户画布表
CREATE TABLE IF NOT EXISTS `user_canvas` (
  `id` VARCHAR(32) NOT NULL,
  `avatar` TEXT DEFAULT NULL,
  `user_id` VARCHAR(255) NOT NULL,
  `title` VARCHAR(255) DEFAULT NULL,
  `description` TEXT DEFAULT NULL,
  `canvas_type` VARCHAR(32) DEFAULT NULL,
  `dsl` LONGTEXT DEFAULT NULL,
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_user_canvas_user_id` (`user_id`),
  INDEX `idx_user_canvas_title` (`title`),
  INDEX `idx_user_canvas_canvas_type` (`canvas_type`),
  INDEX `idx_user_canvas_create_time` (`create_time`),
  INDEX `idx_user_canvas_create_date` (`create_date`),
  INDEX `idx_user_canvas_update_time` (`update_time`),
  INDEX `idx_user_canvas_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 画布模板表
CREATE TABLE IF NOT EXISTS `canvas_template` (
  `id` VARCHAR(32) NOT NULL,
  `avatar` TEXT DEFAULT NULL,
  `title` VARCHAR(255) DEFAULT NULL,
  `description` TEXT DEFAULT NULL,
  `canvas_type` VARCHAR(32) DEFAULT NULL,
  `dsl` LONGTEXT DEFAULT NULL,
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_canvas_template_title` (`title`),
  INDEX `idx_canvas_template_canvas_type` (`canvas_type`),
  INDEX `idx_canvas_template_create_time` (`create_time`),
  INDEX `idx_canvas_template_create_date` (`create_date`),
  INDEX `idx_canvas_template_update_time` (`update_time`),
  INDEX `idx_canvas_template_update_date` (`update_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 团队申请记录表
CREATE TABLE IF NOT EXISTS `team_application` (
  `id` VARCHAR(32) NOT NULL,
  `user_id` VARCHAR(32) NOT NULL COMMENT '申请人ID',
  `tenant_id` VARCHAR(32) NOT NULL COMMENT '申请加入的团队ID',
  `status` VARCHAR(20) NOT NULL DEFAULT 'PENDING' COMMENT '申请状态：PENDING-待审批，APPROVED-已同意，REJECTED-已拒绝',
  `message` TEXT DEFAULT NULL COMMENT '申请留言',
  `processed_by` VARCHAR(32) DEFAULT NULL COMMENT '处理人ID',
  `processed_time` DATETIME DEFAULT NULL COMMENT '处理时间',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_team_application_user_id` (`user_id`),
  INDEX `idx_team_application_tenant_id` (`tenant_id`),
  INDEX `idx_team_application_create_time` (`create_time`),
  INDEX `idx_team_application_update_time` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='团队申请记录表';

-- ==================== RAG评估系统相关表 ====================

-- 评估数据集表
CREATE TABLE IF NOT EXISTS `evaluation_dataset` (
  `id` VARCHAR(32) NOT NULL,
  `name` VARCHAR(128) NOT NULL COMMENT '数据集名称',
  `description` TEXT DEFAULT NULL COMMENT '数据集描述',
  `kb_id` VARCHAR(32) NOT NULL COMMENT '关联的知识库ID',
  `tenant_id` VARCHAR(32) NOT NULL COMMENT '租户ID',
  `created_by` VARCHAR(32) NOT NULL COMMENT '创建者ID',
  `dataset_type` VARCHAR(32) DEFAULT 'qa_pairs' COMMENT '数据集类型：qa_pairs, questions_only',
  `status` VARCHAR(16) DEFAULT 'active' COMMENT '状态：active, archived',
  `total_samples` INT DEFAULT 0 COMMENT '样本总数',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_evaluation_dataset_tenant_kb` (`tenant_id`, `kb_id`),
  INDEX `idx_evaluation_dataset_created_by` (`created_by`),
  INDEX `idx_evaluation_dataset_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评估数据集表';

-- 评估样本表
CREATE TABLE IF NOT EXISTS `evaluation_sample` (
  `id` VARCHAR(32) NOT NULL,
  `dataset_id` VARCHAR(32) NOT NULL COMMENT '数据集ID',
  `question` TEXT NOT NULL COMMENT '问题',
  `ground_truth` TEXT DEFAULT NULL COMMENT '标准答案',
  `contexts` TEXT DEFAULT NULL COMMENT '相关文档片段(JSON格式)',
  `metadata` TEXT DEFAULT NULL COMMENT '额外信息(JSON格式)',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_evaluation_sample_dataset_id` (`dataset_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评估样本表';

-- 评估任务表
CREATE TABLE IF NOT EXISTS `evaluation_task` (
  `id` VARCHAR(32) NOT NULL,
  `name` VARCHAR(128) NOT NULL COMMENT '任务名称',
  `dataset_id` VARCHAR(32) NOT NULL COMMENT '数据集ID',
  `kb_id` VARCHAR(32) NOT NULL COMMENT '知识库ID',
  `tenant_id` VARCHAR(32) NOT NULL COMMENT '租户ID',
  `created_by` VARCHAR(32) NOT NULL COMMENT '创建者ID',
  `metrics` TEXT NOT NULL COMMENT '评估指标(JSON格式)',
  `retrieval_config` TEXT DEFAULT NULL COMMENT '检索配置(JSON格式)',
  `llm_config` TEXT DEFAULT NULL COMMENT 'LLM配置(JSON格式)',
  `status` VARCHAR(16) DEFAULT 'pending' COMMENT '任务状态：pending, running, completed, failed',
  `progress` INT DEFAULT 0 COMMENT '进度百分比',
  `total_samples` INT DEFAULT 0 COMMENT '总样本数',
  `processed_samples` INT DEFAULT 0 COMMENT '已处理样本数',
  `started_at` DATETIME DEFAULT NULL COMMENT '开始时间',
  `completed_at` DATETIME DEFAULT NULL COMMENT '完成时间',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_evaluation_task_tenant_kb` (`tenant_id`, `kb_id`),
  INDEX `idx_evaluation_task_dataset_id` (`dataset_id`),
  INDEX `idx_evaluation_task_status` (`status`),
  INDEX `idx_evaluation_task_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评估任务表';

-- 评估结果表
CREATE TABLE IF NOT EXISTS `evaluation_result` (
  `id` VARCHAR(32) NOT NULL,
  `task_id` VARCHAR(32) NOT NULL COMMENT '任务ID',
  `sample_id` VARCHAR(32) NOT NULL COMMENT '样本ID',
  `retrieved_contexts` TEXT DEFAULT NULL COMMENT '检索到的文档(JSON格式)',
  `generated_answer` TEXT DEFAULT NULL COMMENT '生成的答案',
  `faithfulness` FLOAT DEFAULT NULL COMMENT '忠实度指标',
  `answer_relevancy` FLOAT DEFAULT NULL COMMENT '答案相关性指标',
  `context_precision` FLOAT DEFAULT NULL COMMENT '上下文精确度指标',
  `context_recall` FLOAT DEFAULT NULL COMMENT '上下文召回率指标',
  `context_relevancy` FLOAT DEFAULT NULL COMMENT '上下文相关性指标',
  `answer_correctness` FLOAT DEFAULT NULL COMMENT '答案正确性指标',
  `answer_similarity` FLOAT DEFAULT NULL COMMENT '答案相似性指标',
  `execution_time` FLOAT DEFAULT NULL COMMENT '执行时间(秒)',
  `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_evaluation_result_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评估结果表';

-- 评估报告表
CREATE TABLE IF NOT EXISTS `evaluation_report` (
  `id` VARCHAR(32) NOT NULL,
  `task_id` VARCHAR(32) NOT NULL COMMENT '任务ID',
  `avg_faithfulness` FLOAT DEFAULT NULL COMMENT '平均忠实度',
  `avg_answer_relevancy` FLOAT DEFAULT NULL COMMENT '平均答案相关性',
  `avg_context_precision` FLOAT DEFAULT NULL COMMENT '平均上下文精确度',
  `avg_context_recall` FLOAT DEFAULT NULL COMMENT '平均上下文召回率',
  `avg_context_relevancy` FLOAT DEFAULT NULL COMMENT '平均上下文相关性',
  `avg_answer_correctness` FLOAT DEFAULT NULL COMMENT '平均答案正确性',
  `avg_answer_similarity` FLOAT DEFAULT NULL COMMENT '平均答案相似性',
  `total_samples` INT DEFAULT 0 COMMENT '总样本数',
  `successful_samples` INT DEFAULT 0 COMMENT '成功样本数',
  `failed_samples` INT DEFAULT 0 COMMENT '失败样本数',
  `avg_execution_time` FLOAT DEFAULT NULL COMMENT '平均执行时间',
  `detailed_report` TEXT DEFAULT NULL COMMENT '详细报告(JSON格式)',
  `create_time` BIGINT DEFAULT NULL,
  `create_date` DATETIME DEFAULT NULL,
  `update_time` BIGINT DEFAULT NULL,
  `update_date` DATETIME DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_evaluation_report_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评估报告表'; 