#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Flask-SQLAlchemy与Zebra代理集成示例
用于演示如何将应用连接到公司内部MySQL组件（通过Zebra代理）
"""

from __future__ import absolute_import
import argparse
import datetime
import logging
import os
import uuid
import pymysql
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from pycat import Cat
from zebraproxyclient.api.sqlalchemy import create_engine as zebra_create_engine
from zebraproxyclient.config import ZebraConfig
from sqlalchemy import inspect


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 使用PyMySQL作为MySQL驱动
pymysql.install_as_MySQLdb()

# 初始化Flask应用
app = Flask(__name__)
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy()


# Hack Flask-SQLAlchemy的引擎创建机制，替换为Zebra引擎
def hack_flask_sqlalchemy(db, zebra_config):
    """
    替换Flask-SQLAlchemy的引擎创建逻辑，使用Zebra创建引擎
    
    Args:
        db: Flask-SQLAlchemy实例
        zebra_config: Zebra配置对象
    """
    logger.info("正在进行Flask-SQLAlchemy引擎替换")
    
    def _make_engine(bind_key, options, app):
        # 移除不需要的参数
        options.pop("url", None)
        options.setdefault("connect_args", {}).pop("check_same_thread", None)
        # 使用zebra创建引擎
        return zebra_create_engine(zebra_config, **options)

    def _create_engine(sa_url, engine_opts):
        # 移除不需要的参数
        engine_opts.setdefault("connect_args", {}).pop("check_same_thread", None)
        # 使用zebra创建引擎
        return zebra_create_engine(zebra_config, **engine_opts)
    
    # 替换Flask-SQLAlchemy的引擎创建方法
    if hasattr(db, "_make_engine"):
        logger.info("替换 _make_engine 方法")
        setattr(db, "_make_engine", _make_engine)
    if hasattr(db, "create_engine"):
        logger.info("替换 create_engine 方法")
        setattr(db, "create_engine", _create_engine)


# 定义模型
class User(db.Model):
    """用户模型，对应数据库中的user表"""
    __tablename__ = 'user'
    
    id = db.Column(db.String(32), primary_key=True, comment='用户ID')
    email = db.Column(db.String(255), unique=True, nullable=False, comment='用户邮箱')
    name = db.Column(db.String(255), nullable=False, comment='用户名称')
    password = db.Column(db.String(255), nullable=False, comment='密码（加密存储）')
    status = db.Column(db.Integer, default=1, comment='用户状态：0-禁用，1-启用')
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')
    
    def __repr__(self):
        return f"<User {self.email}>"


class Document(db.Model):
    """文档模型，对应数据库中的document表"""
    __tablename__ = 'document'
    
    id = db.Column(db.String(32), primary_key=True, comment='文档ID')
    title = db.Column(db.String(255), nullable=False, comment='文档标题')
    content = db.Column(db.Text, nullable=True, comment='文档内容')
    doc_type = db.Column(db.String(50), default='text', comment='文档类型')
    knowledge_base_id = db.Column(db.String(32), nullable=False, comment='所属知识库ID')
    created_at = db.Column(db.DateTime, default=datetime.datetime.now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now, comment='更新时间')
    
    def __repr__(self):
        return f"<Document {self.title}>"


class UserDAO:
    """用户数据访问对象，封装与用户表相关的操作"""
    
    @staticmethod
    def create_user(email, name, password):
        """创建用户"""
        with app.app_context():
            try:
                user = User(
                    id=str(uuid.uuid4()).replace('-', ''),
                    email=email,
                    name=name,
                    password=password
                )
                db.session.add(user)
                db.session.commit()
                return user
            except Exception as e:
                db.session.rollback()
                logger.error(f"创建用户失败: {e}")
                raise
    
    @staticmethod
    def get_user_by_email(email):
        """通过邮箱获取用户"""
        with app.app_context():
            return User.query.filter_by(email=email).first()
    
    @staticmethod
    def get_user_by_id(user_id):
        """通过ID获取用户"""
        with app.app_context():
            return User.query.get(user_id)
    
    @staticmethod
    def update_user(user_id, data):
        """更新用户信息"""
        with app.app_context():
            try:
                user = User.query.get(user_id)
                if not user:
                    return None
                
                for key, value in data.items():
                    if hasattr(user, key):
                        setattr(user, key, value)
                
                db.session.commit()
                return user
            except Exception as e:
                db.session.rollback()
                logger.error(f"更新用户失败: {e}")
                raise
    
    @staticmethod
    def delete_user(user_id):
        """删除用户"""
        with app.app_context():
            try:
                user = User.query.get(user_id)
                if not user:
                    return False
                
                db.session.delete(user)
                db.session.commit()
                return True
            except Exception as e:
                db.session.rollback()
                logger.error(f"删除用户失败: {e}")
                raise


def test_database():
    """测试数据库连接和基本操作"""
    logger.info("开始测试数据库连接...")
    
    with app.app_context():
        try:
            # 测试数据库事务操作
            try:
                # 开始事务
                test_email = f"test_{uuid.uuid4().hex[:8]}@example.com"
                test_user = UserDAO.create_user(
                    email=test_email,
                    name="测试用户",
                    password="test_password"
                )
                
                # 验证创建是否成功
                retrieved_user = UserDAO.get_user_by_email(test_email)
                assert retrieved_user is not None, "未能获取创建的测试用户"
                
                # 更新用户
                UserDAO.update_user(retrieved_user.id, {"name": "更新后的测试用户"})
                
                # 验证更新是否成功
                updated_user = UserDAO.get_user_by_id(retrieved_user.id)
                assert updated_user.name == "更新后的测试用户", "用户更新失败"
                
                # 删除测试用户
                UserDAO.delete_user(retrieved_user.id)
                
                # 验证删除是否成功
                deleted_user = UserDAO.get_user_by_id(retrieved_user.id)
                assert deleted_user is None, "用户删除失败"
                
                logger.info("事务测试完成，数据库操作正常")
                return True
                
            except Exception as e:
                db.session.rollback()
                logger.error(f"数据库事务测试失败: {e}")
                return False
                
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False


@app.route('/test-connection')
def test_connection():
    """测试数据库连接的Web接口"""
    try:
        result = test_database()
        if result:
            return "数据库连接测试成功，所有操作正常！"
        else:
            return "数据库连接测试失败，请检查日志获取详细信息。", 500
    except Exception as e:
        return f"发生错误: {str(e)}", 500


def main():
    """主函数，解析参数并启动应用"""
    # 解析命令行参数

    parser = argparse.ArgumentParser(description='Zebra代理Flask-SQLAlchemy示例')
    parser.add_argument('--host', default='127.0.0.1', help='应用监听地址')
    parser.add_argument('--port', type=int, default=5000, help='应用监听端口')
    parser.add_argument('--debug', action='store_true', help='是否启用调试模式')
    args = parser.parse_args()
    args.app_key = "com.sankuai.shangou.ai.sgrag"
    args.ref_key = "sgai_rag_flow_test"

    # 初始化Cat服务监控
    try:
        Cat.init_cat("shangou-ai-rag")  # 替换为你的应用名称
        logger.info("Cat监控服务初始化成功")
    except Exception as e:
        logger.warning(f"Cat监控服务初始化失败: {e}")
    
    # 设置数据库URI（仅作为占位符，会被Zebra引擎替换）
    app.config['SQLALCHEMY_DATABASE_URI'] = f'mysql+mysqldb:///{args.ref_key}'
    
    # 创建Zebra配置
    zebra_config = ZebraConfig(
        appname=args.app_key,  # Zebra应用Key
        ref_key=args.ref_key    # 数据库引用Key
    )
    
    # 先进行hack操作
    hack_flask_sqlalchemy(db, zebra_config)
    
    # 然后初始化应用
    db.init_app(app)
    
    # 测试数据库连接
    with app.app_context():
        test_database()
    
    # 启动应用
    logger.info(f"启动Web服务器在 {args.host}:{args.port}")
    app.run(host=args.host, port=args.port, debug=args.debug)


if __name__ == '__main__':
    main() 