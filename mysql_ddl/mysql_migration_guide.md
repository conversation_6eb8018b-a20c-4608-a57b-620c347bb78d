# MySQL从Docker部署迁移到公司内部组件指南

## 背景

本项目需要将MySQL从Docker容器部署迁移至公司内部MySQL组件。特殊情况是公司内部数据库不支持DDL操作（如CREATE TABLE、ALTER TABLE等），只支持DQL（数据查询）和DML（数据操作）操作。

## 迁移步骤

### 1. 准备工作

#### 1.1 备份当前数据

```bash
# 从当前Docker MySQL容器中导出所有数据
docker exec ragflow-mysql mysqldump -u root -p'infini_rag_flow' rag_flow > rag_flow_backup.sql
```

#### 1.2 获取公司内部MySQL信息

从公司数据库管理员处获取以下信息：
- 数据库连接地址和端口（或Zebra引用键）
- 访问凭证（用户名和密码）
- 分配的数据库名称
- 网络访问限制信息
- 若使用Zebra代理，需获取app_key和ref_key

### 2. 创建数据库表结构

由于公司内部MySQL不支持DDL操作，需要通过DBA或有权限的管理员预先创建表结构：

1. 将生成的完整DDL脚本（`create_tables.sql`）提交给公司DBA
2. DBA执行DDL脚本创建数据库及所有表结构
3. 确认所有表格已正确创建

**注意事项**：
- 确保字符集为`utf8mb4`，排序规则为`utf8mb4_unicode_ci`
- 确保所有索引正确创建
- 表名和字段名务必与脚本完全一致

### 3. 数据迁移

在表结构创建完成后，进行数据导入：

```bash
# 编辑备份SQL文件，移除所有DDL语句（CREATE TABLE、ALTER TABLE等）
# 可以用以下命令过滤出仅包含INSERT语句的SQL
grep -i "^INSERT" rag_flow_backup.sql > rag_flow_data_only.sql

# 向公司内部MySQL导入数据
mysql -h <公司MySQL地址> -P <公司MySQL端口> -u <用户名> -p<密码> <数据库名> < rag_flow_data_only.sql
```

如果无法直接连接公司内部数据库，可请DBA协助导入数据。

### 4. 应用配置修改

#### 4.1 更新配置文件

编辑`conf/service_conf.yaml`文件，修改MySQL连接配置：

**直接连接方式**:
```yaml
mysql:
  name: '公司分配的数据库名'  # 如 'rag_flow'
  user: '公司MySQL用户名'
  password: '公司MySQL密码'
  host: '公司MySQL服务地址'
  port: 公司MySQL端口号
  max_connections: 100
  stale_timeout: 30
```

**使用Zebra代理方式**:
```yaml
mysql:
  zebra_app_key: 'your_app_key'  # 公司分配的Zebra应用Key
  zebra_ref_key: 'your_ref_key'  # 公司分配的数据库引用Key
  max_connections: 100
  stale_timeout: 30
```

#### 4.2 修改Docker配置

编辑`docker/docker-compose-base.yml`，注释或移除MySQL服务相关配置：

```yaml
# 注释或删除以下部分
# mysql:
#   image: mysql:8.0.39
#   container_name: ragflow-mysql
#   ...

# 同样注释volumes中的mysql_data
# volumes:
#   ...
#   mysql_data:
#     driver: local
#   ...
```

### 5. 代码适配修改

由于公司内部数据库不支持DDL，需要修改代码中涉及自动创建表的部分：

#### 5.1 修改数据库初始化代码

编辑`api/db/db_utils.py`，移除自动创建表的部分：

```python
@DB.connection_context()
def bulk_insert_into_db(model, data_source, replace_on_conflict=False):
    # 注释或删除下面这行
    # DB.create_tables([model])

    for i, data in enumerate(data_source):
        # ... 其余代码保持不变
```

#### 5.2 修改数据库表初始化函数

编辑`api/db/db_models.py`，修改init_database_tables函数：

```python
@DB.connection_context()
def init_database_tables(alter_fields=[]):
    # 注释掉表创建相关代码，仅保留日志记录
    members = inspect.getmembers(sys.modules[__name__], inspect.isclass)
    table_objs = []
    for name, obj in members:
        if obj != DataBaseModel and issubclass(obj, DataBaseModel):
            table_objs.append(obj)
            logging.debug(f"table already exists: {obj.__name__}")
    
    # 如果有migrate_db函数调用，也需要修改或移除
    # migrate_db()
```

#### 5.3 使用Zebra代理客户端（推荐方案）

如果使用公司的Zebra代理客户端，需要进行以下修改：

1. 安装必要的依赖：

```bash
pip install pycat zebraproxyclient pymysql flask-sqlalchemy
```

2. 修改数据库连接代码，使用Zebra代理替换直接连接：

```python
from __future__ import absolute_import
import pymysql
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from pycat import Cat
from zebraproxyclient.api.sqlalchemy import create_engine as zebra_create_engine
from zebraproxyclient.config import ZebraConfig

# 使用PyMySQL作为MySQL驱动
pymysql.install_as_MySQLdb()

# 初始化Cat服务监控
Cat.init_cat("shangou-ai-rag")  # 替换为你的应用名称

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+mysqldb:///{your_ref_key}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy()

# Hack Flask-SQLAlchemy的引擎创建机制，替换为Zebra引擎
def hack_flask_sqlalchemy(db, zebra_config):
    def _make_engine(bind_key, options, app):
        options.pop("url", None)
        options.setdefault("connect_args", {}).pop("check_same_thread", None)
        return zebra_create_engine(zebra_config, **options)

    def _create_engine(sa_url, engine_opts):
        engine_opts.setdefault("connect_args", {}).pop("check_same_thread", None)
        return zebra_create_engine(zebra_config, **engine_opts)
    
    if hasattr(db, "_make_engine"):
        setattr(db, "_make_engine", _make_engine)
    if hasattr(db, "create_engine"):
        setattr(db, "create_engine", _create_engine)

# 创建Zebra配置
zebra_config = ZebraConfig(
    appname="your_app_key",  # 替换为公司分配的Zebra应用Key
    ref_key="your_ref_key"   # 替换为公司分配的数据库引用Key
)

# Hack Flask-SQLAlchemy
hack_flask_sqlalchemy(db, zebra_config)

# 初始化应用（必须在hack之后）
db.init_app(app)
```

3. 然后定义你的模型并使用，例如：

```python
class User(db.Model):
    __tablename__ = 'user'
    id = db.Column(db.String(32), primary_key=True)
    # ... 其他字段定义
```

### 6. 验证和测试

#### 6.1 连接测试

根据选择的连接方式使用相应的测试脚本验证连接：

**直接连接方式**:
```bash
python test_db_connection.py --host <公司MySQL地址> --port <端口> --user <用户名> --password <密码> --database <数据库名>
```

**Zebra代理方式**:
```bash
python flask_sqlalchemy_zebra_example.py --app-key <Zebra应用Key> --ref-key <数据库引用Key>
```

#### 6.2 功能测试

执行关键业务流程测试，确保所有依赖数据库的功能正常工作。特别关注：
- 用户登录注册
- 文档创建和检索
- 知识库操作
- 对话功能

### 7. 迁移后的维护

#### 7.1 架构变更处理

由于公司内部数据库不支持DDL，今后任何表结构变更需要：
1. 生成DDL脚本（ALTER TABLE语句）
2. 提交给DBA执行
3. 更新代码中的模型定义

#### 7.2 性能监控

监控应用与数据库的连接情况及查询性能：
- 记录慢查询
- 监控连接池状态
- 定期检查索引使用情况
- 使用Cat服务监控数据库调用（如果使用Zebra）

## 备选方案

### 方案1：使用Flask-SQLAlchemy结合Zebra代理（推荐）

参考`flask_sqlalchemy_zebra_example.py`示例，使用公司的Zebra代理服务：

```python
# 重点代码参见5.3节
```

### 方案2：使用SQLAlchemy连接公司内部MySQL

使用纯SQLAlchemy（不依赖Flask）：

```python
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.ext.declarative import declarative_base

db_config = {
    'user': '公司MySQL用户名',
    'password': '公司MySQL密码',
    'host': '公司MySQL服务地址',
    'port': '公司MySQL端口',
    'database': '数据库名'
}

engine = create_engine(
    f"mysql+pymysql://{db_config['user']}:{db_config['password']}@"
    f"{db_config['host']}:{db_config['port']}/{db_config['database']}",
    pool_size=100,
    pool_timeout=30,
    pool_recycle=3600
)

session_factory = sessionmaker(bind=engine)
Session = scoped_session(session_factory)
Base = declarative_base()
```

### 方案3：使用DBUtils连接公司内部MySQL

使用DBUtils管理连接池：

```python
from dbutils.pooled_db import PooledDB
import pymysql

db_config = {
    'user': '公司MySQL用户名',
    'password': '公司MySQL密码',
    'host': '公司MySQL服务地址',
    'port': 公司MySQL端口,
    'database': '数据库名'
}

pool = PooledDB(
    creator=pymysql,
    maxconnections=100,
    mincached=5,
    maxcached=20,
    blocking=True,
    host=db_config['host'],
    port=db_config['port'],
    user=db_config['user'],
    password=db_config['password'],
    database=db_config['database'],
    charset='utf8mb4'
)

# 获取连接示例
conn = pool.connection()
cursor = conn.cursor()
# 使用完毕后
cursor.close()
conn.close()
```

## 问题排查

如遇到连接问题，请检查：

1. 网络连通性：确保应用服务器可以访问公司内部MySQL或Zebra代理服务
2. 防火墙设置：检查是否有端口限制
3. 权限问题：确认用户拥有正确的数据库权限
4. 连接池配置：检查连接池大小是否合适
5. SQL兼容性：确认应用生成的SQL与公司MySQL版本兼容
6. Zebra配置：确认app_key和ref_key是否正确配置（如果使用Zebra）

## 联系支持

如遇问题请联系：
- 项目技术负责人：[填写姓名和联系方式]
- 公司DBA：[填写姓名和联系方式]
- MySQL组件团队：[填写姓名和联系方式]
- Zebra服务团队：[填写姓名和联系方式] 